import logging
import os
import glob
import re
import pandas as pd
from arcticdb import Arctic, QueryBuilder
from multiprocessing import Pool
import datetime


library = "nse/1_min/snap_file_may20_jan21/trd_ord"
sampled_location = f"{library.split('/')[2]}_sampled"
if not os.path.exists(f"/home/<USER>/repos/data_auditing/{sampled_location}"):
    os.makedirs(f"/home/<USER>/repos/data_auditing/{sampled_location}")
    
storet = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")


def process_oi_snap_symbol(sym):
    print(f"Started symbol: {sym}")

    store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")

    lib = store[library]

    df = lib.read(sym).data

    # backing up for temporary basis
    df.to_parquet(
        f"/home/<USER>/repos/data_auditing/{backup_location}/{sym}.parquet"
    )

    df = df.drop_duplicates(keep="last").set_index("timestamp").sort_index()
    groupby_cols = ["symbol", "expiry"]
    if "option_type" in df.columns:
        groupby_cols.append("option_type")
    if "strike_price" in df.columns:
        groupby_cols.append("strike_price")

    df = (
        df.groupby(groupby_cols)
        .resample("1min", label="right", closed="right")
        .agg(
            {
                "oi": "last",
            }
        )
        .dropna()
        .reset_index()
        .set_index("timestamp")
        .sort_index()
    )

    lib.write(sym, df)

    # deleting backup as write successful
    os.remove(f"/home/<USER>/repos/data_auditing/{backup_location}/{sym}.parquet")

    print(f"Completed symbol: {sym}")


def combine_index_rem_data(symbol):
    print(f"Started combining {symbol}...")
    df_list = []

    store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")

    lib = store[library]

    syms = [sym for sym in lib.list_symbols() if re.fullmatch(rf"^{symbol}\d+$", sym)]

    for sym in syms:
        df_list.append(lib.read(sym).data)

    df_base = lib.read(sym).data
    df = pd.concat([df_base] + df_list).sort_index().reset_index()
    df = df.drop_duplicates().set_index("timestamp").sort_index()

    lib.write(symbol, df)

    for s in syms:
        lib.delete(s)

    print(f"Completed combining {symbol}")


def process_snap_file(sym):
    try:
        if len(glob.glob(f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_*.parquet")):
            return

        store = Arctic(
            "s3://*************:9000:arctic-db?access=super&secret=doopersecret"
        )

        print(f"Started symbol: {sym}")
        lib = store[library]

        tot_len = lib.get_description(sym).row_count
        print(F"Total length for {sym} is {tot_len}")
        chunk_size = int(1e5)

        start = 0
        count = 0

        while start < tot_len:
            end = min(start + chunk_size, tot_len)

            print(f"Processing rows {start} to {end-1} of {tot_len}")

            df_chunk = lib.read(sym, row_range=[start, end]).data
            df_chunk = (
                df_chunk.drop_duplicates(keep="last")
                .set_index("timestamp")
                .sort_index()
            )

            df_fut = df_chunk[df_chunk.strike_price.isna()].drop(
                columns=["strike_price", "option_type"]
            )
            df_opt = df_chunk[~df_chunk.strike_price.isna()]

            df_fut_trd = df_fut[df_fut.close != 0].drop(columns=["ord_price"])
            df_fut_ord = df_fut[df_fut.ord_price != 0].drop(columns=["close", "volume"])
            df_opt_trd = df_opt[df_opt.close != 0].drop(columns=["ord_price"])
            df_opt_ord = df_opt[df_opt.ord_price != 0].drop(columns=["close", "volume"])

            df_fut_trd = (
                df_fut_trd.groupby(["symbol", "expiry"])
                .resample("1min", label="right", closed="right")
                .agg({"close": ["first", "max", "min", "last"], "volume": "sum"})
                .dropna()
            )
            df_fut_trd.columns = [
                "_".join(col).strip() for col in df_fut_trd.columns.values
            ]
            df_fut_trd = df_fut_trd.rename(
                columns={
                    "close_first": "open",
                    "close_max": "high",
                    "close_min": "low",
                    "close_last": "close",
                    "volume_sum": "volume",
                }
            )
            df_fut_trd = df_fut_trd.reset_index().set_index("timestamp").sort_index()
            df_fut_trd.to_parquet(
                f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_fut_trd_{count}.parquet"
            )
            df_fut_trd = None

            df_fut_ord = (
                df_fut_ord.groupby(["symbol", "expiry"])
                .resample("1min", label="right", closed="right")
                .agg(
                    {"ord_price": ["first", "max", "min", "last"]}
                )
                .dropna()
            )
            df_fut_ord.columns = [
                "_".join(col).strip() for col in df_fut_ord.columns.values
            ]
            df_fut_ord = df_fut_ord.rename(
                columns={
                    "ord_price_first": "open",
                    "ord_price_max": "high",
                    "ord_price_min": "low",
                    "ord_price_last": "close",
                }
            )
            df_fut_ord = df_fut_ord.reset_index().set_index("timestamp").sort_index()
            df_fut_ord.to_parquet(
                f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_fut_ord_{count}.parquet"
            )
            df_fut_ord = None

            df_opt_trd = (
                df_opt_trd.groupby(["symbol", "expiry", "strike_price", "option_type"])
                .resample("1min", label="right", closed="right")
                .agg({"close": ["first", "max", "min", "last"]})
                .dropna()
            )
            df_opt_trd.columns = [
                "_".join(col).strip() for col in df_opt_trd.columns.values
            ]
            df_opt_trd = df_opt_trd.rename(
                columns={
                    "close_first": "open",
                    "close_max": "high",
                    "close_min": "low",
                    "close_last": "close",
                    "volume_sum": "volume",
                }
            )
            df_opt_trd = df_opt_trd.reset_index().set_index("timestamp").sort_index()
            df_opt_trd.to_parquet(
                f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_opt_trd_{count}.parquet"
            )
            df_opt_trd = None

            df_opt_ord = (
                df_opt_ord.groupby(["symbol", "expiry", "strike_price", "option_type"])
                .resample("1min", label="right", closed="right")
                .agg(
                    {"ord_price": ["first", "max", "min", "last"]}
                )
                .dropna()
            )
            df_opt_ord.columns = [
                "_".join(col).strip() for col in df_opt_ord.columns.values
            ]
            df_opt_ord = df_opt_ord.rename(
                columns={
                    "ord_price_first": "open",
                    "ord_price_max": "high",
                    "ord_price_min": "low",
                    "ord_price_last": "close",
                }
            )
            df_opt_ord = df_opt_ord.reset_index().set_index("timestamp").sort_index()
            df_opt_ord.to_parquet(
                f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_opt_ord_{count}.parquet"
            )
            df_opt_ord = None

            count += 1
            start = end

        print(f"Completed symbol: {sym}\n")
    except Exception as e:
        print(f"Failed for {sym} due to: {e}\n")
        return


# process_oi_snap_symbol(sym="NIFTYIT")

# with Pool(3) as p:
#     p.map(process_oi_snap_symbol, symbols)

# process_snap_file("HINDALCO")

symbols = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")[
    library
].list_symbols()

# with Pool(4) as p:
#     p.map(process_snap_file, symbols)


def combine_processed_snap_files(sym):
    print(f"Started combining {sym}...")

    df_fut_trd_list = [pd.DataFrame()]
    df_fut_ord_list = [pd.DataFrame()]
    df_opt_trd_list = [pd.DataFrame()]
    df_opt_ord_list = [pd.DataFrame()]

    for file in glob.glob(
        f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_*.parquet"
    ):
        if "opt_trd" in file:
            df_opt_trd_list.append(pd.read_parquet(file))
        elif "opt_ord" in file:
            df_opt_ord_list.append(pd.read_parquet(file))
        elif "fut_trd" in file:
            df_fut_trd_list.append(pd.read_parquet(file))
        elif "fut_ord" in file:
            df_fut_ord_list.append(pd.read_parquet(file))
        

    df_fut_trd = pd.concat(df_fut_trd_list).sort_index()
    df_fut_ord = pd.concat(df_fut_ord_list).sort_index()
    df_opt_trd = pd.concat(df_opt_trd_list).sort_index()
    df_opt_ord = pd.concat(df_opt_ord_list).sort_index()

    df_fut_trd = df_fut_trd.rename(columns={"symbol": "ID", "open" : "Open", "high" : "High", "low" : "Low", "close" : "Close", "volume" : "Volume"})
    df_fut_ord = df_fut_ord.rename(columns={"symbol": "ID", "open" : "Open", "high" : "High", "low" : "Low", "close" : "Close"})
    df_opt_trd = df_opt_trd.rename(columns={"symbol": "ID", "open" : "Open", "high" : "High", "low" : "Low", "close" : "Close", "volume" : "Volume"})
    df_opt_ord = df_opt_ord.rename(columns={"symbol": "ID", "open" : "Open", "high" : "High", "low" : "Low", "close" : "Close"})


    if len(df_fut_trd):
        df_fut_trd.to_parquet(
            f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_fut_trd.parquet"
        )
    if len(df_fut_ord):
        df_fut_ord.to_parquet(
            f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_fut_ord.parquet"
        )
    if len(df_opt_trd):
        df_opt_trd.to_parquet(
            f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_opt_trd.parquet"
        )
    if len(df_opt_ord):
        df_opt_ord.to_parquet(
            f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_opt_ord.parquet"
        )

    for file in glob.glob(
        f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_*.parquet"
    ):
        os.remove(file)

    print(f"Completed combining {sym}")


# combine_processed_snap_files(symbols[0])

with Pool(5) as p:
    p.map(combine_processed_snap_files, symbols)

print()
