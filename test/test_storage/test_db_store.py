import pytest
import pandas as pd
from main.storage.db_store import DbStore
from test.utility.mocker.arctic_mocker import ArcticMocker  # type: ignore


@pytest.fixture(scope="class")
def db_store_obj():
    db_store = DbStore()
    db_store._DbStore__store = ArcticMocker()
    return db_store


@pytest.mark.usefixtures("db_store_obj")
class TestDbStore:
    def test_dbStore_list_file_locations_success(self, db_store_obj: DbStore):
        expected_file_locations = ["nse/5_min/opt/trd", "nse/5_min/opt/ord"]
        output_file_locations = db_store_obj.list_file_locations()
        assert output_file_locations == expected_file_locations

    def test_dbStore_list_file_names_success(self, db_store_obj: DbStore):
        expected_file_names = ["5001", "5002", "5003", "5008"]
        output_file_names = db_store_obj.list_file_names(
            file_location="nse/5_min/opt/trd"
        )
        assert output_file_names == expected_file_names

    def test_dbStore_read_success(self, db_store_obj: DbStore):
        read_output = db_store_obj.read(
            file_location="nse/5_min/opt/trd", file_name="symbol"
        )
        assert read_output.iloc[0]["ID"] == 147502023012505001

    def test_dbStore_append_success(self, db_store_obj: DbStore):
        version_number = db_store_obj.append(
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            data=pd.DataFrame(),
            metadata={},
        )
        assert version_number == 1

    def test_dbStore_update_success(self, db_store_obj):
        version_number = db_store_obj.update(
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            data=pd.DataFrame(),
            metadata={},
        )
        assert version_number == 1

    def test_dbStore_write_success(self, db_store_obj):
        version_number = db_store_obj.write(
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            data=pd.DataFrame(),
            metadata={},
        )
        assert version_number == 1

    def test_dbStore_read_metadata_success(self, db_store_obj):
        read_metadata_output = db_store_obj.read_metadata(
            file_location="nse/5_min/opt/trd", file_name="symbol"
        )
        assert read_metadata_output["column"]["nan_ratio"] == 0.2

    def test_dbStore_write_metadata_success(self, db_store_obj):
        write_metadata_call = db_store_obj.write_metadata(
            file_location="nse/5_min/opt/trd", file_name="symbol", metadata={}
        )
        assert write_metadata_call == True
