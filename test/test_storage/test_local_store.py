import pytest
import pandas as pd
from main.storage.local_store import LocalStore
from test.utility.mocker.arctic_mocker import ArcticMocker


@pytest.fixture(scope="class")
def local_store_obj():
    local_store = LocalStore()
    local_store._LocalStore__store = ArcticMocker()
    return local_store


@pytest.mark.usefixtures("local_store_obj")
class TestLocalStore:
    def test_localStore_list_file_locations_success(self, local_store_obj: LocalStore):
        expected_file_locations = ["nse/5_min/opt/trd", "nse/5_min/opt/ord"]
        output_file_locations = local_store_obj.list_file_locations()
        assert output_file_locations == expected_file_locations

    def test_localStore_list_file_names_success(self, local_store_obj: LocalStore):
        expected_file_names = ["5001", "5002", "5003", "5008"]
        output_file_names = local_store_obj.list_file_names(
            file_location="nse/5_min/opt/trd"
        )
        assert output_file_names == expected_file_names

    def test_localStore_read_success(self, local_store_obj):
        read_output = local_store_obj.read(
            file_location="nse/5_min/opt/trd", file_name="symbol"
        )
        assert read_output.iloc[0]["ID"] == 147502023012505001

    def test_localStore_append_success(self, local_store_obj):
        version_number = local_store_obj.append(
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            data=pd.DataFrame(),
            metadata={},
        )
        assert version_number == 1

    def test_localStore_update_success(self, local_store_obj):
        version_number = local_store_obj.update(
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            data=pd.DataFrame(),
            metadata={},
        )
        assert version_number == 1

    def test_localStore_write_success(self, local_store_obj):
        version_number = local_store_obj.write(
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            data=pd.DataFrame(),
            metadata={},
        )
        assert version_number == 1

    def test_localStore_read_metadata_success(self, local_store_obj):
        read_metadata_output = local_store_obj.read_metadata(
            file_location="nse/5_min/opt/trd", file_name="symbol"
        )
        assert read_metadata_output["column"]["nan_ratio"] == 0.2

    def test_localStore_write_metadata_success(self, local_store_obj):
        write_metadata_call = local_store_obj.write_metadata(
            file_location="nse/5_min/opt/trd", file_name="symbol", metadata={}
        )
        assert write_metadata_call == True
