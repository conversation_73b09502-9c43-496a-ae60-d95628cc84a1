import datetime
import pandas as pd
import pytest
import pickle
from main.storage.file_store import FileStore
from test.utility.mocker.minio_mocker import MinioMocker


@pytest.fixture(scope="class")
def file_store_obj():
    file_store = FileStore()
    file_store._FileStore__store = MinioMocker()
    return file_store


@pytest.mark.usefixtures("file_store_obj")
class TestFileStore:
    def test_fileStore_read_success(self, file_store_obj: FileStore):
        read_output = file_store_obj.read(
            file_location="bucket", file_name="object_ALL_DATES"
        )
        read_output_df = pd.DataFrame(read_output, columns=["ALL_DATES"])
        assert read_output_df["ALL_DATES"].dt.date.iloc[0] == datetime.date(2024, 2, 4)

    def test_fileStore_read_dict_success(self, file_store_obj: FileStore):
        read_output = file_store_obj.read(
            file_location="bucket", file_name="object_dict"
        )
        assert isinstance(read_output, pd.DataFrame)
        assert read_output.iloc[0]["value"] == 42

    def test_fileStore_read_exception(self, file_store_obj: FileStore):
        with pytest.raises(Exception) as exception_info:
            file_store_obj.read(
                file_location="invalid_bucket", file_name="invalid_file"
            )
        assert "Error in getting invalid_file from invalid_bucket" in str(
            exception_info.value
        )

    def test_fileStore_list_file_locations_not_implemented(
        self, file_store_obj: FileStore
    ):
        with pytest.raises(NotImplementedError):
            file_store_obj.list_file_locations()

    def test_fileStore_list_file_names_not_implemented(self, file_store_obj: FileStore):
        with pytest.raises(NotImplementedError):
            file_store_obj.list_file_names(file_location="bucket")

    def test_fileStore_append_not_implemented(self, file_store_obj: FileStore):
        with pytest.raises(NotImplementedError):
            file_store_obj.append(
                file_location="bucket",
                file_name="file",
                data=pd.DataFrame(),
                metadata={},
            )

    def test_fileStore_update_not_implemented(self, file_store_obj: FileStore):
        with pytest.raises(NotImplementedError):
            file_store_obj.update(
                file_location="bucket",
                file_name="file",
                data=pd.DataFrame(),
                metadata={},
            )

    def test_fileStore_write_not_implemented(self, file_store_obj: FileStore):
        with pytest.raises(NotImplementedError):
            file_store_obj.write(
                file_location="bucket",
                file_name="file",
                data=pd.DataFrame(),
                metadata={},
            )

    def test_fileStore_read_metadata_not_implemented(self, file_store_obj: FileStore):
        with pytest.raises(NotImplementedError):
            file_store_obj.read_metadata(file_location="bucket", file_name="file")

    def test_fileStore_write_metadata_not_implemented(self, file_store_obj: FileStore):
        with pytest.raises(NotImplementedError):
            file_store_obj.write_metadata(
                file_location="bucket", file_name="file", metadata={}
            )
