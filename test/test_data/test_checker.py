from datetime import date
import datetime
from io import BytesIO
import pytest
import pandas as pd
import numpy as np
from main.data.checker import Checker
from main.enums import StorageType, Check
from main.config.config_factory import ConfigFactory
from mock import patch  # type: ignore


@pytest.fixture(scope="class")
def checker_obj_all_checks_mocked():
    config = ConfigFactory("nse")
    checker = Checker(config)

    def mock_checker_read(*args, **kwargs):
        return pd.DataFrame()

    def mock_check_columns_set(*args, **kwargs):
        return "check_columns_set:\nPassed\n\n"

    def mock_check_columns_dtype(*args, **kwargs):
        return "check_columns_dtype:\nPassed\n\n"

    checker.checks_to_fxn_mapping = {
        Check.CHECK_COLUMNS_SET: mock_check_columns_set,
        Check.CHECK_COLUMNS_DTYPE: mock_check_columns_dtype,
    }

    with patch("main.data.checker.Checker.read", mock_checker_read):
        yield checker


@pytest.mark.usefixtures("checker_obj_all_checks_mocked")
class TestCheckData:
    def test_check_data_all_checks_done(self, checker_obj_all_checks_mocked: Checker):
        checker_obj_all_checks_mocked.checks = [
            Check.CHECK_COLUMNS_SET,
            Check.CHECK_COLUMNS_DTYPE,
        ]
        expected_message = (
            "check_columns_set:\nPassed\n\ncheck_columns_dtype:\nPassed\n\n"
        )
        output_message = checker_obj_all_checks_mocked.check_data(
            StorageType.DB,
            "nse/5_min/opt/trd",
            "symbol",
            check_list=["all"],
        )
        assert output_message.count("\n") == 6
        assert expected_message == output_message

    def test_check_data_single_check_done(self, checker_obj_all_checks_mocked: Checker):
        checker_obj_all_checks_mocked.checks = [
            Check.CHECK_COLUMNS_SET,
            Check.CHECK_COLUMNS_DTYPE,
        ]
        expected_message = "check_columns_set:\nPassed\n\n"
        output_message = checker_obj_all_checks_mocked.check_data(
            StorageType.DB,
            "nse/5_min/opt/trd",
            "symbol",
            check_list=["all"],
            ignore_checks=[Check.CHECK_COLUMNS_DTYPE],
        )
        assert output_message.count("\n") == 3
        assert expected_message == output_message

    def test_check_data_single_check_done_string_input(
        self, checker_obj_all_checks_mocked: Checker
    ):
        checker_obj_all_checks_mocked.checks = [
            Check.CHECK_COLUMNS_SET,
            Check.CHECK_COLUMNS_DTYPE,
        ]
        expected_message = "check_columns_set:\nPassed\n\n"
        output_message = checker_obj_all_checks_mocked.check_data(
            StorageType.DB,
            "nse/5_min/opt/trd",
            "symbol",
            check_list=["all"],
            ignore_checks=["check_columns_dtype"],
        )
        assert output_message.count("\n") == 3
        assert expected_message == output_message

    def test_check_data_single_check_done_invalid_string_input(
        self, checker_obj_all_checks_mocked: Checker
    ):
        checker_obj_all_checks_mocked.checks = [
            Check.CHECK_COLUMNS_SET,
            Check.CHECK_COLUMNS_DTYPE,
        ]
        expected_message = "Invalid Ignore check value: check_columns"

        with pytest.raises(Exception) as exception_info:
            output_message = checker_obj_all_checks_mocked.check_data(
                StorageType.DB,
                "nse/5_min/opt/trd",
                "symbol",
                check_list=["all"],
                ignore_checks=["check_columns"],
            )
        assert str(exception_info.value) == expected_message

    def test_check_data_no_checks_done(self, checker_obj_all_checks_mocked: Checker):
        expected_message = "No checks done!\n"
        output_message = checker_obj_all_checks_mocked.check_data(
            StorageType.DB,
            "nse/5_min/opt/trd",
            "symbol",
            check_list=[],
            ignore_checks=["all"],
        )
        assert output_message.count("\n") == 1
        assert expected_message == output_message


@pytest.fixture(scope="class")
def checker_obj_with_only_read_mocked():
    config = ConfigFactory("nse")
    checker = Checker(config)
    df = pd.read_parquet("./test/utility/test_data/checker_test_data.parquet")
    all_dates_bytes = b""
    try:
        with open(
            "./test/utility/test_data/minio_all_dates_test_data.npy", "rb"
        ) as file:
            all_dates_bytes = file.read()
    except Exception:
        raise Exception("Error in reading ALL_DATES")

    ALL_DATES = np.load(BytesIO(all_dates_bytes), allow_pickle=True)

    def mock_checker_read(*args, **kwargs):
        if (
            kwargs["storage_type"] == StorageType.FILE
            and (kwargs["file_location"], kwargs["file_name"])
            == config.FILE_DICT["ALL_DATES"]
        ):
            return ALL_DATES

        if kwargs["file_name"] == "5008":
            all_timestamp_df = pd.read_parquet(
                "./test/utility/test_data/check_all_timestamp.parquet"
            )
            return all_timestamp_df
        return df.copy()

    def mock_checker_read_metadata(*args, **kwargs):
        if kwargs["file_name"] != "symbol":
            return None

        metadata_dict = {}
        for column in [
            "timestamp",
            "ID",
            "Open",
            "High",
            "Low",
            "Close",
            "Cons_Volume",
            "OI",
            "Open_int",
            "Vwap",
            "iv",
            "delta",
            "gamma",
            "theta",
            "vega",
        ]:
            metadata_dict[column] = {
                "nan_ratio": 0.2 if column not in ["timestamp", "ID"] else 0.0,
                "intraday_jump": 0.0,
                "overnight_open_jump": 0.0,
                "overnight_close_jump": 0.0,
            }

        metadata_dict["start_timestamp"] = metadata_dict[
            "last_timestamp"
        ] = "2024-02-06"

        return metadata_dict

    with patch("main.data.auditor.Auditor.read", mock_checker_read):
        with patch(
            "main.data.checker.Checker.read_metadata", mock_checker_read_metadata
        ):
            yield checker


@pytest.mark.usefixtures("checker_obj_with_only_read_mocked")
class TestChecker:
    class TestCheckColumnsSet:
        def test_check_columns_set_all_columns_present_in_order(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = "check_columns_set:\nPassed\n\n"
            output_message = checker_obj_with_only_read_mocked.check_columns_set(
                StorageType.DB,
                "nse/5_min/opt/trd",
                "symbol",
            )
            assert output_message.count("\n") == 3
            assert expected_message == output_message

        def test_check_columns_set_all_columns_universe_not_in_option_not_in_spot_list(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = "check_columns_set:\nUnexpected universe: invalid_universe, so columns can't be checked\n\n"
            output_message = checker_obj_with_only_read_mocked.check_columns_set(
                storage_type=StorageType.DB,
                file_location="nse/5_min/invalid_universe/trd",
                file_name="symbol",
            )
            assert output_message.count("\n") == 3
            assert output_message == expected_message

        def test_check_columns_set_all_columns_present_but_unordered(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            unordered_cols = [
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Open_int",
                "Cons_Volume",
                "iv",
                "delta",
                "gamma",
                "theta",
                "vega",
                "Vwap",
            ]
            df_with_unordered_cols = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )[unordered_cols]
            expected_message = (
                "check_columns_set:\nUnexpected order of the columns found\n\n"
            )
            output_message = checker_obj_with_only_read_mocked.check_columns_set(
                StorageType.DB, "nse/5_min/opt/trd", "symbol", df_with_unordered_cols
            )
            assert output_message.count("\n") == 3
            assert expected_message == output_message

        def test_check_columns_set_missing_columns(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            df_with_missing_cols = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            ).drop(["ID", "Open"], axis=1)
            expected_message = "check_columns_set:\nShould have 14 but found 12 columns\nFound following missing column(s) in data: ['ID', 'Open']\n\n"
            output_message = checker_obj_with_only_read_mocked.check_columns_set(
                StorageType.DB, "nse/5_min/opt/trd", "symbol", df_with_missing_cols
            )
            assert output_message.count("\n") == 4
            assert expected_message == output_message

        def test_check_columns_set_extra_columns(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            df_with_extra_cols = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            df_with_extra_cols["pcr"] = 3.4
            expected_message = "check_columns_set:\nShould have 14 but found 15 columns\nFound following extra column(s) in data: ['pcr']\n\n"
            output_message = checker_obj_with_only_read_mocked.check_columns_set(
                StorageType.DB, "nse/5_min/opt/trd", "symbol", df_with_extra_cols
            )
            assert output_message.count("\n") == 4
            assert expected_message == output_message

        def test_check_columns_set_both_missing_and_extra_columns(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            df_with_extra_and_missing_cols = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            ).drop(["ID", "Open"], axis=1)
            df_with_extra_and_missing_cols["pcr"] = 3.4
            df_with_extra_and_missing_cols["next_cons_volume"] = 2.1
            expected_message = "check_columns_set:\nFound following extra column(s) in data: ['next_cons_volume', 'pcr']\nFound following missing column(s) in data: ['ID', 'Open']\n\n"
            output_message = checker_obj_with_only_read_mocked.check_columns_set(
                StorageType.DB,
                "nse/5_min/opt/trd",
                "symbol",
                df_with_extra_and_missing_cols,
            )
            assert output_message.count("\n") == 4
            assert expected_message == output_message

    class TestCheckColumnsDtype:
        def test_check_columns_dtype_all_column_dtype_correct(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = "check_columns_dtype:\nPassed\n\n"
            output_message = checker_obj_with_only_read_mocked.check_columns_dtype(
                StorageType.DB,
                "nse/5_min/opt/trd",
                "symbol",
            )
            assert output_message.count("\n") == 3
            assert expected_message == output_message

        def test_check_columns_dtype_some_column_dtype_mismatch(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            df_with_col_dtypes_mismatch = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            df_with_col_dtypes_mismatch["extra_column"] = 4.5
            df_with_col_dtypes_mismatch["ID"] = 3265986.3
            expected_message = "check_columns_dtype:\nUnexpected dtype for column: ID, since expected uint64 but got float64\nUnexpected column: extra_column, so dtype can't be checked\n\n"
            output_message = checker_obj_with_only_read_mocked.check_columns_dtype(
                StorageType.DB,
                "nse/5_min/opt/trd",
                "symbol",
                df_with_col_dtypes_mismatch,
            )
            assert output_message.count("\n") == 4
            assert expected_message == output_message

    class TestCheckNanEntries:
        def test_check_nan_entries_success(self, checker_obj_with_only_read_mocked):
            expected_message = "check_nan_entries:\nPassed\n\n"
            output_message = checker_obj_with_only_read_mocked.check_nan_entries(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            assert expected_message == output_message

        def test_check_nan_entries_two_column_exceeds(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            df = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )

            df["Close"] = df["iv"] = np.nan
            expected_message = "check_nan_entries:\nFound NaN values > 30.000000000000004% for 1 dates for the column: Close, with highest NaN count of 100.0% on 2024-02-06\nFound NaN values > 30.000000000000004% for 1 dates for the column: iv, with highest NaN count of 100.0% on 2024-02-06\n\n"
            output_message = checker_obj_with_only_read_mocked.check_nan_entries(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
                data=df,
            )
            assert output_message.count("\n") == 4
            assert expected_message == output_message

        def test_check_nan_entries_metadata_none(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            df = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            df["iv"] = df["delta"] = df["gamma"] = df["theta"] = df["vega"] = 0.5
            expected_message = "check_nan_entries:\nMetadataReadError: Found error during reading metadata_dict from invalid_symbol located at nse/5_min/opt/trd inside store: StorageType.DB\n\n"
            output_message = checker_obj_with_only_read_mocked.check_nan_entries(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="invalid_symbol",
                data=df,
            )
            assert output_message.count("\n") == 3
            assert output_message == expected_message

        def test_check_nan_entries_metadata_one_column_missing(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            def mock_checker_read_metadata_one_column_missing(*args, **kwargs):
                metadata_dict = {}
                for column in [
                    "timestamp",
                    "ID",
                    "Open",
                    "High",
                    "Low",
                    "Close",
                    "Cons_Volume",
                    "OI",
                    "Open_int",
                    "iv",
                    "delta",
                    "gamma",
                    "theta",
                    "vega",
                ]:
                    metadata_dict[column] = {
                        "nan_ratio": 0.2 if column not in ["timestamp", "ID"] else 0.0,
                        "intraday_jump": 0.0,
                        "overnight_open_jump": 0.0,
                        "overnight_close_jump": 0.0,
                    }

                metadata_dict["start_timestamp"] = metadata_dict[
                    "last_timestamp"
                ] = "2024-02-06"

                return metadata_dict

            with patch(
                "main.data.checker.Checker.read_metadata",
                new=mock_checker_read_metadata_one_column_missing,
            ):
                df = checker_obj_with_only_read_mocked.read(
                    storage_type=StorageType.DB,
                    file_location="nse/5_min/opt/trd",
                    file_name="symbol",
                )
                expected_message = "check_nan_entries:\nColumn: Vwap not in metadata for nse/5_min/opt/trd with symbol = symbol\n\n"
                output_message = checker_obj_with_only_read_mocked.check_nan_entries(
                    storage_type=StorageType.DB,
                    file_location="nse/5_min/opt/trd",
                    file_name="symbol",
                    data=df,
                )

            assert output_message.count("\n") == 3
            assert output_message == expected_message

    class TestCheckIntradaySuddenJumps:
        def test_check_intraday_sudden_jumps_success(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            def mock_checker_read_metadata_check_intraday_sudden_jumps_success(
                *args, **kwargs
            ):
                metadata_dict = {}
                for column in [
                    "timestamp",
                    "ID",
                    "Open",
                    "High",
                    "Low",
                    "Close",
                    "Cons_Volume",
                    "OI",
                    "Open_int",
                    "Vwap",
                    "iv",
                    "delta",
                    "gamma",
                    "theta",
                    "vega",
                ]:
                    metadata_dict[column] = {
                        "nan_ratio": 0.2 if column not in ["timestamp", "ID"] else 0.0,
                        "intraday_jump": 0.0,
                        "overnight_open_jump": 0.0,
                        "overnight_close_jump": 0.0,
                    }

                metadata_dict["start_timestamp"] = metadata_dict[
                    "last_timestamp"
                ] = "2024-02-06"

                metadata_dict["Open"]["intraday_jump"] = 1.73
                metadata_dict["High"]["intraday_jump"] = 2.04
                metadata_dict["Low"]["intraday_jump"] = 1.23
                metadata_dict["Close"]["intraday_jump"] = 2.21

                return metadata_dict

            with patch(
                "main.data.checker.Checker.read_metadata",
                new=mock_checker_read_metadata_check_intraday_sudden_jumps_success,
            ):
                df = checker_obj_with_only_read_mocked.read(
                    storage_type=StorageType.DB,
                    file_location="nse/5_min/opt/trd",
                    file_name="symbol",
                )

                expected_message = "check_intraday_sudden_jumps:\nPassed\n0.3333333333333333% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination\n0.3333333333333333% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination\n0.3333333333333333% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination\n0.3333333333333333% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination\n\n"
                output_message = (
                    checker_obj_with_only_read_mocked.check_intraday_sudden_jumps(
                        StorageType.DB, "nse/5_min/opt/trd", "symbol", df
                    )
                )
                assert expected_message == output_message
                assert output_message.count("\n") == 7

        def test_check_intraday_sudden_jumps_fails_close(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            df = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            df["ID"] = df["ID"].iloc[0]
            df["High"] = df["Low"] = df["Open"] = 0

            expected_message = "check_intraday_sudden_jumps:\n80.0% data for which Close percentage change is greater than allowed 0.0% with highest jump: 24.8546478424672 on 2024-02-06 09:35:00\nPassed\n\n"
            output_message = (
                checker_obj_with_only_read_mocked.check_intraday_sudden_jumps(
                    StorageType.DB, "nse/5_min/opt/trd", "symbol", df.head(10)
                )
            )
            assert expected_message == output_message
            assert output_message.count("\n") == 4

    class TestCheckDuplicateEntries:
        def test_check_duplicate_entries_success(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = "check_duplicate_entries:\nPassed\n\n"
            output_message = checker_obj_with_only_read_mocked.check_duplicate_entries(
                StorageType.DB, "nse/5_min/opt/trd", "symbol"
            )
            assert expected_message == output_message
            assert output_message.count("\n") == 3

        def test_check_duplicate_entries_fails_complete_rows(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            df = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            record = df.iloc[1]
            df.iloc[1] = df.iloc[0]
            df["ID"].iloc[1] = df["ID"].iloc[0]

            expected_message = "check_duplicate_entries:\n1 duplicate entries with timestamp + row\n1 different entries for same timestamp + ID\n\n"
            output_message = checker_obj_with_only_read_mocked.check_duplicate_entries(
                StorageType.DB, "nse/5_min/opt/trd", "symbol", df
            )

            df.iloc[1] = record
            assert expected_message == output_message
            assert output_message.count("\n") == 4

        def test_check_duplicate_entries_fails_rows_without_index(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            df = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            record = df.iloc[-1]
            df.iloc[-1] = df.iloc[0]
            df["ID"].iloc[-1] = df["ID"].iloc[0]

            expected_message = "check_duplicate_entries:\n1 different entries for same timestamp + ID\n\n"
            output_message = checker_obj_with_only_read_mocked.check_duplicate_entries(
                StorageType.DB, "nse/5_min/opt/trd", "symbol", df
            )

            df.iloc[-1] = record
            assert expected_message == output_message
            assert output_message.count("\n") == 3

    class TestCheckForwardFill:
        def test_check_forward_fill_success(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = "check_forward_fill:\nPassed\n\n"
            output_message = checker_obj_with_only_read_mocked.check_forward_fill(
                StorageType.DB, "nse/5_min/opt/trd", "symbol"
            )
            assert expected_message == output_message
            assert output_message.count("\n") == 3

        def test_check_forward_fill_fails_Open_int(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            df = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            df["Open_int"].iloc[1] = np.nan
            df["ID"].iloc[1] = df["ID"].iloc[0]

            expected_message = (
                "check_forward_fill:\nColumn: Open_int is not forward filled\n\n"
            )
            output_message = checker_obj_with_only_read_mocked.check_forward_fill(
                StorageType.DB, "nse/5_min/opt/trd", "symbol", df
            )
            assert expected_message == output_message
            assert output_message.count("\n") == 3

    class TestCheckMonotonicNature:
        def test_check_monotonic_nature_success(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = "check_monotonic_nature:\nPassed\n\n"
            output_message = checker_obj_with_only_read_mocked.check_monotonic_nature(
                StorageType.DB, "nse/5_min/opt/trd", "symbol"
            )
            assert expected_message == output_message
            assert output_message.count("\n") == 3

        def test_check_monotonic_nature_fails_Cons_Volume(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            df = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            df["ID"].iloc[1] = df["ID"].iloc[0]
            df["Cons_Volume"].iloc[1] = df["Cons_Volume"].iloc[0] - 1

            expected_message = "check_monotonic_nature:\nColumn: Cons_Volume is not monotonically increasing\n\n"
            output_message = checker_obj_with_only_read_mocked.check_monotonic_nature(
                StorageType.DB, "nse/5_min/opt/trd", "symbol", df
            )
            assert expected_message == output_message
            assert output_message.count("\n") == 3

    class TestCheckOvernightSuddenJumps:
        def test_check_overnight_sudden_jumps_success(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            def mock_checker_read_metadata_test_check_overnight_sudden_jumps_success(
                *args, **kwargs
            ):
                metadata_dict = {}
                for column in [
                    "timestamp",
                    "ID",
                    "Open",
                    "High",
                    "Low",
                    "Close",
                    "Cons_Volume",
                    "OI",
                    "Open_int",
                    "Vwap",
                    "iv",
                    "delta",
                    "gamma",
                    "theta",
                    "vega",
                ]:
                    metadata_dict[column] = {
                        "nan_ratio": 0.2 if column not in ["timestamp", "ID"] else 0.0,
                        "intraday_jump": 0.0,
                        "overnight_open_jump": 0.0,
                        "overnight_close_jump": 0.0,
                    }

                metadata_dict["start_timestamp"] = metadata_dict[
                    "last_timestamp"
                ] = "2024-02-06"

                metadata_dict["Open"]["overnight_open_jump"] = 1.02
                metadata_dict["High"]["overnight_open_jump"] = 2.31
                metadata_dict["Low"]["overnight_open_jump"] = 1.33
                metadata_dict["Close"]["overnight_open_jump"] = 1.54

                metadata_dict["Open"]["overnight_close_jump"] = 2.26
                metadata_dict["High"]["overnight_close_jump"] = 2.29
                metadata_dict["Low"]["overnight_close_jump"] = 2.70
                metadata_dict["Close"]["overnight_close_jump"] = 2.76

                return metadata_dict

            with patch(
                "main.data.checker.Checker.read_metadata",
                new=mock_checker_read_metadata_test_check_overnight_sudden_jumps_success,
            ):
                data = pd.read_parquet(
                    "./test/utility/test_data/check_overnight_sudden_jumps_test_data.parquet"
                )
                checker_obj_with_only_read_mocked._config.ALLOWED_SUDDEN_JUMP = 0.75
                expected_message = "check_overnight_sudden_jumps:\nPassed\n\n"
                output_message = (
                    checker_obj_with_only_read_mocked.check_overnight_sudden_jumps(
                        StorageType.DB, "nse/5_min/opt/trd", "symbol", data
                    )
                )
                checker_obj_with_only_read_mocked._config.ALLOWED_SUDDEN_JUMP = -0.1
                assert output_message.count("\n") == 3
                assert expected_message == output_message

        def test_check_overnight_sudden_jumps_fails(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            data = pd.read_parquet(
                "./test/utility/test_data/check_overnight_sudden_jumps_test_data.parquet"
            )
            expected_message = "check_overnight_sudden_jumps:\n85.71428571428571% data for which Open percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 101.79640253506037 on 4   2024-02-06 09:30:00\n4   2024-02-07 09:40:00\nName: timestamp, dtype: datetime64[ns]\n42.857142857142854% data for which Open percentage change in current day last price to previous day last price is greater than allowed 0.0% with highest jump: 225.14968117584039 on 0   2024-02-06 09:20:00\n0   2024-02-07 09:20:00\nName: timestamp, dtype: datetime64[ns]\n100.0% data for which Low percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 132.14285714285714 on 4   2024-02-06 09:30:00\n4   2024-02-07 09:40:00\nName: timestamp, dtype: datetime64[ns]\n42.857142857142854% data for which Low percentage change in current day last price to previous day last price is greater than allowed 0.0% with highest jump: 269.2857197352818 on 0   2024-02-06 09:20:00\n0   2024-02-07 09:20:00\nName: timestamp, dtype: datetime64[ns]\n71.42857142857143% data for which High percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 230.53891162351908 on 4   2024-02-06 09:30:00\n4   2024-02-07 09:40:00\nName: timestamp, dtype: datetime64[ns]\n42.857142857142854% data for which High percentage change in current day last price to previous day last price is greater than allowed 0.0% with highest jump: 228.1436930151069 on 0   2024-02-06 09:20:00\n0   2024-02-07 09:20:00\nName: timestamp, dtype: datetime64[ns]\n100.0% data for which Close percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 153.57142857142858 on 4   2024-02-06 09:30:00\n4   2024-02-07 09:40:00\nName: timestamp, dtype: datetime64[ns]\n42.857142857142854% data for which Close percentage change in current day last price to previous day last price is greater than allowed 0.0% with highest jump: 275.71427481515065 on 0   2024-02-06 09:20:00\n0   2024-02-07 09:20:00\nName: timestamp, dtype: datetime64[ns]\nPassed\n\n"
            output_message = (
                checker_obj_with_only_read_mocked.check_overnight_sudden_jumps(
                    StorageType.DB, "nse/5_min/opt/trd", "symbol", data
                )
            )
            assert output_message.count("\n") == 27
            assert expected_message == output_message

        def test_check_overnight_sudden_jumps_metadata_none_fails(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            def mock_checker_read_metadata_none_fails(*args, **kwargs):
                return None

            with patch(
                "main.data.checker.Checker.read_metadata",
                new=mock_checker_read_metadata_none_fails,
            ):
                data = pd.read_parquet(
                    "./test/utility/test_data/check_overnight_sudden_jumps_test_data.parquet"
                )
                expected_message = "check_overnight_sudden_jumps:\nMetadata is none or doesn't have last_timestamp for nse/5_min/opt/trd with symbol = symbol\n100.0% data for which Open percentage change in current day first price to previous day last price is greater than allowed -10.0% with highest jump: 101.79640253506037 on 2024-02-07 09:20:00\n100.0% data for which Open percentage change in current day last price to previous day last price is greater than allowed -10.0% with highest jump: 0.0 on 2024-02-07 09:20:00\n100.0% data for which Low percentage change in current day first price to previous day last price is greater than allowed -10.0% with highest jump: 132.14285714285714 on 2024-02-07 09:20:00\n100.0% data for which Low percentage change in current day last price to previous day last price is greater than allowed -10.0% with highest jump: 0.0 on 2024-02-07 09:20:00\n100.0% data for which High percentage change in current day first price to previous day last price is greater than allowed -10.0% with highest jump: 230.53891162351908 on 2024-02-07 09:20:00\n100.0% data for which High percentage change in current day last price to previous day last price is greater than allowed -10.0% with highest jump: 0.0 on 2024-02-07 09:20:00\n100.0% data for which Close percentage change in current day first price to previous day last price is greater than allowed -10.0% with highest jump: 153.57142857142858 on 2024-02-07 09:20:00\n100.0% data for which Close percentage change in current day last price to previous day last price is greater than allowed -10.0% with highest jump: 0.0 on 2024-02-07 09:20:00\nPassed\n\n"
                output_message = (
                    checker_obj_with_only_read_mocked.check_overnight_sudden_jumps(
                        storage_type=StorageType.DB,
                        file_location="nse/5_min/opt/trd",
                        file_name="symbol",
                        data=data,
                    )
                )

                assert output_message == expected_message

    class TestCheckAllDates:
        def test_check_all_dates_both_same_success(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = "check_all_dates:\nPassed\n\n"
            data = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            output_message = checker_obj_with_only_read_mocked.check_all_dates(
                StorageType.DB, "opt/5_min/trd", "symbol", data
            )

            assert output_message.count("\n") == 3
            assert output_message == expected_message

        def test_check_all_dates_one_missing_fails(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = f"check_all_dates:\nFound {1} missing dates\n\n"
            data = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            new_data = pd.DataFrame(
                [
                    [
                        36752024011518001,
                        0.5,
                        0.7,
                        0.3,
                        0.6,
                        0.6,
                        456,
                        0.87,
                        35,
                        0.75,
                        -0.343,
                        0.009,
                        -0.0483,
                    ]
                ],
                columns=data.columns,
                index=[pd.Timestamp(2024, 2, 8, 9, 20, 0)],
            )
            data = pd.concat([data, new_data])
            data.index.name = "timestamp"
            output_message = checker_obj_with_only_read_mocked.check_all_dates(
                storage_type=StorageType.DB,
                file_location="opt/5_min/trd",
                file_name="symbol",
                data=data,
            )
            assert output_message.count("\n") == 3
            assert output_message == expected_message

        def test_check_all_dates_one_extra_fails(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            extra_dates = [date(2024, 2, 3)]
            expected_message = f"check_all_dates:\nFound {1} extra dates\n\n"
            data = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            data.index = data.index.map(lambda x: x.replace(day=4))
            new_data = pd.DataFrame(
                [
                    [
                        36752024011518001,
                        0.5,
                        0.7,
                        0.3,
                        0.6,
                        0.6,
                        456,
                        0.87,
                        35,
                        0.75,
                        -0.343,
                        0.009,
                        -0.0483,
                    ]
                ],
                columns=data.columns,
                index=[pd.Timestamp(2024, 2, 3, 9, 20, 0)],
            )
            data = pd.concat([data, new_data])
            data.index.name = "timestamp"
            output_message = checker_obj_with_only_read_mocked.check_all_dates(
                StorageType.DB, "opt/5_min/trd", "symbol", data
            )
            assert output_message.count("\n") == 3
            assert output_message == expected_message

        def test_check_all_dates_three_missing_one_extra_fails(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = (
                "check_all_dates:\nFound 2 missing dates\nFound 1 extra dates\n\n"
            )
            data = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            new_data = pd.DataFrame(
                [
                    [
                        36752024011518001,
                        0.5,
                        0.7,
                        0.3,
                        0.6,
                        0.6,
                        456,
                        0.87,
                        35,
                        0.75,
                        -0.343,
                        0.009,
                        -0.0483,
                    ]
                ],
                columns=data.columns,
                index=[pd.Timestamp(2024, 2, 10, 9, 20, 0)],
            )
            data = pd.concat([data, new_data])
            data = data[pd.DatetimeIndex(data.index).date != date(2023, 7, 24)]
            data.index.name = "timestamp"
            output_message = checker_obj_with_only_read_mocked.check_all_dates(
                StorageType.DB, "opt/5_min/trd", "symbol", data
            )
            assert output_message.count("\n") == 4
            assert output_message == expected_message

        @patch(
            "main.data.checker.Checker.read_metadata", new=lambda *args, **kwargs: None
        )
        def test_check_all_dates_metadata_none_fails(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = "check_all_dates:\nMetadata is none or doesn't have last_timestamp for nse/5_min/opt/trd with symbol = symbol\n\n"
            output_message = checker_obj_with_only_read_mocked.check_all_dates(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            assert output_message == expected_message

        def test_check_all_dates_metadata_column_missing_fails(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            def mock_checker_read_metadata(*args, **kwargs):
                metadata_dict = {}
                for column in [
                    "timestamp",
                    "ID",
                    "Open",
                    "High",
                    "Low",
                    "Close",
                    "Cons_Volume",
                    "OI",
                    "Open_int",
                    "iv",
                    "delta",
                    "gamma",
                    "theta",
                    "vega",
                ]:
                    metadata_dict[column] = {
                        "nan_ratio": 0.2 if column not in ["timestamp", "ID"] else 0.0,
                        "intraday_jump": 0.0,
                        "overnight_open_jump": 0.0,
                        "overnight_close_jump": 0.0,
                    }

                return metadata_dict

            with patch(
                "main.data.checker.Checker.read_metadata",
                new=mock_checker_read_metadata,
            ):
                expected_message = "check_all_dates:\nMetadata is none or doesn't have last_timestamp for nse/5_min/opt/trd with symbol = symbol\n\n"
                output_message = checker_obj_with_only_read_mocked.check_all_dates(
                    storage_type=StorageType.DB,
                    file_location="nse/5_min/opt/trd",
                    file_name="symbol",
                )
                assert output_message == expected_message

        def test_check_all_dates_metadata_last_timestamp_smaller_success(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            data = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
            data.index = data.index.map(lambda x: x.replace(day=7))

            expected_message = "check_all_dates:\nPassed\n\n"
            output_message = checker_obj_with_only_read_mocked.check_all_dates(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
                data=data,
            )
            assert output_message == expected_message

    class TestCheckAllTimestamps:
        def test_check_all_timestamps_success(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = "check_all_timestamps:\nPassed\n\n"
            output_message = checker_obj_with_only_read_mocked.check_all_timestamps(
                storage_type=StorageType.DB,
                file_location="nse/1_min/futidx/trd",
                file_name="5008",
            )
            assert output_message == expected_message

        def test_check_all_timestamps_one_missing_fails(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            data = pd.read_parquet(
                "./test/utility/test_data/check_all_timestamp.parquet"
            )
            data = data[1:]
            expected_message = "check_all_timestamps:\nFound 1 missing times\n\n"
            output_message = checker_obj_with_only_read_mocked.check_all_timestamps(
                storage_type=StorageType.DB,
                file_location="nse/1_min/futidx/trd",
                file_name="5008",
                data=data,
            )
            assert output_message == expected_message

        def test_check_all_timestamps_one_extra_fails(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            data = pd.read_parquet(
                "./test/utility/test_data/check_all_timestamp.parquet"
            )
            extra_data = data.iloc[[-1]]
            extra_data.index.values[0] += pd.Timedelta(seconds=60)
            data = pd.concat([data, extra_data])
            extra_times = [datetime.time(15, 40)]
            expected_message = "check_all_timestamps:\nFound 1 extra times\n\n"
            output_message = checker_obj_with_only_read_mocked.check_all_timestamps(
                storage_type=StorageType.DB,
                file_location="nse/1_min/futidx/trd",
                file_name="5008",
                data=data,
            )
            assert output_message == expected_message

        def test_check_all_timestamps_one_missing_one_extra_fails(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            data = pd.read_parquet(
                "./test/utility/test_data/check_all_timestamp.parquet"
            )
            extra_data = data.iloc[[-1]]
            extra_data.index.values[0] += pd.Timedelta(seconds=60)
            data = pd.concat([data, extra_data])

            data = data[1:]
            expected_message = (
                "check_all_timestamps:\nFound 1 missing times\nFound 1 extra times\n\n"
            )
            output_message = checker_obj_with_only_read_mocked.check_all_timestamps(
                storage_type=StorageType.DB,
                file_location="nse/1_min/futidx/trd",
                file_name="5008",
                data=data,
            )
            assert output_message == expected_message

    class TestCheckOHLC:
        def test_check_OHLC_passed(self, checker_obj_with_only_read_mocked: Checker):
            expected_message = "check_OHLC:\nPassed\n\n"
            df = pd.DataFrame(
                {
                    "timestamp": pd.date_range(start="2023-01-01", periods=5, freq="D"),
                    "ID": [1, 1, 1, 1, 1],
                    "Open": [100, 101, 102, 103, 104],
                    "High": [105, 106, 107, 108, 109],
                    "Low": [99, 100, 101, 102, 103],
                    "Close": [104, 105, 106, 107, 108],
                }
            )
            df = df.set_index("timestamp")
            output_message = checker_obj_with_only_read_mocked.check_OHLC(
                StorageType.DB, "nse/5_min/opt/trd", "symbol", df
            )
            assert output_message.count("\n") == 3
            assert expected_message == output_message

        def test_check_OHLC_OHLC_basic_check_failed(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = (
                "check_OHLC:\n100.0% data for which OHLC basic check failed\nPassed\n\n"
            )
            df = pd.DataFrame(
                {
                    "timestamp": pd.date_range(start="2023-01-01", periods=5, freq="D"),
                    "ID": [1, 1, 1, 1, 1],
                    "Open": [100, 101, 102, 103, 104],
                    "High": [99, 100, 101, 102, 103],  # High is less than Low
                    "Low": [105, 106, 107, 108, 109],  # Low is greater than High
                    "Close": [104, 105, 106, 107, 108],
                }
            )
            df = df.set_index("timestamp")
            output_message = checker_obj_with_only_read_mocked.check_OHLC(
                StorageType.DB, "nse/5_min/opt/trd", "symbol", df
            )
            assert output_message.count("\n") == 4
            assert expected_message == output_message

        def test_check_OHLC_open_close_and_low_high_jump_exceeded(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = "check_OHLC:\n100.0% data for which OHLC basic check failed\nPassed\n100.0% data for which open-close percentage change is 0.9 times greater than average open-close percentage for the specific ID and Date combination\n100.0% data for which low-high percentage change is 0.9 times greater than average low-high percentage for the specific ID and Date combination\n\n"
            df = pd.DataFrame(
                {
                    "timestamp": pd.date_range(start="2023-01-01", periods=5, freq="D"),
                    "ID": [1, 1, 1, 1, 1],
                    "Open": [100, 101, 102, 103, 104],
                    "High": [105, 106, 107, 108, 109],
                    "Low": [99, 100, 101, 102, 103],
                    "Close": [200, 201, 202, 203, 204],  # Large jump in Close
                }
            )
            df = df.set_index("timestamp")
            checker_obj_with_only_read_mocked._config.ALLOWED_OHLC_ACROSS_ROW_AVERAGE_MULTIPLIER = (
                0.9
            )
            output_message = checker_obj_with_only_read_mocked.check_OHLC(
                StorageType.DB, "nse/5_min/opt/trd", "symbol", df
            )
            assert output_message.count("\n") == 6
            assert expected_message == output_message

        def test_check_OHLC_missing_columns(
            self, checker_obj_with_only_read_mocked: Checker
        ):
            expected_message = "check_OHLC:\nPassed\n\n"
            df = pd.DataFrame(
                {
                    "timestamp": pd.date_range(start="2023-01-01", periods=5, freq="D"),
                    "ID": [1, 1, 1, 1, 1],
                    "Open": [100, 101, 102, 103, 104],
                    "High": [105, 106, 107, 108, 109],
                    "Close": [104, 105, 106, 107, 108],
                }
            )
            df = df.set_index("timestamp")
            output_message = checker_obj_with_only_read_mocked.check_OHLC(
                StorageType.DB, "nse/5_min/opt/trd", "symbol", df
            )
            assert output_message.count("\n") == 3
            assert expected_message == output_message


@pytest.mark.usefixtures("checker_obj_with_only_read_mocked")
class TestCheckDataOperations:
    def test_check_data_operations_all_checks_success(
        self, checker_obj_with_only_read_mocked: Checker
    ):
        # Store original config value
        original_ignore_check_list = (
            checker_obj_with_only_read_mocked._config.UNIVERSE_TO_IGNORE_CHECK_LIST.get(
                "opt", []
            ).copy()
        )

        try:

            def mock_checker_read_metadata_all_checks_success(*args, **kwargs):
                metadata_dict = {}
                for column in [
                    "timestamp",
                    "ID",
                    "Open",
                    "High",
                    "Low",
                    "Close",
                    "Cons_Volume",
                    "OI",
                    "Open_int",
                    "Vwap",
                    "iv",
                    "delta",
                    "gamma",
                    "theta",
                    "vega",
                ]:
                    metadata_dict[column] = {
                        "nan_ratio": 0.2 if column not in ["timestamp", "ID"] else 0.0,
                        "intraday_jump": 0.0,
                        "overnight_open_jump": 0.0,
                        "overnight_close_jump": 0.0,
                    }

                metadata_dict["start_timestamp"] = metadata_dict[
                    "last_timestamp"
                ] = "2024-02-06"

                metadata_dict["Open"]["intraday_jump"] = 2.21
                metadata_dict["High"]["intraday_jump"] = 2.21
                metadata_dict["Low"]["intraday_jump"] = 2.21
                metadata_dict["Close"]["intraday_jump"] = 2.21

                metadata_dict["Open"]["overnight_open_jump"] = 2.21
                metadata_dict["High"]["overnight_open_jump"] = 2.21
                metadata_dict["Low"]["overnight_open_jump"] = 2.21
                metadata_dict["Close"]["overnight_open_jump"] = 2.21

                return metadata_dict

            with patch(
                "main.data.checker.Checker.read_metadata",
                new=mock_checker_read_metadata_all_checks_success,
            ):
                data = checker_obj_with_only_read_mocked.read(
                    storage_type=StorageType.DB,
                    file_location="nse/5_min/opt/trd",
                    file_name="symbol",
                )

                data["High"] = data["Close"]
                data["Low"] = data["Close"]
                data["Open"] = data["Close"]

                checker_obj_with_only_read_mocked._config.UNIVERSE_TO_IGNORE_CHECK_LIST[
                    "opt"
                ] = []

                output_message = (
                    checker_obj_with_only_read_mocked.check_data_operations(
                        StorageType.DB, "nse/5_min/opt/trd", "symbol", data
                    )
                )
                assert (
                    output_message
                    == "check_columns_set:\nPassed\n\ncheck_columns_dtype:\nPassed\n\ncheck_nan_entries:\nPassed\n\ncheck_duplicate_entries:\nPassed\n\ncheck_monotonic_nature:\nPassed\n\ncheck_all_dates:\nPassed\n\ncheck_all_timestamps:\nPassed\n\ncheck_forward_fill:\nPassed\n\ncheck_OHLC:\nPassed\n\ncheck_intraday_sudden_jumps:\nPassed\n0.3333333333333333% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination\n0.3333333333333333% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination\n0.3333333333333333% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination\n0.3333333333333333% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination\n\ncheck_overnight_sudden_jumps:\nPassed\n\n"
                )
        finally:
            # Restore original config value
            checker_obj_with_only_read_mocked._config.UNIVERSE_TO_IGNORE_CHECK_LIST[
                "opt"
            ] = original_ignore_check_list

    def test_check_data_operations_some_checks_failed(
        self, checker_obj_with_only_read_mocked: Checker
    ):
        # Store original config value
        original_ignore_check_list = (
            checker_obj_with_only_read_mocked._config.UNIVERSE_TO_IGNORE_CHECK_LIST.get(
                "opt", []
            ).copy()
        )

        try:
            expected_message = "DataChecksFailed: Data inconsistencies found for the symbol: symbol.\ncheck_columns_set:\nPassed\n\ncheck_columns_dtype:\nPassed\n\ncheck_nan_entries:\nFound NaN values > 30.000000000000004% for 1 dates for the column: Close, with highest NaN count of 100.0% on 2024-02-06\nFound NaN values > 30.000000000000004% for 1 dates for the column: iv, with highest NaN count of 100.0% on 2024-02-06\n\ncheck_duplicate_entries:\nPassed\n\ncheck_monotonic_nature:\nPassed\n\ncheck_all_dates:\nPassed\n\ncheck_all_timestamps:\nPassed\n\ncheck_forward_fill:\nPassed\n\ncheck_OHLC:\n10.0% data for which OHLC basic check failed\nPassed\n\ncheck_intraday_sudden_jumps:\n31.333333333333332% data for which Open percentage change is greater than allowed 0.0% with highest jump: 172.74276664847267 on 2024-02-06 11:40:00\n24.333333333333332% data for which Low percentage change is greater than allowed 0.0% with highest jump: 122.98245346337033 on 2024-02-06 11:40:00\n30.666666666666668% data for which High percentage change is greater than allowed 0.0% with highest jump: 203.08639606021853 on 2024-02-06 11:35:00\nPassed\n0.3333333333333333% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination\n0.3333333333333333% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination\n0.3333333333333333% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination\n0.0% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination\n\ncheck_overnight_sudden_jumps:\n100.0% data for which Open percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 116.94291842891515 on 0   2024-02-06 09:20:00\n0   2024-02-06 09:20:00\nName: timestamp, dtype: datetime64[ns]\n100.0% data for which Low percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 90.13539961535751 on 0   2024-02-06 09:20:00\n0   2024-02-06 09:20:00\nName: timestamp, dtype: datetime64[ns]\n100.0% data for which High percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 117.15328769479581 on 0   2024-02-06 09:20:00\n0   2024-02-06 09:20:00\nName: timestamp, dtype: datetime64[ns]\n100.0% data for which Close percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 114.06844437366223 on 0   2024-02-06 09:20:00\n0   2024-02-06 09:20:00\nName: timestamp, dtype: datetime64[ns]\nPassed\n\n"

            df = checker_obj_with_only_read_mocked.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )

            df["Close"] = df["iv"] = np.nan

            checker_obj_with_only_read_mocked._config.UNIVERSE_TO_IGNORE_CHECK_LIST[
                "opt"
            ] = ["check_overnight_sudden_jumps", "check_intraday_sudden_jumps"]

            with pytest.raises(Exception) as exception_info:
                checker_obj_with_only_read_mocked.check_data_operations(
                    storage_type=StorageType.DB,
                    file_location="nse/5_min/opt/trd",
                    file_name="symbol",
                    data=df,
                )
            assert str(exception_info.value) == expected_message
        finally:
            # Restore original config value
            checker_obj_with_only_read_mocked._config.UNIVERSE_TO_IGNORE_CHECK_LIST[
                "opt"
            ] = original_ignore_check_list

    def test_check_data_operations_empty_data_raise_exception(
        self, checker_obj_with_only_read_mocked: Checker
    ):
        df = pd.DataFrame()
        expected_message = (
            "DataError: DataFrame contains no data for the symbol: symbol"
        )
        with pytest.raises(Exception) as exception_info:
            checker_obj_with_only_read_mocked.check_data_operations(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
                data=df,
            )
        assert str(exception_info.value) == expected_message

    def test_check_data_operations_no_timestamp_in_index_raise_exception(
        self, checker_obj_with_only_read_mocked: Checker
    ):
        data = checker_obj_with_only_read_mocked.read(
            storage_type=StorageType.DB,
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
        )
        data = data.reset_index()
        expected_message = "DataError: DataFrame must include timestamp in the index"
        with pytest.raises(Exception) as exception_info:
            checker_obj_with_only_read_mocked.check_data_operations(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
                data=data,
            )
        assert str(exception_info.value) == expected_message
