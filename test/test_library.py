from typing import cast
import pytest
import pandas as pd
from main.library import Library
from test.utility.mocker.auditor_mocker import <PERSON><PERSON><PERSON>ocker, CheckerMocker
from mock import patch
from main.config.config_factory import ConfigFactory
from main.enums import Check
import numpy as np


@pytest.fixture(scope="class")
def library_obj():
    config = ConfigFactory("nse")
    del config.UNIVERSE_TO_IGNORE_CHECK_LIST["opt"]
    library_obj = Library(config, lib_name="nse/5_min/opt/trd")
    library_obj._Library__auditor = AuditorMocker(config)
    library_obj._Library__checker = CheckerMocker(config)
    all_users_data = pd.read_parquet("./test/utility/test_data/user_test_data.parquet")
    library_obj.user_data = all_users_data[all_users_data.username == "mantraraj"]
    library_obj.user_data = library_obj.user_data.reset_index()
    config.ALLOWED_SUDDEN_JUMP = 2.25

    def mock_check_overnight_sudden_jumps(*args, **kwargs):
        return "check_overnight_sudden_jumps:\nPassed\n\n"

    def mock_checker_read_metadata(*args, **kwargs):
        metadata_dict = {}
        for column in [
            "timestamp",
            "ID",
            "Open",
            "High",
            "Low",
            "Close",
            "Cons_Volume",
            "OI",
            "Open_int",
            "Vwap",
            "iv",
            "delta",
            "gamma",
            "theta",
            "vega",
        ]:
            metadata_dict[column] = {
                "nan_ratio": 0.2 if column not in ["timestamp", "ID"] else 0.0,
                "intraday_jump": 15.0,
                "overnight_open_jump": 15.0,
                "overnight_close_jump": 15.0,
            }

        metadata_dict["start_timestamp"] = metadata_dict[
            "last_timestamp"
        ] = "2025-03-30"

        return metadata_dict

    library_obj._Library__checker.checks_to_fxn_mapping[
        Check.CHECK_OVERNIGHT_SUDDEN_JUMPS
    ] = mock_check_overnight_sudden_jumps
    library_obj._Library__checker.read_metadata = mock_checker_read_metadata

    yield library_obj


@pytest.fixture(scope="function")
def df():
    df = pd.read_parquet("./test/utility/test_data/library_test_data.parquet")
    return df


@pytest.mark.usefixtures("library_obj")
class TestLibrary:
    def test_library_list_file_locations_success(self, library_obj: Library):
        library_obj._Library__lib_name = library_obj._Library__config.META_DATA_LIBRARY
        expected_file_locations = [
            "nse/5_min/opt/trd",
            "nse/5_min/opt/ord",
        ]
        output_file_locations = library_obj.list_file_locations()
        library_obj._Library__lib_name = "nse/5_min/opt/trd"
        assert output_file_locations == expected_file_locations

    def test_library_list_file_locations_raise_exception(self, library_obj: Library):
        expected_message = "Permission denied: not a valid call"
        with pytest.raises(Exception) as exception_info:
            library_obj.list_file_locations()
        assert str(exception_info.value) == expected_message

    def test_library_list_file_locations_exception(self, library_obj: Library):
        original_lib_name = library_obj._Library__lib_name
        library_obj._Library__lib_name = library_obj._Library__config.META_DATA_LIBRARY

        def mock_list_file_locations(*args, **kwargs):
            raise Exception("Error listing file locations")

        with patch.object(
            library_obj._Library__auditor,
            "list_file_locations",
            mock_list_file_locations,
        ):
            expected_message = "An error occurred while listing the file locations from store StorageType.DB: Exception('Error listing file locations')"
            with pytest.raises(Exception) as exception_info:
                library_obj.list_file_locations()

            assert str(exception_info.value) == expected_message
        library_obj._Library__lib_name = original_lib_name

    def test_library_list_symbols_success(self, library_obj: Library):
        expected_list_symbols = ["5001", "5002", "5003", "5008"]
        output_list_symbols = library_obj.list_symbols()
        assert output_list_symbols == expected_list_symbols

    def test_library_list_symbols_authorization_raise_exception(
        self, library_obj: Library
    ):
        library_obj._Library__lib_name = "restricted_library"
        expected_message = f"Permission denied: user not authorize to access {library_obj._Library__lib_name}"
        with pytest.raises(Exception) as exception_info:
            library_obj.list_symbols()
        library_obj._Library__lib_name = "nse/5_min/opt/trd"
        assert str(exception_info.value) == expected_message

    def test_library_read_without_daterange_success(self, library_obj: Library):
        read_output = cast(pd.DataFrame, library_obj.read(symbol="symbol"))
        assert read_output.iloc[0]["ID"] == 147502023012505001

    def test_library_read_with_daterange_success(self, library_obj: Library):
        read_output = cast(
            pd.DataFrame,
            library_obj.read(
                symbol="symbol",
                start_date=pd.Timestamp("2023-08-31"),
                end_date=pd.Timestamp("2023-09-07"),
            ),
        )
        assert read_output.iloc[0]["ID"] == 147502023012505001

    def test_library_read_raise_exception(self, library_obj: Library):
        expected_message = "ReadError: Found error during reading invalid_symbol at nse/5_min/opt/trd from store StorageType.DB: Exception('Error reading invalid_symbol at nse/5_min/opt/trd')"
        with pytest.raises(Exception) as exception_info:
            library_obj.read(symbol="invalid_symbol")
        assert str(exception_info.value) == expected_message

    def test_library_read_date_range_error(self, library_obj: Library):
        with pytest.raises(Exception) as exception_info:
            library_obj.read(symbol="symbol_date_range_error")
        assert (
            str(exception_info.value)
            == "DateRangeError: start_date can't be more than end_date"
        )

    def test_library_read_authorization_raise_exception(self, library_obj: Library):
        library_obj._Library__lib_name = "invalid_library"
        excepted_message = f"Permission denied: user not authorize to access {library_obj._Library__lib_name}"
        with pytest.raises(Exception) as exception_info:
            library_obj.read(symbol="symbol")
        library_obj._Library__lib_name = "nse/5_min/opt/trd"
        assert str(exception_info.value) == excepted_message

    def test_library_append_success(self, library_obj: Library, df: pd.DataFrame):
        append_call = library_obj.append(symbol="symbol", data=df)
        assert append_call == True

    def test_library_append_during_checks_raise_exception(
        self, library_obj: Library, df: pd.DataFrame
    ):
        df["Close"] = df["iv"] = np.nan
        expected_message = "DataChecksFailed: Data inconsistencies found for the symbol: symbol.\ncheck_columns_set:\nPassed\n\ncheck_columns_dtype:\nPassed\n\ncheck_nan_entries:\nFound NaN values > 30.000000000000004% for 2 dates for the column: Close, with highest NaN count of 100.0% on 2025-04-02\nFound NaN values > 30.000000000000004% for 2 dates for the column: iv, with highest NaN count of 100.0% on 2025-04-02\n\ncheck_duplicate_entries:\nPassed\n\ncheck_monotonic_nature:\nPassed\n\ncheck_all_dates:\nPassed\n\ncheck_all_timestamps:\nPassed\n\ncheck_forward_fill:\nPassed\n\ncheck_OHLC:\nPassed\n\ncheck_intraday_sudden_jumps:\nPassed\n\ncheck_overnight_sudden_jumps:\nPassed\n\n"

        with pytest.raises(Exception) as exception_info:
            library_obj.append(symbol="symbol", data=df)
        assert str(exception_info.value).count("\n") == 35
        assert str(exception_info.value) == expected_message

    def test_library_append_after_checks_raise_exception(
        self, library_obj: Library, df: pd.DataFrame
    ):
        expected_message = "AppendError: Found error during appending invalid_append_symbol at nse/5_min/opt/trd from store StorageType.DB: Exception('Error updating metadata for invalid_append_symbol at nse/5_min/opt/trd')"
        with pytest.raises(Exception) as exception_info:
            library_obj.append(symbol="invalid_append_symbol", data=df)
        assert str(exception_info.value) == expected_message

    def test_library_append_authorization_raise_exception(
        self, library_obj: Library, df: pd.DataFrame
    ):
        library_obj._Library__lib_name = "invalid_library"
        expected_message = f"Permission denied: user not authorize to write/update {library_obj._Library__lib_name}"
        with pytest.raises(Exception) as exception_info:
            library_obj.append(symbol="symbol", data=df)
        library_obj._Library__lib_name = "nse/5_min/opt/trd"
        assert str(exception_info.value) == expected_message

    def test_Library_append_raise_exception(
        self, library_obj: Library, df: pd.DataFrame
    ):
        df["Close"] = df["iv"] = np.nan
        expected_message = "DataChecksFailed: Data inconsistencies found for the symbol: symbol.\ncheck_columns_set:\nPassed\n\ncheck_columns_dtype:\nPassed\n\ncheck_nan_entries:\nFound NaN values > 30.000000000000004% for 2 dates for the column: Close, with highest NaN count of 100.0% on 2025-04-02\nFound NaN values > 30.000000000000004% for 2 dates for the column: iv, with highest NaN count of 100.0% on 2025-04-02\n\ncheck_duplicate_entries:\nPassed\n\ncheck_monotonic_nature:\nPassed\n\ncheck_all_dates:\nPassed\n\ncheck_all_timestamps:\nPassed\n\ncheck_forward_fill:\nPassed\n\ncheck_OHLC:\nPassed\n\ncheck_intraday_sudden_jumps:\nPassed\n\ncheck_overnight_sudden_jumps:\nPassed\n\n"

        with pytest.raises(Exception) as exception_info:
            library_obj.append(symbol="symbol", data=df)
        assert str(exception_info.value) == expected_message

    def test_library_update_success(self, library_obj: Library, df: pd.DataFrame):
        update_call = library_obj.update(symbol="symbol", data=df)
        assert update_call == True

    def test_library_update_during_checks_raise_exception(
        self, library_obj: Library, df: pd.DataFrame
    ):
        df["Close"] = df["iv"] = np.nan
        expected_message = "DataChecksFailed: Data inconsistencies found for the symbol: symbol.\ncheck_columns_set:\nPassed\n\ncheck_columns_dtype:\nPassed\n\ncheck_nan_entries:\nFound NaN values > 30.000000000000004% for 2 dates for the column: Close, with highest NaN count of 100.0% on 2025-04-02\nFound NaN values > 30.000000000000004% for 2 dates for the column: iv, with highest NaN count of 100.0% on 2025-04-02\n\ncheck_duplicate_entries:\nPassed\n\ncheck_monotonic_nature:\nPassed\n\ncheck_all_dates:\nPassed\n\ncheck_all_timestamps:\nPassed\n\ncheck_forward_fill:\nPassed\n\ncheck_OHLC:\nPassed\n\ncheck_intraday_sudden_jumps:\nPassed\n\ncheck_overnight_sudden_jumps:\nPassed\n\n"
        with pytest.raises(Exception) as exception_info:
            library_obj.update(symbol="symbol", data=df)
        assert str(exception_info.value).count("\n") == 35
        assert str(exception_info.value) == expected_message

    def test_library_update_after_checks_raise_exception(
        self, library_obj: Library, df: pd.DataFrame
    ):
        expected_message = "UpdateError: Found error during updating invalid_update_symbol at nse/5_min/opt/trd from store StorageType.DB: Exception('Error updating metadata for invalid_update_symbol at nse/5_min/opt/trd')"
        with pytest.raises(Exception) as exception_info:
            library_obj.update(symbol="invalid_update_symbol", data=df)
        assert str(exception_info.value) == expected_message

    def test_library_update_authorization_raise_exception(
        self, library_obj: Library, df: pd.DataFrame
    ):
        library_obj._Library__lib_name = "invalid_library"
        expected_message = f"Permission denied: user not authorize to write/update {library_obj._Library__lib_name}"
        with pytest.raises(Exception) as exception_info:
            library_obj.update(symbol="symbol", data=df)
        library_obj._Library__lib_name = "nse/5_min/opt/trd"
        assert str(exception_info.value) == expected_message

    def test_Library_update_raise_exception(
        self, library_obj: Library, df: pd.DataFrame
    ):
        df["Close"] = df["iv"] = np.nan
        expected_message = "DataChecksFailed: Data inconsistencies found for the symbol: symbol.\ncheck_columns_set:\nPassed\n\ncheck_columns_dtype:\nPassed\n\ncheck_nan_entries:\nFound NaN values > 30.000000000000004% for 2 dates for the column: Close, with highest NaN count of 100.0% on 2025-04-02\nFound NaN values > 30.000000000000004% for 2 dates for the column: iv, with highest NaN count of 100.0% on 2025-04-02\n\ncheck_duplicate_entries:\nPassed\n\ncheck_monotonic_nature:\nPassed\n\ncheck_all_dates:\nPassed\n\ncheck_all_timestamps:\nPassed\n\ncheck_forward_fill:\nPassed\n\ncheck_OHLC:\nPassed\n\ncheck_intraday_sudden_jumps:\nPassed\n\ncheck_overnight_sudden_jumps:\nPassed\n\n"

        with pytest.raises(Exception) as exception_info:
            library_obj.update(symbol="symbol", data=df)
        assert str(exception_info.value) == expected_message

    def test_library_write_success(self, library_obj: Library, df: pd.DataFrame):
        write_call = library_obj.write(symbol="symbol", data=df)
        assert write_call == True

    def test_library_write_during_checks_raise_exception(
        self, library_obj: Library, df: pd.DataFrame
    ):
        df["Close"] = df["iv"] = np.nan
        expected_message = "DataChecksFailed: Data inconsistencies found for the symbol: symbol.\ncheck_columns_set:\nPassed\n\ncheck_columns_dtype:\nPassed\n\ncheck_nan_entries:\nFound NaN values > 30.000000000000004% for 2 dates for the column: Close, with highest NaN count of 100.0% on 2025-04-02\nFound NaN values > 30.000000000000004% for 2 dates for the column: iv, with highest NaN count of 100.0% on 2025-04-02\n\ncheck_duplicate_entries:\nPassed\n\ncheck_monotonic_nature:\nPassed\n\ncheck_all_dates:\nPassed\n\ncheck_all_timestamps:\nPassed\n\ncheck_forward_fill:\nPassed\n\ncheck_OHLC:\nPassed\n\ncheck_intraday_sudden_jumps:\nPassed\n\ncheck_overnight_sudden_jumps:\nPassed\n\n"
        with pytest.raises(Exception) as exception_info:
            library_obj.write(symbol="symbol", data=df)
        assert str(exception_info.value).count("\n") == 35
        assert str(exception_info.value) == expected_message

    def test_library_write_after_checks_raise_exception(
        self, library_obj: Library, mocker
    ):
        expected_message = "WriteError: Found error during writing invalid_symbol at nse/5_min/opt/trd from store StorageType.DB: Exception('Error updating metadata for invalid_symbol at nse/5_min/opt/trd')"

        def mock_check_data_operations(*args, **kwargs):
            pass

        mocker.patch(
            "main.data.checker.Checker.check_data_operations",
            mock_check_data_operations,
        )

        with pytest.raises(Exception) as exception_info:
            library_obj.write(symbol="invalid_symbol", data=pd.DataFrame())
        assert str(exception_info.value) == expected_message

    def test_Library_write_raise_exception(
        self, library_obj: Library, df: pd.DataFrame
    ):
        df["Close"] = df["iv"] = np.nan
        expected_message = "DataChecksFailed: Data inconsistencies found for the symbol: symbol.\ncheck_columns_set:\nPassed\n\ncheck_columns_dtype:\nPassed\n\ncheck_nan_entries:\nFound NaN values > 30.000000000000004% for 2 dates for the column: Close, with highest NaN count of 100.0% on 2025-04-02\nFound NaN values > 30.000000000000004% for 2 dates for the column: iv, with highest NaN count of 100.0% on 2025-04-02\n\ncheck_duplicate_entries:\nPassed\n\ncheck_monotonic_nature:\nPassed\n\ncheck_all_dates:\nPassed\n\ncheck_all_timestamps:\nPassed\n\ncheck_forward_fill:\nPassed\n\ncheck_OHLC:\nPassed\n\ncheck_intraday_sudden_jumps:\nPassed\n\ncheck_overnight_sudden_jumps:\nPassed\n\n"

        with pytest.raises(Exception) as exception_info:
            library_obj.write(symbol="symbol", data=df)
        assert str(exception_info.value) == expected_message

    def test_library_write_authorization_raise_exception(
        self, library_obj: Library, df: pd.DataFrame
    ):
        library_obj._Library__lib_name = "invalid_library"
        expected_message = f"Permission denied: user not authorize to write/update {library_obj._Library__lib_name}"
        with pytest.raises(Exception) as exception_info:
            library_obj.write(symbol="symbol", data=df)
        library_obj._Library__lib_name = "nse/5_min/opt/trd"
        assert str(exception_info.value) == expected_message

    def test_library_write_with_corpact_applied(
        self, library_obj: Library, df: pd.DataFrame
    ):
        result = library_obj.write(
            symbol="symbol_corpact_applied", data=df, comment="corpact_applied"
        )
        assert result is True

    def test_library_read_metadata_success(self, library_obj: Library):
        metadata = library_obj.read_metadata(symbol="symbol")
        expected_metadata = {
            "length": "300",
            "start_timestamp": "2023-01-01",
            "last_timestamp": "2023-01-31",
        }
        assert metadata is not None
        assert "start_timestamp" in metadata
        assert "last_timestamp" in metadata
        assert metadata == expected_metadata

    def test_library_read_metadata_raise_exception(self, library_obj: Library):
        expected_message = "ReadMetadataError: Found error during reading metadata for the invalid_symbol at nse/5_min/opt/trd from store StorageType.DB: Exception('Error reading metadata for invalid_symbol at nse/5_min/opt/trd')"
        with pytest.raises(Exception) as exception_info:
            library_obj.read_metadata(symbol="invalid_symbol")
        assert str(exception_info.value) == expected_message

    def test_library_write_metadata_success(
        self, library_obj: Library, mocker, df: pd.DataFrame
    ):
        result = library_obj.write_metadata(symbol="symbol", data=df)
        assert result is True

    def test_library_write_metadata_raise_exception(
        self, library_obj: Library, df: pd.DataFrame
    ):
        expected_message = "WriteMetadataError: Found error during writing metadata for the invalid_symbol at nse/5_min/opt/trd from store StorageType.DB: Exception('Error writing metadata for invalid_symbol at nse/5_min/opt/trd')"

        with pytest.raises(Exception) as exception_info:
            library_obj.write_metadata(symbol="invalid_symbol", data=df)
        assert str(exception_info.value) == expected_message
