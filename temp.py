# import pandas as pd
# import time
# from main.config.config_NSE import ConfigNS<PERSON>
# from main.tanki import Tanki
# from main.data.checker_fixer import Checker_Fixer
# from main.data.checker import Checker
# from main.config.config_factory import ConfigFactory
# from main.enums import StorageType
# from multiprocessing import Pool
# from main.data.auditor import Auditor
# from main.data.operation import Operator

# # load -> checker -> fixer -> checker


# library = "nse/1_min/optstk/ord"
# library_split = library.split("/")
# exchange = library_split[0]
# universe = library_split[2]
# dtype = library_split[-1]
# symbol = "182"
# date_range = "20220101_20240316"

# # load
# print("\nStarted data loading...")

# data1 = pd.read_parquet(
#     f"optstk_182_testing.parquet"
# )


# tanki = Tanki(exchange_type=exchange)
# tanki.login(username="mantraraj", password="mantraraj")
# config = ConfigFactory(exchange_type=exchange)
# checker_fixer = Checker_Fixer(config=config)
# response = tanki.check_data(storage_type=StorageType.DB, file_location=library, file_name=symbol,data=data1)

# print(response)
# checker = Checker(config=config)
# check1 = checker.check_overnight_sudden_jumps(
#     storage_type=StorageType.DB, file_location=library, file_name=symbol, data=data1
# )
# print(check1)
# check2=checker.check_intraday_sudden_jumps(storage_type=StorageType.DB, file_location=library, file_name=symbol, data=data1)
# print(check2)
# print()
# # report = "check_columns_set:\nPassed\n\ncheck_columns_dtype:\nPassed\n\ncheck_nan_entries:\nMetadataReadError: Found error during reading metadata_dict from 5001 located at nse/1_min/raw_opt/trd inside store: StorageType.DB\n\ncheck_duplicate_entries:\nPassed\n\ncheck_monotonic_nature:\nPassed\n\ncheck_all_dates:\nMetadata is none or doesn't have last_timestamp for nse/1_min/raw_opt/trd with symbol = 5001\n\ncheck_all_timestamps:\nFound 149 missing times\n\ncheck_forward_fill:\nPassed\n\ncheck_OHLC:\nFound 76756 invalid OHLC entries\nThe average Open-Close percentage difference is 0.0%\nThe average Low-High percentage difference is 2.962829543673004%\nThe highest Open-Close percentage difference is found 0.0% for the id 122002020010215001 on 2020-01-01 09:16:00\nThe highest Low-High percentage difference is found 1560900.0% for the id 113002020082015001 on 2020-08-19 14:27:00\n\ncheck_intraday_sudden_jumps:\nMetadataReadError: Found error during reading metadata_dict from 5001 located at nse/1_min/raw_opt/trd inside store: StorageType.DB\nFound 15509137 rows with sudden jump >= -10.0% for the column: Open with highest jump 14763.999999999998% on 2020-08-19\nFound 15509137 rows with sudden jump >= -10.0% for the column: Low with highest jump 15229.0% on 2020-08-19\nFound 15509137 rows with sudden jump >= -10.0% for the column: High with highest jump 558.0% on 2020-05-13\nFound 15509137 rows with sudden jump >= -10.0% for the column: Close with highest jump 14763.999999999998% on 2020-08-19\n\ncheck_overnight_sudden_jumps:\nMetadata is none or doesn't have last_timestamp for nse/1_min/raw_opt/trd with symbol = 5001\nFound 81767 sudden jumps >= -10.0% in previous day last values to current day last values for the column: Open with highest jump: 14742.857142857141% on 2020-03-12\nFound 81767 sudden jumps >= -10.0% in previous day last values to current day first values for the column: Open with highest jump: 6000.0% on 2020-06-05\nFound 81767 sudden jumps >= -10.0% in previous day last values to current day last values for the column: Low with highest jump: 14742.857142857141% on 2020-03-12\nFound 81767 sudden jumps >= -10.0% in previous day last values to current day first values for the column: Low with highest jump: 6000.0% on 2020-06-05\nFound 81767 sudden jumps >= -10.0% in previous day last values to current day last values for the column: High with highest jump: 9036.956521739132% on 2020-03-12\nFound 81767 sudden jumps >= -10.0% in previous day last values to current day first values for the column: High with highest jump: 6000.0% on 2020-06-05\nFound 81767 sudden jumps >= -10.0% in previous day last values to current day last values for the column: Close with highest jump: 14742.857142857141% on 2020-03-12\nFound 81767 sudden jumps >= -10.0% in previous day last values to current day first values for the column: Close with highest jump: 6000.0% on 2020-06-05\n\n"
# # checker_fixer.check_data(
# #     storage_type=StorageType.DB,
# #     file_location=library,
# #     file_name=symbol,
# #     checker_report=report,
# #     data=data1,
# # )
# checker.check_forward_fill(StorageType.DB, library, symbol, data1)
# checker_fixer.check_OHLC_fixer(storage_type=StorageType.DB, file_location=library, file_name=symbol, data=data1)
print("")

# # # # import pandas as pd
# # # # import time
# # # # from main.tanki import Tanki
# # # # from main.data.checker_fixer import Checker_Fixer
# # # # from main.config.config_factory import ConfigFactory
# # # # from main.enums import StorageType
# # # # from multiprocessing import Pool

# # # # library = "nse/1_min/raw_opt/trd"
# # # # library_split = library.split("/")
# # # # exchange = library_split[0]
# # # # universe = library_split[2]
# # # # dtype = library_split[-1]
# # # # symbol = "5001"
# # # # date_range = "20220101_20230101"

# # # # # load
# # # # print("\nStarted data loading...")
# # # # start_time = time.time()
# # # # data = pd.read_parquet(
# # # #     f"/home/<USER>/jupyter/opt_1min_trd/{symbol}_{date_range}.parquet"
# # # # )
# # # # end_time = time.time()
# # # # print(f"Successfully loaded data with time: {end_time - start_time}\n")

# # # # tanki = Tanki(exchange_type=exchange)
# # # # tanki.login(username="mantraraj", password="mantraraj")
# # # # config = ConfigFactory(exchange_type=exchange)
# # # # checker_fixer = Checker_Fixer(config=config)

# # # # data["Open"] = data["Close"]
# # # # data = data[config.COLUMNS_DICT[universe][1:]]

# # # # if symbol == "5003":
# # # #     data["ID"] = data["ID"].astype("uint64")
# # # #     data["ID"] = data["ID"] // 10000
# # # #     data["ID"] = data["ID"] * 10000
# # # #     data["ID"] = data["ID"] + 5003

# # # # # check
# # # # print("Started checker...")
# # # # start_time = time.time()
# # # # checker_report = tanki.check_data(
# # # #     storage_type=StorageType.DB,
# # # #     file_location=library,
# # # #     file_name=symbol,
# # # #     data=data,
# # # # )
# # # # end_time = time.time()
# # # # print(f"Successfully completed checker with time: {end_time-start_time}\n")
# # # # with open(f"{universe}/{dtype}/{symbol}/{date_range}_checker_report.txt", "w") as file:
# # # #     file.write(checker_report)

# # # # print(f"Initial checker_report:\n\n{checker_report}")

# # # # final_checker_report = checker_report

# # # # # fix
# # # # if checker_report.count("Passed") != config.TOTAL_CHECKS:
# # # #     print("Started fixer...")
# # # #     start_time = time.time()
# # # #     (
# # # #         fixer_report,
# # # #         data,
# # # #         missing_dates,
# # # #         missing_date_to_times,
# # # #         date_to_id,
# # # #     ) = checker_fixer.check_data(
# # # #         storage_type=StorageType.DB,
# # # #         file_location=library,
# # # #         file_name=symbol,
# # # #         checker_report=checker_report,
# # # #         data=data,
# # # #     )
# # # #     end_time = time.time()
# # # #     print(f"Successfully completed fixer with time: {end_time-start_time}\n")
# # # #     with open(
# # # #         f"{universe}/{dtype}/{symbol}/{date_range}_fixer_report.txt", "w"
# # # #     ) as file:
# # # #         file.write(fixer_report)

# # # #     if len(missing_dates) > 0:
# # # #         missing_dates_df = pd.DataFrame(missing_dates, columns=["missing_dates"])
# # # #         missing_dates_df.to_csv(
# # # #             f"{universe}/{dtype}/{symbol}/{date_range}_missing_dates.csv"
# # # #         )

# # # #     if len(missing_date_to_times) > 0:
# # # #         for date in missing_date_to_times.keys():
# # # #             missing_date_to_times[date] = list(missing_date_to_times[date])
# # # #         missing_date_to_times_df = pd.DataFrame()
# # # #         missing_date_to_times_df["date"] = missing_date_to_times.keys()
# # # #         missing_date_to_times_df["missing_times"] = list(missing_date_to_times.values())
# # # #         missing_date_to_times_df.to_csv(
# # # #             f"{universe}/{dtype}/{symbol}/{date_range}_missing_times.csv"
# # # #         )

# # # #     if len(date_to_id) > 0:
# # # #         timestamp_id_df = pd.DataFrame(date_to_id, columns=["Date", "ID"])
# # # #         timestamp_id_df = timestamp_id_df.drop_duplicates()
# # # #         timestamp_id_df.to_csv(
# # # #             f"{universe}/{dtype}/{symbol}/{date_range}_date_to_id.csv"
# # # #         )

# # # #     # check
# # # #     print("Again started checker...")
# # # #     start_time = time.time()
# # # #     final_checker_report = tanki.check_data(
# # # #         storage_type=StorageType.DB,
# # # #         file_location=library,
# # # #         file_name=symbol,
# # # #         data=data,
# # # #     )
# # # #     end_time = time.time()
# # # #     print(f"Again successfully completed checker with time: {end_time-start_time}\n")
# # # #     with open(
# # # #         f"{universe}/{dtype}/{symbol}/{date_range}_final_checker_report.txt", "w"
# # # #     ) as file:
# # # #         file.write(final_checker_report)

# # # # print(f"Final checker_report:\n\n{final_checker_report}")

# # # # data["Open"] = data.groupby(["ID", data.index.date])["Close"].shift(1)
# # # # data["Open"] = data["Open"].fillna(data["Close"])
# # # # data.to_parquet(
# # # #     f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}/{date_range}_fixed.parquet"
# # # # )


# # # # import os

# # # # # Define the directory path
# # # # directory = '/home/<USER>/repos/data_auditing/raw_opt/trd/5001'

# # # # # Function to delete all files within a folder
# # # # def delete_files_in_folder(folder_path):
# # # #     for filename in os.listdir(folder_path):
# # # #         file_path = os.path.join(folder_path, filename)
# # # #         if os.path.isfile(file_path):
# # # #             os.unlink(file_path)

# # # # # Iterate over all folders within the /trd directory
# # # # for folder_name in os.listdir(directory):
# # # #     folder_path = os.path.join(directory, folder_name)
# # # #     print(folder_path)
# # # #     if os.path.isdir(folder_path):
# # # #         delete_files_in_folder(folder_path)


# # # # print("All files within the folders in /trd have been deleted.")


# # # import datetime
# # # import calendar
# # # import numpy as np
# # # import pandas as pd
# # # import os
# # # import time
# # # from multiprocessing import Pool

# # # SYMBOL_TO_NAME_DICT = {
# # #     "5001": "NIFTY",
# # #     "5002": "BANKNIFTY",
# # #     "5003": "FINNIFTY",
# # #     "5007": "INDIAVIX",
# # #     "5008": "MIDCPNIFTY",
# # # }
# # # NAME_TO_SYMBOL_DICT = {
# # #     "NIFTY": "5001",
# # #     "BANKNIFTY": "5002",
# # #     "FINNIFTY": "5003",
# # #     "INDIAVIX": "5007",
# # #     "MIDCPNIFTY": "5008",
# # # }
# # # MARKET_HOUR_DATA_DICT = {
# # #     "opt": {"start": "09:16", "end": "15:30"},
# # #     "futidx": {"start": "09:16", "end": "15:30"},
# # #     "raw_opt": {"start": "9:15", "end": "15:30"},
# # # }
# # # COLUMNS_TO_FETCH = {
# # #     "futidx": ["Open", "High", "Low", "Close", "ID"],
# # #     "raw_opt_trd": ["Open", "High", "Low", "Close", "Cons_Volume", "ID"],
# # # }
# # # SYMBOL_TO_ERROR = dict()
# # # COLUMNS = {"raw_opt": ["ID", "Open", "High", "Low", "Close", "Vwap", "Cons_Volume"]}
# # # COLUMN_TO_DTYPE = {
# # #     "raw_opt": {
# # #         "ID": "uint64",
# # #         "Open": "float64",
# # #         "High": "float64",
# # #         "Low": "float64",
# # #         "Close": "float64",
# # #         "Vwap": "float64",
# # #         "Cons_Volume": "float64",
# # #     }
# # # }


# # # # returns paths of all contracts for the given dates
# # # def get_date_paths(dates, symbol, dtype):
# # #     root_trd_date_paths = []
# # #     path1 = f"/mnt/hft_tick_data/root_{dtype}/"

# # #     for date in dates:
# # #         date = date[-2:] + date[5:7] + date[:4]
# # #         path2 = path1 + f"{date}/{symbol}/"
# # #         try:
# # #             for expiry in os.listdir(path2):
# # #                 path3 = path2 + f"{expiry}/"
# # #                 for dtype in os.listdir(path3):
# # #                     if dtype == "FF":
# # #                         continue
# # #                     path4 = path3 + f"{dtype}/"
# # #                     for contract in os.listdir(path4):
# # #                         path5 = path4 + f"{contract}"
# # #                         root_trd_date_paths.append(path5)
# # #         except Exception as e:
# # #             SYMBOL_TO_ERROR[f"Found error for {date}"] = e
# # #             continue

# # #     return root_trd_date_paths


# # # def __parse_contract(contract: str):
# # #     symbol = option_type = expiry = strike = ""

# # #     all_months_abbr = list(calendar.month_abbr)[1:]
# # #     for month in all_months_abbr:
# # #         t_month = f"-{month}-"
# # #         if t_month in contract:
# # #             ind = contract.index(t_month)
# # #             symbol = contract[: ind - 2]
# # #             expiry = contract[ind - 2 : ind + 9]
# # #             option_type = contract[ind + 9 : ind + 11]
# # #             strike = contract[ind + 11 :]
# # #             break

# # #     if symbol == "":
# # #         symbol = contract

# # #     return symbol, expiry, option_type, strike


# # # def __get_id(contract: str):
# # #     symbol, expiry, option_type, strike = __parse_contract(contract=contract)
# # #     if strike == "":
# # #         return -1
# # #     symbol = NAME_TO_SYMBOL_DICT[symbol]
# # #     expiry = datetime.datetime.strptime(expiry, "%d-%b-%Y").strftime("%Y%m%d")
# # #     option_type = "0" if option_type == "PE" else "1"
# # #     strike = strike[:-2]

# # #     id_str = strike + expiry + option_type + symbol
# # #     id_uint64 = np.uint64(id_str)
# # #     return id_uint64


# # # def _get_raw_trd_data_derivative(path: str):
# # #     path_split = path.split("/")[1:]
# # #     date_str = path_split[3]
# # #     contract = path_split[-1][:-4]

# # #     tick_data_csv = pd.read_csv(path, header=None)
# # #     tick_data_csv = tick_data_csv[(tick_data_csv > 0).all(axis=1)]

# # #     tick_data_csv["timestamp"] = pd.to_datetime(
# # #         tick_data_csv[0],
# # #         unit="s",
# # #         origin=pd.Timestamp(
# # #             day=int(date_str[0:2]),
# # #             month=int(date_str[2:4]),
# # #             year=int(date_str[4:]),
# # #         ),
# # #     )
# # #     tick_data_csv = tick_data_csv.set_index("timestamp")

# # #     tick_data_csv = tick_data_csv.rename(
# # #         columns={0: "time_in_seconds_count", 2: "ltp", 1: "Cons_Volume"}
# # #     )
# # #     tick_data_csv["Cons_Volume"] = tick_data_csv["Cons_Volume"].astype("float64")

# # #     tick_data_csv = tick_data_csv[tick_data_csv["ltp"] > 0]
# # #     tick_data_csv["ltp"] = tick_data_csv["ltp"] / 100

# # #     tick_data_csv = tick_data_csv.resample("1T", closed="right", label="right").agg(
# # #         {"ltp": ["first", "max", "min", "last"], "Cons_Volume": "sum"}
# # #     )[["ltp", "Cons_Volume"]]

# # #     tick_data_csv = pd.concat(
# # #         [tick_data_csv["ltp"], tick_data_csv["Cons_Volume"]], axis=1
# # #     )

# # #     tick_data_csv = tick_data_csv.dropna()

# # #     tick_data_csv["ID"] = __get_id(contract=contract)
# # #     tick_data_csv.columns = COLUMNS_TO_FETCH["raw_opt_trd"]

# # #     return tick_data_csv


# # # UNIVERSE_TO_PATH_FUNCTION = {"raw_opt_trd": _get_raw_trd_data_derivative}


# # # def get_missing_data_from_path(
# # #     file_location: str,
# # #     file_name: str,
# # #     path: str,
# # # ):
# # #     universe = file_location.split("/")[2]
# # #     dtype = file_location.split("/")[-1]
# # #     universe_dtype = universe
# # #     if "raw" in universe:
# # #         universe_dtype = f"{universe}_{dtype}"

# # #     missing_data = pd.DataFrame(columns=COLUMNS[universe])
# # #     missing_data = missing_data.astype(dtype=COLUMN_TO_DTYPE[universe])
# # #     missing_data.index.name = "timestamp"

# # #     path_split = path.split("/")[1:]
# # #     date_str = path_split[3]
# # #     date = datetime.datetime.strptime(date_str, "%d%m%Y").date()
# # #     year = date.year
# # #     month = date.month
# # #     day = date.day

# # #     try:
# # #         external_data_csv = UNIVERSE_TO_PATH_FUNCTION[universe_dtype](path)
# # #         external_data_df = pd.DataFrame(columns=missing_data.columns)

# # #         if external_data_csv is None or len(external_data_csv) == 0:
# # #             SYMBOL_TO_ERROR[
# # #                 f"{file_name}_{year}{month:02d}{day:02d}"
# # #             ] = "EmptyDataError"
# # #             return

# # #         start_time = datetime.datetime.strptime(
# # #             MARKET_HOUR_DATA_DICT[universe]["start"], "%H:%M"
# # #         ).time()
# # #         end_time = datetime.datetime.strptime(
# # #             MARKET_HOUR_DATA_DICT[universe]["end"], "%H:%M"
# # #         ).time()
# # #         external_data_csv = external_data_csv[
# # #             (external_data_csv.index.time >= start_time)
# # #             & (external_data_csv.index.time <= end_time)
# # #         ]

# # #         for column in COLUMNS_TO_FETCH[universe_dtype]:
# # #             external_data_df[column] = external_data_csv[column]

# # #         external_data_df.index.name = missing_data.index.name

# # #         for column in list(
# # #             set(COLUMNS[universe]) - set(COLUMNS_TO_FETCH[universe_dtype]) - {"ID"}
# # #         ):
# # #             external_data_df[column] = 0.0
# # #         external_data_df = external_data_df.astype(dtype=missing_data.dtypes)

# # #         missing_data = external_data_df.reset_index()
# # #     except pd.errors.EmptyDataError:
# # #         SYMBOL_TO_ERROR[f"{file_name}_{year}{month:02d}{day:02d}"] = "EmptyDataError"
# # #         print(
# # #             f"MissingDataError: found error for the symbol {file_name} and date {date}: EmptyDataError"
# # #         )
# # #     except Exception as e:
# # #         SYMBOL_TO_ERROR[f"{file_name}_{year}{month:02d}{day:02d}"] = e
# # #         print(
# # #             f"MissingDateError: found error for the symbol {file_name} and date {date}: {e}"
# # #         )

# # #     return missing_data


# # # def get_all_paths(date_range_tuple):

# # #     file_location = "nse/1_min/raw_opt/trd"
# # #     file_location_split = file_location.split("/")
# # #     universe = file_location_split[2]
# # #     dtype = file_location_split[-1]
# # #     file_name = "5001"
# # #     date_range = f"{date_range_tuple[0].strftime('%Y%m%d')}_{date_range_tuple[1].strftime('%Y%m%d')}"
# # #     all_paths = []

# # #     missing_dates = set()

# # #     if os.path.exists(
# # #         f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{file_name}/fixed_with_external_source_2_missing_dates_csv/{date_range}_missing_dates.csv"
# # #     ):
# # #         missing_dates_csv = pd.read_csv(
# # #             f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{file_name}/fixed_with_external_source_2_missing_dates_csv/{date_range}_missing_dates.csv"
# # #         )
# # #         missing_dates.update(set(missing_dates_csv["missing_dates"]))

# # #     if os.path.exists(
# # #         f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{file_name}/fixed_with_external_source_2_missing_times_csv/{date_range}_missing_times.csv"
# # #     ):
# # #         missing_date_to_times_csv = pd.read_csv(
# # #             f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{file_name}/fixed_with_external_source_2_missing_times_csv/{date_range}_missing_times.csv"
# # #         )
# # #         missing_dates.update(set(missing_date_to_times_csv["date"]))

# # #     missing_dates = list(missing_dates)
# # #     all_paths = get_date_paths(
# # #         dates=missing_dates, symbol=SYMBOL_TO_NAME_DICT[file_name], dtype=dtype
# # #     )

# # #     with open(
# # #         f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{file_name}/missing_dates_tick_data_all_paths/{date_range}_all_paths.txt",
# # #         "w",
# # #     ) as file:
# # #         if len(all_paths) > 0:
# # #             for path in all_paths[:-1]:
# # #                 file.write(path + ",")
# # #             file.write(all_paths[-1])


# # # def get_all_paths_multiprocessing():
# # #     start_time = time.perf_counter()

# # #     date_range_list = []
# # #     for y in range(2007, 2024):
# # #         date_range_list.append((datetime.date(y, 1, 1), datetime.date(y + 1, 1, 1)))
# # #     date_range_list.append((datetime.date(2024, 1, 1), datetime.date(2024, 3, 16)))

# # #     with Pool() as p:
# # #         p.map(get_all_paths, date_range_list)

# # #     finish_time = time.perf_counter()

# # #     print(f"Program finished in {finish_time-start_time} seconds")


# # # # get_all_paths_multiprocessing()


# # # def get_data_from_paths(date_range_tuple):

# # #     file_location = "nse/1_min/raw_opt/trd"
# # #     file_location_split = file_location.split("/")
# # #     universe = file_location_split[2]
# # #     dtype = file_location_split[-1]
# # #     file_name = "5001"
# # #     date_range = f"{date_range_tuple[0].strftime('%Y%m%d')}_{date_range_tuple[1].strftime('%Y%m%d')}"

# # #     all_paths = []
# # #     with open(
# # #         f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{file_name}/missing_dates_tick_data_all_paths/{date_range}_all_paths.txt",
# # #         "r",
# # #     ) as file:
# # #         all_paths = file.read().split(",")

# # #     missing_data_list = []
# # #     for path in all_paths:
# # #         if len(path) == 0:
# # #             continue

# # #         missing_data_from_path = get_missing_data_from_path(
# # #             file_location=file_location, file_name=file_name, path=path
# # #         )
# # #         missing_data_list.append(missing_data_from_path)

# # #     missing_data_from_paths = pd.concat(missing_data_list)
# # #     missing_data_from_paths.to_parquet(
# # #         f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{file_name}/missing_dates_tick_data_parquets/{date_range}_missing_dates_tick_data.parquet"
# # #     )

# # # get_data_from_paths(date_range_tuple=(datetime.date(2008,1,1),datetime.date(2009,1,1)))

# # # def get_data_from_paths_multiprocessing():
# # #     start_time = time.perf_counter()

# # #     date_range_list = []
# # #     for y in range(2007, 2024):
# # #         date_range_list.append((datetime.date(y, 1, 1), datetime.date(y + 1, 1, 1)))
# # #     date_range_list.append((datetime.date(2024, 1, 1), datetime.date(2024, 3, 16)))

# # #     with Pool() as p:
# # #         p.map(get_data_from_paths, date_range_list)

# # #     finish_time = time.perf_counter()

# # #     print(f"Program finished in {finish_time-start_time} seconds")


# # # # get_data_from_paths_multiprocessing()

# # # file_location = "nse/1_min/raw_opt/trd"
# # # file_location_split = file_location.split("/")
# # # universe = file_location_split[2]
# # # dtype = file_location_split[-1]
# # # file_name = "5001"

# # # if len(SYMBOL_TO_ERROR) > 0:
# # #     symbol_to_error_df = pd.DataFrame()
# # #     symbol_to_error_df["symbol"] = SYMBOL_TO_ERROR.keys()
# # #     symbol_to_error_df["message"] = SYMBOL_TO_ERROR.values()
# # #     symbol_to_error_df.to_csv(
# # #         f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{file_name}/missing_dates_tick_data_error_messages.csv"
# # #     )
# # #     SYMBOL_TO_ERROR = dict()

# # # print()


# import datetime

# date_range_list = []
# for y in range(2007, 2024):
#     date_range_list.append((datetime.date(y, 1, 1), datetime.date(y + 1, 1, 1)))
# date_range_list.append((datetime.date(2024, 1, 1), datetime.date(2024, 3, 16)))

# import os

# # Example list of directory names

# # Base directory path
# base_directory = "/home/<USER>/repos/data_auditing/raw_opt/trd/5002/tick_data_exceed_jump_timestamp_to_id_parquets"

# # Iterate over the list of directory names
# for directory_name in date_range_list:
#     directory_name = (
#         f"{directory_name[0].strftime('%Y%m%d')}_{directory_name[1].strftime('%Y%m%d')}"
#     )
#     # Create the full path for the new directory
#     new_directory_path = os.path.join(base_directory, directory_name)

#     # Check if the directory already exists
#     if not os.path.exists(new_directory_path):
#         # If not, create the directory
#         os.makedirs(new_directory_path)
#         print(f"Created directory: {new_directory_path}")
#     else:
#         print(f"Directory already exists: {new_directory_path}")


# import datetime
# import pandas as pd
# import time
# from main.tanki import Tanki
# from main.data.checker_fixer import Checker_Fixer
# from main.config.config_factory import ConfigFactory
# from main.enums import StorageType
# from multiprocessing import Pool

# # load -> checker -> fixer -> checker


# def checker_cycle(date_range_tuple):
#     library = "nse/1_min/raw_opt/trd"
#     library_split = library.split("/")
#     exchange = library_split[0]
#     universe = library_split[2]
#     dtype = library_split[-1]
#     symbol = "5002"
#     date_range = f"{date_range_tuple[0].strftime('%Y%m%d')}_{date_range_tuple[1].strftime('%Y%m%d')}"

#     # load
#     print("\nStarted data loading...")
#     start_time = time.time()
#     data = pd.read_parquet(
#         f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}/fixed_with_tick_data_fixed_parquets/{date_range}_fixed_with_tick_data_fixed.parquet"
#     )
#     end_time = time.time()
#     print(f"Successfully loaded data with time: {end_time - start_time}\n")

#     config=ConfigFactory("nse")
#     checker_fixer = Checker_Fixer(config=config)


#     ohlc_percentage_exceed_date_to_id=checker_fixer.check_OHLC_fixer(storage_type=StorageType.DB,file_location=library,file_name=symbol,data=data)


#     if len(ohlc_percentage_exceed_date_to_id) > 0:
#         timestamp_id_df = pd.DataFrame(ohlc_percentage_exceed_date_to_id, columns=["Timestamp", "ID"])
#         timestamp_id_df = timestamp_id_df.drop_duplicates()
#         timestamp_id_df.to_csv(
#             f"{universe}/{dtype}/{symbol}/ohlc_percentage_exceed_date_to_id_csv/{date_range}_ohlc_percentage_exceed_date_to_id.csv"
#         )

# def checker_cycle_multiprocessing():
#     start_time = time.perf_counter()

#     date_range_list = []
#     for y in range(2007, 2024):
#         date_range_list.append((datetime.date(y, 1, 1), datetime.date(y + 1, 1, 1)))
#     date_range_list.append((datetime.date(2024, 1, 1), datetime.date(2024, 3, 16)))

#     with Pool() as p:
#         p.map(checker_cycle, date_range_list)

#     finish_time = time.perf_counter()

#     print(f"Program finished in {finish_time-start_time} seconds")


# checker_cycle_multiprocessing()

# print()

# import pandas as pd
# import datetime

# date_range_list = []
# for y in range(2007, 2024):
#     date_range_list.append((datetime.date(y, 1, 1), datetime.date(y + 1, 1, 1)))
# date_range_list.append((datetime.date(2024, 1, 1), datetime.date(2024, 3, 16)))

# library = "nse/1_min/raw_opt/trd"
# library_split = library.split("/")
# exchange = library_split[0]
# universe = library_split[2]
# dtype = library_split[-1]
# symbol = "5001"


# for date_range_tuple in date_range_list:
#     msg = ""

#     date_range = f"{date_range_tuple[0].strftime('%Y%m%d')}_{date_range_tuple[1].strftime('%Y%m%d')}"

#     # df = pd.read_csv(
#     #     f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}/ohlc_percentage_exceed_date_to_id_csv/{date_range}_ohlc_percentage_exceed_date_to_id.csv"
#     # )

#     data = pd.read_parquet(
#         f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}/fixed_with_tick_data_fixed_parquets/{date_range}_fixed_with_tick_data_fixed.parquet"
#     )
#     msg = f"Total data length: {len(data)}\n\n"

#     data["ocp"] = ((data["Close"] - data["Open"]) / data["Open"]) * 100
#     data["hlp"] = ((data["High"] - data["Low"]) / data["Low"]) * 100

#     ocp_Mean = data["ocp"].mean()
#     hlp_Mean = data["hlp"].mean()

#     # df["Timestamp"] = pd.to_datetime(df["Timestamp"])

#     # filtered_data = data[data.index.isin(df["Timestamp"]) & data["ID"].isin(df["ID"])]

#     # filtered_data = filtered_data.sort_values(by="hlp", ascending=False)

#     # filtered_data = filtered_data.reset_index()

#     # filt_without_time = filtered_data.drop(columns="timestamp")

#     oc_mean_100_se_bada_percent = ((data["ocp"] > ocp_Mean * 100).sum() * 100) / len(
#         data
#     )
#     hl_mean_100_se_bada_percent = ((data["hlp"] > hlp_Mean * 100).sum() * 100) / len(
#         data
#     )

#     msg += "Across row:\n"
#     msg += f"{oc_mean_100_se_bada_percent}% data for which open-close percentage change is 100 times greater than average open-close percentage\n"
#     msg += f"{hl_mean_100_se_bada_percent}% data for which low-high percentage change is 100 times greater than average low-high percentage\n\n"

#     for col in ["Open", "High", "Low", "Close"]:
#         data[f"{col}_pct_change"] = data.groupby("ID")[col].pct_change() * 100

#     average_jump_100 = (
#         data.groupby("ID")[
#             ["Open_pct_change", "High_pct_change", "Low_pct_change", "Close_pct_change"]
#         ].mean()
#         * 100
#     )

#     highest_jump = (
#         data.groupby("ID")[
#             ["Open_pct_change", "High_pct_change", "Low_pct_change", "Close_pct_change"]
#         ]
#         .apply(abs)
#         .max()
#     )

#     num_entries_greater_than_average_open = (
#         data[
#             data["Open_pct_change"].abs()
#             > data["ID"].map(average_jump_100["Open_pct_change"]).abs()
#         ]
#         .groupby("ID")
#         .size()
#     )
#     num_entries_greater_than_average_high = (
#         data[
#             data["High_pct_change"].abs()
#             > data["ID"].map(average_jump_100["High_pct_change"]).abs()
#         ]
#         .groupby("ID")
#         .size()
#     )
#     num_entries_greater_than_average_low = (
#         data[
#             data["Low_pct_change"].abs()
#             > data["ID"].map(average_jump_100["Low_pct_change"]).abs()
#         ]
#         .groupby("ID")
#         .size()
#     )
#     num_entries_greater_than_average_close = (
#         data[
#             data["Close_pct_change"].abs()
#             > data["ID"].map(average_jump_100["Close_pct_change"]).abs()
#         ]
#         .groupby("ID")
#         .size()
#     )

#     condition_open = (
#         data["Open_pct_change"].abs()
#         > data["ID"].map(average_jump_100["Open_pct_change"]).abs()
#     )
#     condition_high = (
#         data["High_pct_change"].abs()
#         > data["ID"].map(average_jump_100["High_pct_change"]).abs()
#     )
#     condition_low = (
#         data["Low_pct_change"].abs()
#         > data["ID"].map(average_jump_100["Low_pct_change"]).abs()
#     )
#     condition_close = (
#         data["Close_pct_change"].abs()
#         > data["ID"].map(average_jump_100["Close_pct_change"]).abs()
#     )

#     data["To_take"] = condition_open | condition_high | condition_low | condition_close

#     data = data.reset_index()
#     data["Shift_To_take"] = (
#         data.groupby("ID")["To_take"].shift(-1).fillna(data["To_take"])
#     )
#     data["Shift_To_take"] = data["Shift_To_take"] | data["To_take"]

#     all_greater_than_average_data = data[data["Shift_To_take"]]

#     if len(all_greater_than_average_data) > 0:
#         all_greater_than_average_data_df = pd.DataFrame(
#             all_greater_than_average_data[["timestamp", "ID"]],
#             columns=["timestamp", "ID"],
#         )
#         all_greater_than_average_data_df.to_csv(
#             f"{universe}/{dtype}/{symbol}/exceed_jump_timestamp_to_id_csv/{date_range}_exceed_jumps_timestamp_to_id.csv"
#         )

#     open_percent_entries_greater_than_average_100 = (
#         num_entries_greater_than_average_open.sum() * 100
#     ) / len(data)
#     high_percent_entries_greater_than_average_100 = (
#         num_entries_greater_than_average_high.sum() * 100
#     ) / len(data)
#     low_percent_entries_greater_than_average_100 = (
#         num_entries_greater_than_average_low.sum() * 100
#     ) / len(data)
#     close_percent_entries_greater_than_average_100 = (
#         num_entries_greater_than_average_close.sum() * 100
#     ) / len(data)

#     msg += "Across column ID wise:\n"
#     msg += f"{open_percent_entries_greater_than_average_100}% data for which open percentage change is 100 times greater than average open percentage change\n"
#     msg += f"{high_percent_entries_greater_than_average_100}% data for which high percentage change is 100 times greater than average high percentage change\n"
#     msg += f"{low_percent_entries_greater_than_average_100}% data for which low percentage change is 100 times greater than average low percentage change\n"
#     msg += f"{close_percent_entries_greater_than_average_100}% data for which close percentage change is 100 times greater than average close percentage change\n\n"

#     with open(
#         f"{universe}/{dtype}/{symbol}/exceed_jump_reports/{date_range}_jump_report.txt",
#         "w",
#     ) as file:
#         file.write(msg)

#     print(data)

# import pandas as pd
# import datetime

# date_range_list = []
# for y in range(2007, 2024):
#     date_range_list.append((datetime.date(y, 1, 1), datetime.date(y + 1, 1, 1)))
# date_range_list.append((datetime.date(2024, 1, 1), datetime.date(2024, 3, 16)))

# library = "nse/1_min/raw_opt/trd"
# library_split = library.split("/")
# exchange = library_split[0]
# universe = library_split[2]
# dtype = library_split[-1]
# symbol = "5001"


# for date_range_tuple in date_range_list:
#     msg = ""

#     date_range = f"{date_range_tuple[0].strftime('%Y%m%d')}_{date_range_tuple[1].strftime('%Y%m%d')}"

#     data = pd.read_parquet(
#         f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}/fixed_with_tick_data_fixed_parquets/{date_range}_fixed_with_tick_data_fixed.parquet"
#     )
#     data = data.reset_index()
#     msg = f"Total data length: {len(data)}\n\n"

#     data["ocp"] = ((data["Close"] - data["Open"]) / data["Open"]) * 100
#     data["hlp"] = ((data["High"] - data["Low"]) / data["Low"]) * 100

#     ocp_Mean = data["ocp"].mean()
#     hlp_Mean = data["hlp"].mean()

#     oc_mean_100_se_bada = (data["ocp"] > ocp_Mean * 100)
#     hl_mean_100_se_bada = (data["hlp"] > hlp_Mean * 100)

#     all_greater_data = data[oc_mean_100_se_bada | hl_mean_100_se_bada]

#     if len(all_greater_data) > 0:
#         all_greater_data_df = pd.DataFrame(
#             all_greater_data[["timestamp", "ID"]],
#             columns=["timestamp", "ID"],
#         )
#         all_greater_data_df["timestamp"] = all_greater_data_df["timestamp"].astype(str)
#         already_df = pd.read_csv(f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}/exceed_jump_timestamp_to_id_csv/{date_range}_exceed_jumps_timestamp_to_id.csv")[["timestamp","ID"]]
#         already_df["ID"] = already_df["ID"].astype("uint64")

#         merged_df = all_greater_data_df.merge(already_df, on=['timestamp', 'ID'], how='outer', indicator=True)
#         result_df = merged_df[merged_df['_merge'] == 'left_only'][all_greater_data_df.columns]

#         if len(result_df) > 0:
#             result_df.to_csv(
#                 f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}/exceed_jump_row_wise_timestamp_to_id_csv/{date_range}_exceed_jumps_timestamp_to_id.csv"
#             )

# print()

# import datetime
# import pandas as pd
# import time
# from main.tanki import Tanki
# from main.data.checker_fixer import Checker_Fixer
# from main.config.config_factory import ConfigFactory
# from main.enums import StorageType
# from multiprocessing import Pool

# # load -> checker -> fixer -> checker


# def checker_cycle(date_range_tuple):
#     library = "nse/1_min/raw_opt/trd"
#     library_split = library.split("/")
#     exchange = library_split[0]
#     universe = library_split[2]
#     dtype = library_split[-1]
#     symbol = "5001"
#     date_range = f"{date_range_tuple[0].strftime('%Y%m%d')}_{date_range_tuple[1].strftime('%Y%m%d')}"

#     # load
#     print("\nStarted data loading...")
#     start_time = time.time()
#     data = pd.read_parquet(
#         f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}/exceed_jumps_fixed_with_tick_data_parquets/{date_range}_exceed_jumps_fixed_with_tick_data.parquet"
#     )
#     end_time = time.time()
#     print(f"Successfully loaded data with time: {end_time - start_time}\n")

#     tanki = Tanki(exchange_type=exchange)
#     tanki.login(username="mantraraj", password="mantraraj")
#     config = ConfigFactory(exchange_type=exchange)
#     checker_fixer = Checker_Fixer(config=config)

#     # check
#     print("Started checker...")
#     start_time = time.time()
#     checker_report = tanki.check_data(
#         storage_type=StorageType.DB,
#         file_location=library,
#         file_name=symbol,
#         data=data,
#     )
#     end_time = time.time()
#     print(f"Successfully completed checker with time: {end_time-start_time}\n")
#     with open(
#         f"{universe}/{dtype}/{symbol}/all_fixed_checker_reports/{date_range}_all_fixed_checker_report.txt",
#         "w",
#     ) as file:
#         file.write(checker_report)

#     print(f"Initial checker_report:\n\n{checker_report}")

#     final_checker_report = checker_report

#     # fix
#     if checker_report.count("Passed") != config.TOTAL_CHECKS:
#         print("Started fixer...")
#         start_time = time.time()
#         (
#             fixer_report,
#             data,
#             missing_dates,
#             missing_date_to_times,
#             date_to_id,
#             ohlc_percentage_exceed_date_to_id,
#             ohlc_basic_fail_data,
#         ) = checker_fixer.check_data(
#             storage_type=StorageType.DB,
#             file_location=library,
#             file_name=symbol,
#             checker_report=checker_report,
#             data=data,
#         )
#         end_time = time.time()
#         print(f"Successfully completed fixer with time: {end_time-start_time}\n")

#         if len(missing_dates) > 0:
#             missing_dates_df = pd.DataFrame(missing_dates, columns=["missing_dates"])
#             missing_dates_df.to_csv(
#                 f"{universe}/{dtype}/{symbol}/all_fixed_missing_dates_csv/{date_range}_all_fixed_missing_dates.csv"
#             )

#         if len(missing_date_to_times) > 0:
#             for date in missing_date_to_times.keys():
#                 missing_date_to_times[date] = list(missing_date_to_times[date])
#             missing_date_to_times_df = pd.DataFrame()
#             missing_date_to_times_df["date"] = missing_date_to_times.keys()
#             missing_date_to_times_df["missing_times"] = list(
#                 missing_date_to_times.values()
#             )
#             missing_date_to_times_df.to_csv(
#                 f"{universe}/{dtype}/{symbol}/all_fixed_missing_times_csv/{date_range}_all_fixed_missing_times.csv"
#             )

#         # check
#         print("Again started checker...")
#         start_time = time.time()
#         final_checker_report = tanki.check_data(
#             storage_type=StorageType.DB,
#             file_location=library,
#             file_name=symbol,
#             data=data,
#         )
#         end_time = time.time()
#         print(
#             f"Again successfully completed checker with time: {end_time-start_time}\n"
#         )
#         with open(
#             f"{universe}/{dtype}/{symbol}/all_fixed_final_checker_reports/{date_range}_all_fixed_final_checker_report.txt",
#             "w",
#         ) as file:
#             file.write(final_checker_report)

#     print(f"Final checker_report:\n\n{final_checker_report}")

#     data.to_parquet(
#         f"{universe}/{dtype}/{symbol}/all_fixed_parquets/{date_range}_all_fixed.parquet"
#     )

#     print(f"Completed for {date_range}\n")


# checker_cycle(date_range_tuple=((datetime.date(2020, 1, 1), datetime.date(2021, 1, 1))))


# def checker_cycle_multiprocessing():
#     start_time = time.perf_counter()

#     date_range_list = []
#     for y in range(2007, 2024):
#         date_range_list.append((datetime.date(y, 1, 1), datetime.date(y + 1, 1, 1)))
#     date_range_list.append((datetime.date(2024, 1, 1), datetime.date(2024, 3, 16)))

#     with Pool() as p:
#         p.map(checker_cycle, date_range_list)

#     finish_time = time.perf_counter()

#     print(f"Program finished in {finish_time-start_time} seconds")


# checker_cycle_multiprocessing()

# print()


# import pandas as pd
# import datetime
# import os

# file_location = "nse/1_min/raw_opt/trd"
# file_location_split = file_location.split("/")
# universe = file_location_split[2]
# dtype = file_location_split[-1]
# file_name = "5002"
# date_range = "20070424_20171229"

# data = pd.read_parquet(
#     "/home/<USER>/repos/data_auditing/raw_opt/trd/5002/20070424_20171229_fixed.parquet"
# )
# data = data.reset_index()
# data["Cons_Volume"] = (
#     data.groupby([data["timestamp"].dt.date, "ID"])["Cons_Volume"]
#     .diff()
#     .fillna(data["Cons_Volume"])
#     .astype("float64")
# )

# ipath = f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{file_name}"

# paths_list = [
#     f"{ipath}/missing_data_from_tick",
# ]

# initial_data_len = len(data)
# missing_data = pd.DataFrame(columns=data.columns)
# missing_data = missing_data.astype(dtype=data.dtypes)
# missing_data.index.name = data.index.name
# count1 = 0

# missing_data_list = []
# for path in paths_list:
#     count1 += 1
#     count2 = 0
#     for missing_data_name in os.listdir(path):
#         count2 += 1
#         missing_data_cur = pd.read_parquet(f"{path}/{missing_data_name}")
#         missing_data_list.append(missing_data_cur)

# missing_data_list.insert(0, data)
# data = pd.concat(missing_data_list)
# data = data.drop_duplicates(subset=["timestamp", "ID"], keep="first")

# data = data.set_index("timestamp")
# data = data.sort_index()

# data["Cons_Volume"] = data.groupby([data.index.date, "ID"])["Cons_Volume"].cumsum()

# data.to_parquet(f"{ipath}/{date_range}_missing_data_fixed.parquet")


# from main.tanki import Tanki
# from main.enums import StorageType
# import pandas as pd

# tanki = Tanki(exchange_type="nse")
# tanki.login(username="mantraraj", password="mantraraj")
# tanki.add_user_permissions(
#     username="mantraraj",
#     role="admin",
#     read_permissions="nse/1_min/raw_opt/ord",
#     write_permissions="nse/1_min/raw_opt/ord",
#     password="mantraraj",
# )


from multiprocessing import Pool
import pickle
import time
from arcticdb import Arctic
from minio import Minio
import pandas as pd
import numpy as np
import datetime
import os

# store = Arctic("s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret")
# store = Arctic(
#     "s3://*************:9000:daily-appends-121?access=super&secret=doopersecret"
# )
store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
storek=Arctic('s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret')

# library = "nse_old/1_min/optstk/ord"
# library_split = library.split("/")
# exchange = library_split[0]
# universe = library_split[2]
# frequency = library_split[1][0]
# dtype = library_split[-1]

# if library not in store.list_libraries():
#     store.create_library(library)
# lib = store[library]

# for name in os.listdir(f"/home/<USER>/jupyter/{universe}_{frequency}min_{dtype}"):
#     if ".ipy" in name:
#         continue

#     symbol = name[:-8]
#     data = pd.read_parquet(
#         f"/home/<USER>/jupyter/{universe}_{frequency}min_{dtype}/{name}"
#     )

#     # data = data[data.index.get_level_values(0).date < datetime.date(2024, 6, 6)]
#     lib.write(symbol, data)

#     print(f"Data write successfully for {symbol}\n")

# # libcom = store["nse/compilation"]

# len_dict = {}
# for lib in store.list_libraries():
#     if "nse_old" in lib and "1440_min" not in lib:
#         for sym in store[lib].list_symbols():
#             df = store[lib].read(sym).data
#             len_dict[f"{lib.replace('nse_old', 'nse')}_{sym}"] = len(df)

#             with open("len_dict_kivi_arcticdb", "wb") as f:
#                 pickle.dump(len_dict, f)

#             print(f"Done for {sym}")


# librf=store['nse/1_min/raw_fut/trd']
# qsyms=os.listdir("/home/<USER>/jupyter/raw_fut_from_qcollector")

# for sym in os.listdir("/home/<USER>/repos/data_auditing/final_raw_fut_tick_20240901"):
#     dfo=pd.read_parquet(f"/home/<USER>/repos/data_auditing/final_raw_fut_tick_20240901/{sym}/combined.parquet")
#     if f"{sym}.parquet" in qsyms:
#         dfn=pd.read_parquet(f"/home/<USER>/jupyter/raw_fut_from_qcollector/{sym}.parquet")
#     else:
#         dfn=pd.DataFrame()
    
    
#     df=pd.concat([dfo,dfn])
#     df=df.reset_index()
#     df=df.drop_duplicates(subset=['timestamp','expiry'],keep='first').set_index('timestamp').sort_index()
    
#     df['Cons_Volume']=df.groupby([df.index.date,'expiry'])['Volume'].cumsum()
#     df=df.drop(columns=['Volume'])
    
#     df=df[(df.index.date<datetime.date(2024,9,1))&(df.index.time>datetime.time(9,15))&(df.index.time<=datetime.time(15,30))]
    
#     librf.write(sym, df)

# lsyms = librf.list_symbols()
# for sym in qsyms:
#     if sym[:-8] not in lsyms:
#         df=pd.read_parquet(f"/home/<USER>/jupyter/raw_fut_from_qcollector/{sym}")
        
#         df['Cons_Volume']=df.groupby([df.index.date,'expiry'])['Volume'].cumsum()
#         df=df.drop(columns=['Volume'])
        
#         df=df[(df.index.date<datetime.date(2024,9,1))&(df.index.time>datetime.time(9,15))&(df.index.time<=datetime.time(15,30))]
        
#         librf.write(sym[:-8], df)
        
        
print()

# ['nse/1_min/opt/trd', 'nse/1_min/optstk/ord','nse/5_min/optstk/ord', 'nse/1_min/optstk/trd', 'nse/5_min/optstk/trd','nse/5_min/optstk/column_bucket','nse/1_min/fut/trd','nse/1_min/fut/column_bucket','nse/5_min/fut/trd','nse/5_min/fut/column_bucket','nse/1_min/futidx/trd','nse/5_min/futidx/trd','nse/1_min/futidx/column_bucket','nse/5_min/futidx/column_bucket',"nse/1_min/futidx_fut/trd","nse/1_min/optstk_unadjusted_spot/trd","nse/1_min/opt/ord",            "nse/5_min/futidx_fut/trd","nse/1_min/futidx_fut/column_bucket","nse/5_min/futidx_fut/column_bucket","nse/5_min/optstk_unadjusted_spot/trd","nse/1_min/optstk_unadjusted_spot/column_bucket","nse/5_min/optstk_unadjusted_spot/column_bucket",'nse/5_min/opt/trd','nse/5_min/opt/column_bucket',]

# library = "nse/1_min/opt/trd"
# library_split = library.split("/")
# universe = library_split[2]
# dtype = library_split[-1]
# symbol = "5008"
# date_range = "20230529_20240316"

# # data_list = []
# # for year in range(2007, 2025):
# #     data_list.append(
#         libcom.read(f"nse_1min_opt_trd_{symbol}_{year}0101_{year}1231").data
#     )


# data = pd.concat(data_list)
# data.to_parquet(
#     f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}/{date_range}.parquet"
# )
# print()/home/<USER>/repos/data_auditing/futidx/trd/5004/1_min/all_fixed.parquet
# print(lib.list_symbols())

# data = libcom.read(symbol="`nse/1_min/raw_opt/trd")
# print(data)

# from main.tanki import Tanki
# import pandas as pd
# from multiprocessing import Pool
# import time


# def compile_data_range(args):
#     print()
#     print(f"Started for {args[0]} - {args[1]}\n")

#     start_date = args[0]
#     end_date = args[1]

#     tanki = Tanki(exchange_type="nse")
#     tanki.login(username="mantraraj", password="mantraraj")

#     start_time = time.perf_counter()
#     tanki.compile_data(
#         universe_list=["opt"],
#         frequency=1,
#         dtype="trd",
#         start_date=start_date,
#         end_date=end_date,
#     )
#     finish_time = time.perf_counter()

#     print(f"Completed for {args[0]} - {args[1]} in {finish_time - start_time}\n")


# def combine():
#     year_ranges = []

#     for year in range(2007, 2025):
#         year_ranges.append(
#             (pd.Timestamp(year, 1, 1), pd.Timestamp(year, 12, 31, 23, 59))
#         )

#     start_time = time.perf_counter()

#     tanki = Tanki(exchange_type="nse")
#     tanki.login(username="mantraraj", password="mantraraj")

#     finish_time = time.perf_counter()

#     print(f"Program finished in {finish_time-start_time} seconds")


# combine()
# print()


# import pandas as pd
# import datetime

# data = pd.read_parquet(
#     "/home/<USER>/repos/data_auditing/opt/trd/5001/20190101_20240316.parquet"
# )
# print(data)


# import os
# import datetime


# def create_folders(base_directory, folder_names):
#     """
#     Create folders with given names inside the base directory.

#     Parameters:
#         base_directory (str): Base directory where folders will be created.
#         folder_names (list): List of folder names to create.

#     Returns:
#         None
#     """
#     # Ensure base directory exists
#     if not os.path.exists(base_directory):
#         os.makedirs(base_directory)

#     # Create folders inside base directory
#     for date_range_tuple in folder_names:
#         date_range = f"{date_range_tuple[0].strftime('%Y%m%d')}_{date_range_tuple[1].strftime('%Y%m%d')}"
#         folder_path = os.path.join(base_directory, date_range)
#         os.makedirs(folder_path, exist_ok=True)


# # Example usage:
# base_directory = "/home/<USER>/repos/data_auditing/raw_opt/trd/5002/fixed_with_external_source_2_date_to_id_csv"  # Specify your base directory path
# date_range_list = []

# for y in range(2007, 2024):
#     date_range_list.append((datetime.date(y, 1, 1), datetime.date(y + 1, 1, 1)))
# date_range_list.append(
#     (datetime.date(2024, 1, 1), datetime.date(2024, 3, 16))
# )  # Specify folder names

# create_folders(base_directory, date_range_list)


# Final checker cycle for metadata generation

# import datetime
# import numpy as np
# import pandas as pd
# import time
# from main.tanki import Tanki
# from main.data.checker_fixer import Checker_Fixer
# from main.config.config_factory import ConfigFactory
# from main.enums import StorageType
# from multiprocessing import Pool

# # load -> checker -> fixer -> checker


# def checker_cycle(date_range_tuple):
#     library = "nse/1_min/raw_opt/trd"
#     library_split = library.split("/")
#     exchange = library_split[0]
#     universe = library_split[2]
#     dtype = library_split[-1]
#     symbol = "5002"
#     date_range = f"{date_range_tuple[0].strftime('%Y%m%d')}_{date_range_tuple[1].strftime('%Y%m%d')}"

#     # load
#     print("\nStarted data loading...")
#     start_time = time.time()
#     data = pd.read_parquet(
#         f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}/{date_range}_all_fixed_fixed.parquet"
#     )

#     # data["Vwap"] = np.nan

#     end_time = time.time()
#     print(f"Successfully loaded data with time: {end_time - start_time}\n")

#     tanki = Tanki(exchange_type=exchange)
#     tanki.login(username="mantraraj", password="mantraraj")
#     config = ConfigFactory(exchange_type=exchange)
#     checker_fixer = Checker_Fixer(config=config)

#     # data = data[config.COLUMNS_DICT[universe][1:]]

#     # check
#     print("Started checker...")
#     start_time = time.time()
#     checker_report = tanki.check_data(
#         storage_type=StorageType.DB,
#         file_location=library,
#         file_name=symbol,
#         data=data,
#     )
#     end_time = time.time()
#     # print(f"Successfully completed checker with time: {end_time-start_time}\n")
#     with open(
#         f"{universe}/{dtype}/{symbol}/{date_range}_all_fixed_final_checker_report.txt",
#         "w",
#     ) as file:
#         file.write(checker_report)

#     # print(f"Initial checker_report:\n\n{checker_report}")

#     # final_checker_report = checker_report

#     # # fix
#     # if checker_report.count("Passed") != config.TOTAL_CHECKS:
#     #     print("Started fixer...")
#     #     start_time = time.time()
#     #     (
#     #         fixer_report,
#     #         data,
#     #         missing_dates,
#     #         missing_date_to_times,
#     #         failed_date_to_id,
#     #         ohlc_basic_fail_data,
#     #     ) = checker_fixer.check_data(
#     #         storage_type=StorageType.DB,
#     #         file_location=library,
#     #         file_name=symbol,
#     #         checker_report=checker_report,
#     #         data=data,
#     #     )
#     #     end_time = time.time()
#     #     print(f"Successfully completed fixer with time: {end_time-start_time}\n")

#     #     if len(missing_dates) > 0:
#     #         missing_dates_df = pd.DataFrame(missing_dates, columns=["missing_dates"])
#     #         missing_dates_df.to_csv(
#     #             f"{universe}/{dtype}/{symbol}/all_fixed_missing_dates_csv/{date_range}_all_fixed_missing_dates.csv"
#     #         )

#     #     if len(missing_date_to_times) > 0:
#     #         for date in missing_date_to_times.keys():
#     #             missing_date_to_times[date] = list(missing_date_to_times[date])
#     #         missing_date_to_times_df = pd.DataFrame()
#     #         missing_date_to_times_df["date"] = missing_date_to_times.keys()
#     #         missing_date_to_times_df["missing_times"] = list(
#     #             missing_date_to_times.values()
#     #         )
#     #         missing_date_to_times_df.to_csv(
#     #             f"{universe}/{dtype}/{symbol}/all_fixed_missing_times_csv/{date_range}_all_fixed_missing_times.csv"
#     #         )

#     #     if len(failed_date_to_id) > 0:
#     #         timestamp_id_df = pd.DataFrame(failed_date_to_id, columns=["Date", "ID"])
#     #         timestamp_id_df = timestamp_id_df.drop_duplicates()
#     #         timestamp_id_df.to_csv(
#     #             f"{universe}/{dtype}/{symbol}/all_fixed_failed_date_to_id_csv/{date_range}_failed_date_to_id.csv"
#     #         )

#     #     # check
#     #     print("Again started checker...")
#     #     start_time = time.time()
#     #     final_checker_report = tanki.check_data(
#     #         storage_type=StorageType.DB,
#     #         file_location=library,
#     #         file_name=symbol,
#     #         data=data,
#     #     )
#     #     end_time = time.time()
#     #     print(
#     #         f"Again successfully completed checker with time: {end_time-start_time}\n"
#     #     )
#     #     with open(
#     #         f"{universe}/{dtype}/{symbol}/all_fixed_final_checker_reports/{date_range}_all_fixed_final_checker_report.txt",
#     #         "w",
#     #     ) as file:
#     #         file.write(final_checker_report)

#     # print(f"Final checker_report:\n\n{final_checker_report}")

#     # data.to_parquet(f"{universe}/{dtype}/{symbol}/{date_range}_fixed_with_tick_data_fixed.parquet")

#     print(f"Completed for {date_range}\n")


# # def checker_cycle_multiprocessing():
# #     start_time = time.perf_counter()

# #     date_range_list = []
# #     for y in range(2007, 2024):
# #         date_range_list.append((datetime.date(y, 1, 1), datetime.date(y + 1, 1, 1)))
# #     date_range_list.append((datetime.date(2024, 1, 1), datetime.date(2024, 3, 16)))
# #     # date_range_list.append((datetime.date(2021, 1, 13), datetime.date(2024, 3, 16)))

# #     with Pool(3) as p:
# #         p.map(checker_cycle, date_range_list)

# #     finish_time = time.perf_counter()

# #     print(f"Program finished in {finish_time-start_time} seconds")


# # checker_cycle_multiprocessing()

# checker_cycle(date_range_tuple=((datetime.date(2007, 1, 1), datetime.date(2021, 1, 1))))

# print()


# concatenate

# import pandas as pd
# import datetime

# library = "nse/1_min/raw_opt/ord"
# library_split = library.split("/")
# exchange = library_split[0]
# universe = library_split[2]
# dtype = library_split[-1]
# symbol = "5001"

# date_range_list = []
# for y in range(2015, 2022):
#     date_range_list.append((datetime.date(y, 1, 1), datetime.date(y + 1, 1, 1)))
# # date_range_list.append((datetime.date(2024, 1, 1), datetime.date(2024, 3, 16)))

# data_list = []
# for date_range_tuple in date_range_list:
#     date_range = f"{date_range_tuple[0].strftime('%Y%m%d')}_{date_range_tuple[1].strftime('%Y%m%d')}"

#     data_list.append(
#         pd.read_parquet(
#             f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}/fixed_with_tick_data_fixed_parquets/{date_range}_fixed.parquet"
#         )
#     )

# data=pd.concat(data_list)
# data.to_parquet(f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}/20150101_20220101_fixed_with_tick_data_fixed.parquet")


# import pandas as pd
# import datetime

# dfo = pd.read_parquet("/home/<USER>/repos/data_auditing/opt/trd/5002/1_min/compiled.parquet")
# dfo = dfo[dfo.index.date >= datetime.date(2022, 7, 8)]
# dff = pd.read_parquet("/home/<USER>/jupyter/opt_1min_trd/5002_20070101_20220708_fixed_OI_backup.parquet")
# data = pd.concat([dff, dfo])
# data.to_parquet("/home/<USER>/repos/data_auditing/opt/trd/5002/1_min/OI_fixed_compiled.parquet")

# print()

# from multiprocessing import Pool
# import os
# import time
# from typing import cast
# import numpy as np
# import pandas as pd
# import datetime
# from main.config.config_NSE import ConfigNSE
# from main.data.utility import column_bucket_casting

# config = ConfigNSE()


# def column_bucket(symbol):
#     print(f"Started for {symbol}\n")

#     def create_column_bucket(data: pd.DataFrame):
#         """creates column bucket for each column of the given `data`

#         Args:
#             universe (str): name of universe
#             data (pd.DataFrame): combined dataframe of all symbols of the universe having timestamp as an index

#         Raises:
#             Exception: In case pivotting of dataframe has some issue

#         Returns:
#             Dict[str, pd.DataFrame]: column_bucket dict corresponding to each column of `data` in which key is the column name in string and value is its column bucket in dataframe with [`date`, `ID`] as an index
#         """
#         columns = data.columns
#         data["time"] = data.index.strftime("%H:%M")
#         data["date"] = data.index.date

#         try:
#             data = data.set_index(["date", "ID"]).pivot(columns="time")
#         except Exception as exception:
#             # try:
#             #     data = data.reset_index().reset_index()
#             #     data = data.set_index(["index", "date", "ID"]).pivot(columns="time")
#             #     # data["Open_int"] = data["Open_int"].reset_index().set_index(["date", "ID"]).drop(columns="index")
#             # except Exception as exception:
#             #     raise Exception(
#             #         f"Creation of column bucket fails due to {repr(exception)}"
#             #     )
#             raise Exception(f"Creation of column bucket fails due to {repr(exception)}")

#         column_to_data = {}

#         for column in columns:
#             if column == "Open_int":
#                 data_column = data[column]
#                 data_column = column_bucket_casting(
#                     universe="opt",
#                     column_name=column,
#                     data=data_column.reset_index(),
#                     data_types_dict=config.DATA_TYPES_DICT,
#                 )
#                 data_column = data_column.set_index(["date", "ID"])
#                 column_to_data[column] = data_column
#         return column_to_data

#     data = None
#     if symbol == "5001":
#         data = pd.read_parquet(
#             f"/home/<USER>/repos/data_auditing/opt/trd/5001/5_min/20070101_20240313_compiled.parquet"
#         )[["ID", "Open_int"]]
#     elif symbol == "5002":
#         data = pd.read_parquet(
#             f"/home/<USER>/repos/data_auditing/opt/trd/5002/5_min/20070101_20240313_compiled.parquet"
#         )[["ID", "Open_int"]]
#     elif symbol == "5003":
#         data = pd.read_parquet(
#             f"/home/<USER>/repos/data_auditing/opt/trd/5003/5_min/20210113_20240313_compiled.parquet"
#         )[["ID", "Open_int"]]
#     else:
#         data = pd.read_parquet(
#             f"/home/<USER>/repos/data_auditing/opt/trd/5008/5_min/20230529_20240313_compiled.parquet"
#         )[["ID", "Open_int"]]

#     datacb = create_column_bucket(data=data)["Open_int"]
#     datacb.to_parquet(
#         f"/home/<USER>/repos/data_auditing/opt/column_bucket/5_min/{symbol}_Open_int.parquet"
#     )


# def resample_data_1_min_to_5_min(data: pd.DataFrame) -> pd.DataFrame:
#     """resample 1_min data to 5_min data

#     Args:
#         data (pd.DataFrame): dataFrame with 1_min data, having a timestamp as an index.

#     Raises:
#         Exception: In case we find some extra columns which is not present in SAMPLING_AGG_DICT

#     Returns:
#         pd.DataFrame: dataFrame with 5_min data, having a timestamp as an index.
#     """

#     sampling_agg_dict = {}
#     for column in data.columns:
#         if column in config.SAMPLING_AGG_DICT:
#             sampling_agg_dict[column] = config.SAMPLING_AGG_DICT[column]

#     resample_data = pd.DataFrame(
#         (
#             data.groupby("ID")
#             .resample(rule="5T", label="right", closed="right")  # type: ignore
#             .agg(sampling_agg_dict)
#             .reset_index()
#             .set_index("timestamp")
#             .sort_index()
#         )
#     )

#     return resample_data


# print()
# # # for symbol in range(5001, 5009):
# # #     data = pd.read_parquet(f"/home/<USER>/repos/data_auditing/futidx/trd/{str(symbol)}/1_min/compiled.parquet")

# # #     data5m = resample_data_1_min_to_5_min(data=data)

# # #     data5m.to_parquet(f"/home/<USER>/repos/data_auditing/futidx/trd/{str(symbol)}/5_min/compiled.parquet")

# # # dataoi = pd.read_parquet(f"/home/<USER>/repos/data_auditing/opt/trd/5001/1_min/oi_compiled_1.parquet")
# # # oi5m = resample_data_1_min_to_5_min(data=dataoi)
# # # oi5m.to_parquet(f"/home/<USER>/repos/data_auditing/opt/trd/5002/5_min/oi_compiled_1.parquet")

# # print()


# def column_bucket_multiprocessing():
#     start_time = time.perf_counter()

#     symbol_list = ["5001", "5003", "5008", "5002"]

#     with Pool() as p:
#         p.map(column_bucket, symbol_list)

#     finish_time = time.perf_counter()

#     print(f"Program finished in {finish_time-start_time} seconds")


# # column_bucket_multiprocessing()


# print()


# def data_concatenation(path):
#     data_list = []
#     for data_path in os.listdir(path):
#         data = pd.read_parquet(os.path.join(path, data_path))
#         data_list.append(data)
#     data = pd.concat(data_list)
#     data = data.sort_index()
#     data.to_parquet(os.path.join(path, "compiled.parquet"))


# def data_drop_duplicates(path):
#     data = pd.read_parquet(path)
#     print(f"{path} initial: {len(data)}")
#     if "5_min" in path:
#         data["ID"] = data["ID"].astype("uint64")
#         data = data[data["Close"] > 0]
#     data = data.reset_index()
#     data = data.drop_duplicates(subset=["timestamp", "ID"])
#     # data = data.drop_duplicates(subset=["date", "ID"])
#     data = data.set_index("timestamp")
#     # data = data.set_index(["date", "ID"])
#     print(f"{path} final: {len(data)}")
#     data.to_parquet(path)


# # def column_bucket_concatenation(column):
# #     data_list=[]
# #     for


# # for symbol in range(5008, 5009):
# #     # data_drop_duplicates(
# #     #     path=f"/home/<USER>/repos/data_auditing/opt/trd/{symbol}/1_min/compiled.parquet"
# #     # )
# #     data_drop_duplicates(
# #         path=f"/home/<USER>/repos/data_auditing/opt/trd/{symbol}/5_min/compiled.parquet"
# #     )
# #     # data_drop_duplicates(
# #     #     f"/home/<USER>/repos/data_auditing/futidx/column_bucket/{symbol}.parquet"
# #     # )


# from main.tanki import Tanki
# import pandas as pd

# tanki = Tanki(exchange_type="nse")
# tanki.login(username="mantraraj", password="mantraraj")

# lib = tanki["nse/compilation"]

# df = pd.read_parquet("/home/<USER>/repos/data_auditing/futidx/trd/5008/5_min/compiled.parquet")

# assert lib.append("testing_appends", df, "testing_appends")

# print(df)


# import pandas as pd
# import datetime
# import numpy as np
# from main.config.config_NSE import ConfigNSE

# config = ConfigNSE()

# for symbol in ["5001", "5002", "5003", "5008"]:
#     df = pd.read_parquet(f"/home/<USER>/jupyter/opt_1min_ord/{symbol}_20240313_20240505.parquet")

#     df['Vwap'] = np.nan
#     df = df[config.COLUMNS_DICT['raw_opt'][1:]]

#     df.to_parquet(f"/home/<USER>/repos/data_auditing/raw_opt/ord/{symbol}/remaining_data/20240313_20240505.parquet")


# import pandas as pd
# import datetime

# for symbol in ["5001", "5002"]:
#     for date_range in [
#         "20150101_20200101",
#         "20200101_20210101",
#         "20210101_20220101",
#         "20220101_20230101",
#         "20230101_20240313",
#     ]:
#         df = pd.read_parquet(
#             f"/home/<USER>/repos/data_auditing/opt/ord/{symbol}/1_min/{date_range}_compiled.parquet"
#         )
#         dfoi = pd.read_parquet(
#             f"/home/<USER>/repos/data_auditing/opt/ord/{symbol}/1_min/{date_range}_OI.parquet"
#         )

#         if (df.ID == dfoi.ID).all():
#             df["Open_int"] = dfoi["Open_int"]
#             df.to_parquet(
#                 f"/home/<USER>/repos/data_auditing/opt/ord/{symbol}/1_min/{date_range}_compiled.parquet"
#             )
#         else:
#             print(f"failed for {symbol} and {date_range}\n")


# print()


# from multiprocessing import Pool
# import time
# from typing import cast
# from arcticdb import Arctic
# import pandas as pd
# import numpy as np
# import datetime

# from main.config.config_NSE import ConfigNSE
# from main.data.auditor import Auditor
# from main.enums import StorageType


# def comparison(library, symbol, start_date, end_date):
#     print(f"Started for {symbol}\n")
#     library = library
#     library_split = library.split("/")
#     exchange = library_split[0]

#     frequency = library_split[1][0]
#     universe = library_split[-2]
#     dtype = library_split[-1]
#     symbol = symbol
#     diff_threshold = 0.1
#     start_date = start_date
#     end_date = end_date
#     sedr = f"{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}"

#     date_range_list = []
#     for y in range(2007, 2024):
#         date_range_list.append((datetime.date(y, 1, 1), datetime.date(y + 1, 1, 1)))
#     date_range_list.append((datetime.date(2024, 1, 1), datetime.date(2024, 3, 16)))
#     # date_range_list.append((start_date, end_date))
#     # date_range_list.append((start_date, datetime.date(2024, 3, 16)))

#     config = ConfigNSE()
#     auditor = Auditor(config=config)

#     def get_missing_dates(
#         storage_type: StorageType,
#         file_location: str,
#         file_name: str,
#         data: pd.DataFrame,
#     ):
#         metadata = None

#         given_dates = set(pd.DatetimeIndex(data.index).date)
#         start_date = min(given_dates)
#         if (
#             (metadata is not None)
#             and ("last_timestamp" in metadata)
#             and (metadata["last_timestamp"].date() < start_date)
#         ):
#             start_date = metadata["last_timestamp"].date()
#             given_dates.add(start_date)
#         end_date = max(given_dates)

#         ALL_DATES = auditor.read(
#             storage_type=StorageType.FILE,
#             file_location=config.FILE_DICT["ALL_DATES"][0],
#             file_name=config.FILE_DICT["ALL_DATES"][1],
#         )

#         allowed_dates = set(
#             date
#             for date in ALL_DATES["ALL_DATES"].dt.date.values
#             if date >= start_date and date <= end_date
#         )

#         missing_dates = allowed_dates.difference(given_dates)

#         return missing_dates

#     def get_missing_times(
#         storage_type: StorageType,
#         file_location: str,
#         file_name: str,
#         data: pd.DataFrame,
#     ):
#         all_time_set = {
#             stamp.time()
#             for stamp in pd.date_range(
#                 start=config.MARKET_HOURS_DICT[universe]["open"],
#                 end=config.MARKET_HOURS_DICT[universe]["close"],
#                 freq=f"{frequency}T",
#                 inclusive="right",
#             )
#         }

#         date_wise_data_list = [
#             (date, group)
#             for date, group in data.groupby(pd.DatetimeIndex(data.index).date)
#         ]

#         missing_date_to_times = dict()
#         for date, date_wise_data in date_wise_data_list:
#             given_time_set = set(
#                 cast(datetime.time, cast(pd.DatetimeIndex, date_wise_data.index).time)
#             )
#             date_wise_missing_time_set = all_time_set - given_time_set

#             if len(date_wise_missing_time_set) > 0:
#                 if date not in missing_date_to_times:
#                     missing_date_to_times[date] = set()
#                 missing_date_to_times[date].update(date_wise_missing_time_set)

#         return missing_date_to_times

#     msg = "Old Data vs New data report:\n\nThe observed changes in the data due to several fixes implemented, including:\n- Correction: If all OHLC prices are NaN, they are now removed and replaced with values obtained from another data source.\n- Data Cleanup: Duplicate entries have been removed.\n- Ensured monotonic nature of Cons_Volume\n- Ensured forward filling of Open_int or OI\n- Data Enhancement: Additional data has been incorporated from another source for instances where intraday, interday, and across OHLC jumps exceeded a certain multiplier of their respective averages.\n- Data Completion: Added missing dates and times data from an alternative source.\n- Data Inconsistency Resolution: Addressed rare instances where 1_min data is missing but present in 5_min. It should be noted that 5_min data is compiled from 1_min data, so cases may arise where a date is present in the old 5_min data but missing in the new 5_min data.\n\nBugs Identified:\n- Inconsistencies in OHLC checks: Some data points fail to meet basic checks, such as ensuring that the high is the maximum and the low is the minimum of all values.\n- Manual Price Entries: Instances where prices are manually filled with tick prices (e.g., 0.01 or 0.15), leading to sudden jumps in the data.\n- Missing Entry Issue in Cons_Volume Calculation: Instances where some entries are missing, but their volume traded is already added to the next entry in the Cons_Volume calculation.\n- Discrepancy in Cons_Volume Calculation: Discrepancies between Cons_Volume and bhav copy data is due to the erroneous practice of initially adding the previous day's Cons_Volume to the first timestamp of the contract, followed by taking cumulative sum for the complete contract (not daywise).\n- Inconsistent Cons_Volume: We've noticed cases where the Cons_Volume is not a multiple of the lot size. Cons_Volume should reflect the product of contracts traded and lot size.\n- Corrupted Tick Files: Identified 1-2 tick files as corrupted.\n- Missing Tick Data: Rare instances where tick data is absent.\n- Entries Beyond Expiry: Found some entries in banknifty after their expiry date.\n- Inconsistent Open Interest and PCR: For some years of data, OI is present in 5-minute data but not in 1-minute data. Additionally, PCR, which is derived from OI, still has entries for which OI is NaN in both 1 and 5-minute data.\n\n"

#     # dfold_list = []
#     # for date_range_tuple in date_range_list:
#     #     date_range = f"{date_range_tuple[0].strftime('%Y%m%d')}_{date_range_tuple[1].strftime('%Y%m%d')}"

#     #     dfold_list.append(
#     #         pd.read_parquet(
#     #             f"/home/<USER>/jupyter/{universe}_{frequency}min_{dtype}/{symbol}_{date_range}.parquet"
#     #         )
#     #     )
#     #     # dfold_list.append(
#     #     #     pd.read_parquet(
#     #     #         f"/home/<USER>/jupyter/{universe}_{frequency}min_{dtype}/{symbol}_20240316.parquet"
#     #     #     )
#     #     # )

#     # dfold = pd.concat(dfold_list)
#     dfold = pd.read_parquet("/home/<USER>/jupyter/futidx_1min_5001.parquet")
#     dfold = dfold[(dfold.index.date >= start_date) & (dfold.index.date < end_date)]
#     # dfold = dfold[
#     #     [
#     #         "ID",
#     #         "Open",
#     #         "High",
#     #         "Low",
#     #         "Close",
#     #         "Cons_Volume",
#     #         "Open_int",
#     #         "iv",
#     #         "delta",
#     #         "gamma",
#     #         "theta",
#     #         "vega",
#     #     ]
#     # ]
#     # dfold = dfold.reset_index().set_index("date")

#     dfnew = pd.read_parquet(
#         f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}/{frequency}_min/pcr_fixed_bhav_compiled.parquet"
#     )
#     dfnew = dfnew[(dfnew.index.date >= start_date) & (dfnew.index.date < end_date)]
#     # dfnew = pd.read_parquet(
#     #     f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}_compiled.parquet"
#     # )
#     # dfnew = dfnew.reset_index().set_index("date")

#     # print(f"Data loaded for {symbol}\n")
#     missing_dates_old = get_missing_dates(
#         storage_type=StorageType.DB, file_location=library, file_name=symbol, data=dfold
#     )
#     missing_dates_new = get_missing_dates(
#         storage_type=StorageType.DB, file_location=library, file_name=symbol, data=dfnew
#     )

#     missing_times_old = get_missing_times(
#         storage_type=StorageType.DB, file_location=library, file_name=symbol, data=dfold
#     )
#     missing_times_new = get_missing_times(
#         storage_type=StorageType.DB, file_location=library, file_name=symbol, data=dfnew
#     )

#     msg += f"Length difference between new data and old data is {len(dfnew) - len(dfold)} rows"

#     if len(dfnew) - len(dfold) < 0:
#         msg += " (negative difference indicate entries filled with NaNs are removed)"
#     msg += "\n\n"

#     dfold = dfold.reset_index()
#     dfnew = dfnew.reset_index()

#     merged_df = pd.merge(dfnew, dfold, on=["timestamp", "ID"], how="inner")

#     msg += "Column wise difference in data:\n"
#     for col in dfold.columns[2:]:
#         if "Vwap" in col:
#             continue
#         diff_1percent_se_bada_percent = (
#             (abs(merged_df[f"{col}_x"] - merged_df[f"{col}_y"]) > diff_threshold).sum()
#             * 100
#             / len(merged_df)
#         )

#         if diff_1percent_se_bada_percent > 0:
#             msg += f"- {diff_1percent_se_bada_percent}% of data is updated for {col} having difference between old and new data is greater than {diff_threshold}\n"

#         # diff_1percent_se_bada_percent = (
#         #     (abs(merged_df[f"{col}_x"] - merged_df[f"{col}_y"])) / merged_df[f"{col}_y"]
#         # ).mean() * 100

#         # if diff_1percent_se_bada_percent > 0:
#         #     msg += f"- {diff_1percent_se_bada_percent}% of data is updated for {col} having difference between old and new data is greater than {diff_threshold}\n"

#         # msg += f"- Average percentage change between the old and new values for the column: {col} is: {diff_1percent_se_bada_percent}\n"

#     msg += "\nNan ratio comparison:\n"

#     for col in dfold.columns[2:]:
#         if "Vwap" in col:
#             continue
#         old_nan_ratio = (dfold[col].isna().sum() / len(dfold)) * 100
#         new_nan_ratio = (dfnew[col].isna().sum()) / len(dfnew) * 100
#         msg += f"- For the column {col}, old nan ratio is: {old_nan_ratio}, while new nan ratio is: {new_nan_ratio}\n"

#     msg += "\nMissing dates and times comparison:\n"
#     msg += "In some cases, the number of missing dates and times may increase due to the removal of NaN price entries and the absence of data in some other sources\n"

#     msg += f"- Old missing dates count: {len(missing_dates_old)}, while new missing dates count: {len(missing_dates_new)} with {len(missing_dates_old - missing_dates_new)} of the previously missing dates being resolved\n"

#     missing_times_old_count = 0
#     missing_times_new_count = 0
#     missing_times_fixed_count = 0

#     for times in missing_times_old.values():
#         missing_times_old_count += len(times)

#     for times in missing_times_new.values():
#         missing_times_new_count += len(times)

#     for date, times in missing_times_old.items():
#         if date in missing_times_new:
#             missing_times_fixed_count += len(times - missing_times_new[date])
#         else:
#             missing_times_fixed_count += len(times)

#     msg += f"- Old missing times count: {missing_times_old_count}, while new missing times count: {missing_times_new_count} with {missing_times_fixed_count} of the previously missing times being resolved\n"

#     msg += "\n\n"

#     with open(
#         f"/home/<USER>/repos/data_auditing/{universe}_reports/{dtype}/{symbol}/{frequency}_min/{sedr}_old_vs_new.txt",
#         "w",
#     ) as file:
#         file.write(msg)

#     print(f"Completed for {symbol}\n")

#     # with open(
#     #     f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{symbol}_old_vs_new.txt",
#     #     "w",
#     # ) as file:
#     #     file.write(msg)


# def symbol_wise(symbol):
#     comparison(
#         library="nse/1_min/futidx/trd",
#         symbol=str(symbol),
#         start_date=datetime.date(2024, 1, 1),
#         end_date=datetime.date(2024, 3, 13),
#     )


# # symbol_wise(5001)
# print()


# symbol="5008"
# print(f"Started for {symbol}")


# df=pd.read_parquet(f"/home/<USER>/repos/data_auditing/raw_opt_oi/{symbol}/purana_without_bhav_tick_dump.parquet")
# df=df[df.index.date<datetime.date(2022,6,6)]
# df=df.reset_index()

# dft=pd.read_parquet(f"/home/<USER>/repos/data_auditing/raw_opt_oi/{symbol}/combined_tick.parquet")
# dft=dft[dft.index.date<datetime.date(2022,6,6)]
# dft=dft.reset_index()

# import numpy as np
# from io import BytesIO
# from minio import Minio

# MINIO_END_POINT = "192.168.0.143:9000"
# MINIO_ACCESS_KEY = "reader"
# MINIO_SECRET_KEY = "doopersecret"
# store = Minio(MINIO_END_POINT, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, secure=False)
# all_dates = (np.load(BytesIO(store.get_object("commondata", "balte_uploads/ALL_DATES.npy").data), allow_pickle=True))

# bhav_oi = pd.read_parquet(f"/home/<USER>/jupyter/bhav_oi/{symbol}.parquet")
# insertion_points = np.searchsorted(all_dates, bhav_oi.index, side="right")
# bhav_oi.index = all_dates[insertion_points]
# bhav_oi.index.name = "timestamp"
# bhav_oi["expiry"] = pd.to_datetime(
#     (bhav_oi["ID"] // 100000) % 100000000, format="%Y%m%d"
# ).dt.date
# bhav_oi = bhav_oi[bhav_oi.index.date <= bhav_oi["expiry"]]
# bhav_oi = bhav_oi.drop(columns=["expiry"])
# bhav_oi = bhav_oi.reset_index()
# bhav_oi["timestamp"] = bhav_oi["timestamp"].apply(
#     lambda x: x.replace(hour=9, minute=16)
# )

# dfnf=pd.read_parquet(f"/home/<USER>/jupyter/NF_OI_sampler_dump/{symbol}_combined.parquet")
# dfnf=dfnf.reset_index()

# dftc=pd.concat([df,dft])
# dftc=dftc.drop_duplicates(subset=['timestamp','ID'],keep='last')
# dftc=dftc.set_index('timestamp')
# dftc=dftc.sort_index()
# dftc=dftc.reset_index()

# dftbc=pd.concat([dftc,bhav_oi])
# dftbc=dftbc.drop_duplicates(subset=['timestamp','ID'],keep='first')
# dftbc=dftbc.set_index('timestamp')
# dftbc=dftbc.sort_index()
# dftbc=dftbc.reset_index()

# dftbnc=pd.concat([dftbc,dfnf])
# dftbnc=dftbnc.drop_duplicates(subset=['timestamp','ID'],keep='last')
# dftbnc=dftbnc.set_index('timestamp')
# dftbnc=dftbnc.sort_index()

# print(dftbnc)

# dftbnc.to_parquet(f"/home/<USER>/repos/data_auditing/raw_opt_oi/{symbol}/tick_bhav_dump_sab_combined.parquet")


# import pandas as pd
# import os
# import datetime
# import shutil

# ipath="/home/<USER>/repos/data_auditing/raw_futstk_fut"

# for symbol in os.listdir(ipath):

# exp_path = f"{ipath}/{symbol}/expiry_wise_parquets"
# hft_path=f"{ipath}/{symbol}/combined_hft_data.parquet"
# dump_path=f"{ipath}/{symbol}/Vwap_fixed_sampler_dump.parquet"

# if os.path.exists(exp_path):
#     shutil.rmtree(exp_path)

# hft_data=None
# dump_data=None

# if os.path.exists(hft_path):
#     hft_data=pd.read_parquet(hft_path)
# if os.path.exists(dump_path):
#     dump_data=pd.read_parquet(dump_path)

# if hft_data is not None and dump_data is not None:

#     hft_data=hft_data[hft_data.index.date<dump_data.index.date[0]]

# data_list=[]
# if hft_data is not None:
#     hft_data['Cons_Volume']=hft_data.groupby([hft_data.index.date,'ID'])['Volume'].cumsum()
#     hft_data=hft_data.drop("Volume",axis=1)
#     data_list.append(hft_data)
# if dump_data is not None:
#     data_list.append(dump_data)

# data=pd.concat([hft_data,dump_data])

# data.to_parquet(f"{ipath}/{symbol}/Vwap_fixed_final_combined.parquet")

# print()


# import pandas as pd
# import datetime

# from main.data.compiler import Compiler
# from main.config.config_NSE import ConfigNSE


# config = ConfigNSE()

# compiler = Compiler(config=config)

# compiler.compile(
#     universe_list=["fut"],
#     frequency=1,
#     dtype="trd",
#     start_date=datetime.date(2007, 1, 1),
#     end_date=datetime.date(2024, 3, 13),
# )

# print("Compilation done!")


# import pandas as pd
# import datetime

# from main.data.operation import Operator
# from main.data.compiler import Compiler
# from main.config.config_NSE import ConfigNSE


# config = ConfigNSE()

# operator = Operator(config=config)

# df = pd.read_parquet("/home/<USER>/repos/data_auditing/futidx_fut_testing.parquet")

# reporate_info = pd.DataFrame(
#     {"repo_rate": [0.065], "date": [pd.Timestamp(2024, 6, 13)]}
# ).set_index("date")

# df = operator._Operator__add_next_cons_volume(
#     data=df.copy(),
#     underlying_data=df.copy(),
# )

# df = operator._Operator__apply_basis_adjustment(
#     universe="futidx_fut", data=df, reporate_info=reporate_info
# )

# df = operator._Operator__filter_near_contract(universe="futidx_fut", data=df)


# print("Compilation done!")


# import pandas as pd
# import datetime
# import pickle
# from minio import Minio

# MINIO_END_POINT = "192.168.0.198:11009"
# MINIO_ACCESS_KEY = "minioreader"
# MINIO_SECRET_KEY = "reasersecret"

# minioClient = Minio(
#     MINIO_END_POINT,
#     MINIO_ACCESS_KEY,
#     MINIO_SECRET_KEY,
#     secure=False,
# )

# nse_file_bucket_name = "commondata"

# MINIO_NIFTY_EXPIRY_DICT = "balte_uploads/nifty_expiry_dict"
# MINIO_BANKNIFTY_EXPIRY_DICT = "balte_uploads/banknifty_expiry_dict"
# MINIO_FINNIFTY_EXPIRY_DICT = "balte_uploads/finnifty_expiry_dict"
# MINIO_MIDCPNIFTY_EXPIRY_DICT = "balte_uploads/midcpnifty_expiry_dict"

# nifty_expiry_dict = {}
# try:
#     nifty_expiry_dict = pickle.loads(
#         minioClient.get_object(nse_file_bucket_name, MINIO_NIFTY_EXPIRY_DICT).data
#     )
# except Exception as e:
#     print("WARNING : Didnt load nifty_expiry_dict from minio, trying file now....")

# data_list = []
# for key, value_dict in nifty_expiry_dict.items():
#     for sub_key, expiry in value_dict.items():
#         data_list.append({'date': key, 'expiry': expiry})
# nifty_exp_df = pd.DataFrame(data_list)


# banknifty_expiry_dict = {}
# try:
#     banknifty_expiry_dict = pickle.loads(
#         minioClient.get_object(nse_file_bucket_name, MINIO_BANKNIFTY_EXPIRY_DICT).data
#     )
# except Exception as e:
#     print("WARNING : Didnt load banknifty_expiry_dict from minio, trying file now....")

# data_list = []
# for key, value_dict in banknifty_expiry_dict.items():
#     for sub_key, expiry in value_dict.items():
#         data_list.append({'date': key, 'expiry': expiry})
# banknifty_exp_df = pd.DataFrame(data_list)

# finnifty_expiry_dict = {}
# try:
#     finnifty_expiry_dict = pickle.loads(
#         minioClient.get_object(nse_file_bucket_name, MINIO_FINNIFTY_EXPIRY_DICT).data
#     )
# except Exception as e:
#     print("WARNING : Didnt load finnifty_expiry_dict....")

# data_list = []
# for key, value_dict in finnifty_expiry_dict.items():
#     for sub_key, expiry in value_dict.items():
#         data_list.append({'date': key, 'expiry': expiry})
# finnifty_exp_df = pd.DataFrame(data_list)

# midcpnifty_expiry_dict = {}
# try:
#     midcpnifty_expiry_dict = pickle.loads(
#         minioClient.get_object(nse_file_bucket_name, MINIO_MIDCPNIFTY_EXPIRY_DICT).data
#     )
# except Exception as e:
#     print("WARNING : Didnt load midcpnifty_expiry_dict....")

# data_list = []
# for key, value_dict in midcpnifty_expiry_dict.items():
#     for sub_key, expiry in value_dict.items():
#         data_list.append({'date': key, 'expiry': expiry})
# midcpnifty_exp_df = pd.DataFrame(data_list)

# symbol = "5002"

# df = pd.read_parquet(
#     f"/home/<USER>/repos/data_auditing/raw_futidx_fut/{symbol}/final_combined.parquet"
# )
# df["expiry"] = pd.to_datetime(df["ID"].astype(str).str[:8], format="%Y%m%d")


print()


# New data pushing to arcticdb

# from arcticdb import Arctic
# import pandas as pd
# import os
# from main.tanki import Tanki

# store = Arctic("s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret")

# library = "nse/5_min/futidx_fut/column_bucket"
# library_split = library.split("/")
# exchange = library_split[0]
# frequency = library_split[1][0]
# universe = library_split[2]
# dtype = library_split[-1]

# if library not in store.list_libraries():
#     store.create_library(library)

# tanki = Tanki(exchange_type=exchange)
# tanki.login(username="mantraraj", password="mantraraj")

# ipath = f"/home/<USER>/repos/data_auditing/{universe}/{dtype}/{frequency}_min"

# path_list = []


# for sym in os.listdir(ipath):
#     print(f"Started for {sym}")
#     data = pd.read_parquet(f"{ipath}/{sym}")

#     if sym not in tanki[library].list_symbols():
#         tanki[library].write_metadata(symbol=sym, data=data)
#         tanki[library].write(symbol=sym, data=data, comment="First time data write!")
#     else:
#         tanki[library].append(
#             symbol=sym, data=data, comment="Appending remaining data!"
#         )
#     print(f"Completed for {sym}\n")

# print()
# def data_write(path):
#     print(f"Started for {path}")
#     data = pd.read_parquet(path)

#     symbol = path.split("/")[6]

#     if symbol not in tanki[library].list_symbols():
#         tanki[library].write_metadata(symbol=symbol, data=data)
#         tanki[library].write(symbol=symbol, data=data, comment="First time data write!")
#     else:
#         tanki[library].append(
#             symbol=symbol, data=data, comment="Appending remaining data!"
#         )
#     print(f"Completed for {path}\n")


# def data_write_multiprocessing():
#     start_time = time.perf_counter()

#     with Pool() as p:
#         p.map(data_write, path_list)

#     finish_time = time.perf_counter()

#     print(f"Program finished in {finish_time-start_time} seconds")


# # data_write_multiprocessing()


# from main.tanki import Tanki

# library = "nse/1440_min/after_market/trd"
# library_old = library.replace("nse", "nse_old")
# library_split = library.split("/")
# exchange = library_split[0]
# frequency = library_split[1][0]
# universe = library_split[2]
# dtype = library_split[-1]

# store = Arctic("s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret")
# lib = store[library]
# lib_old = store[library_old]

# tanki = Tanki(exchange_type=exchange)
# tanki.login(username="mantraraj", password="mantraraj")

# move = [
#     "opt_margin",
#     "cm_bhav",
#     "isnifty_backup",
#     "cmvolt",
#     "mwpl",
#     "fii",
#     "cd_fo_bhav",
#     "lotval_next",
#     "cd_op_bhav",
#     "sector_wts",
#     "bmdate_forward",
#     "isht",
#     "isnifty_filtered",
#     "lotsize_near",
#     "isnifty",
#     "optidx_bhav",
#     "bmdate",
#     "lotval_far",
#     "lotsize_index",
#     "lotval_near",
#     "dii",
#     "op_bhav",
#     "index_bhav",
#     "client_oi",
# ]

# for sym in move:
#     data = lib_old.read(sym).data
#     if sym not in lib.list_symbols():
#         tanki[library].write_metadata(symbol=sym, data=data)
#         tanki[library].write(symbol=sym, data=data, comment="First time data write!")
#     else:
#         tanki[library].append(
#             symbol=sym, data=data, comment="Appending remaining data!"
#         )

#     print(f"Completed for {sym}\n")


# import pandas as pd
# import pickle

# ID_to_missing_timestamps_dict = {}

# for sym in os.listdir("/home/<USER>/repos/data_auditing/fut/trd"):

#     if f"{sym}.parquet" in os.listdir("/home/<USER>/jupyter/fut_1min_trd"):

#         dfnew = pd.read_parquet(f"/home/<USER>/repos/data_auditing/fut/trd/{sym}/1_min/compiled.parquet")
#         dfold = pd.read_parquet(f"/home/<USER>/jupyter/fut_1min_trd/{sym}.parquet")

#         missing = sorted(set(dfold.index) - set(dfnew.index))

#         ID_to_missing_timestamps_dict[sym] = missing


# with open("/home/<USER>/repos/data_auditing/ID_to_missing_timestamps_dict_fut_1min_trd", "wb") as f:
#     pickle.dump(ID_to_missing_timestamps_dict, f)


# import sys
# import pandas as pd

# sys.path.insert(0,"/home/<USER>/repos/balte")
# import balte

# from arcticdb import Arctic


# store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
# lib=store['nse/1_min/raw_cash/trd']
# libold=store['nse/1_min/raw_cash_old/trd']

# for sym in libold.list_symbols():
#     dfold=libold.read(sym).data
#     dfnew=pd.read_parquet(f"/home/<USER>/jupyter/raw_cash_from_cash/{balte.balte_config.symbol_to_balte_id[sym]}.parquet")
#     dfnew['ID']=balte.balte_config.symbol_to_balte_id[sym]
#     dfnew['ID']=dfnew['ID'].astype("str")


#     df=pd.concat([dfold,dfnew])
#     df=df.drop_duplicates(subset=['timestamp'],keep="first")
#     break
#     lib.write(sym,df)

# import asyncio

# async def long_running_task():
#     print("Task started")
#     await asyncio.sleep(3)  # Simulate a long-running task
#     print("Task finished")

# async def main():
#     try:
#         # Set a timeout of 5 seconds for the long_running_task
#         async with asyncio.timeout(5):
#             await long_running_task()
#     except asyncio.TimeoutError:
#         print("Task took too long and was cancelled")

# # Run the main function
# asyncio.run(main())


# import os
# import pandas as pd
# import pickle

# base_path = "/home/<USER>/repos/data_auditing/raw_cash"
# already_paths_213 = []
# already_paths_211 = []
# already_paths = []
# base_tick_path = "/mnt/companydata/MarketData/eq/tick/root_trd"

# all_paths = []
# with open("/home/<USER>/repos/data_auditing/fut_trd_20240901_all_paths", "rb") as f:
#     all_paths = pickle.load(f)

# for sym in os.listdir(base_path):
#     for date_str in os.listdir(f"{base_path}/{sym}"):
#         already_paths_213.append(f"{base_tick_path}/{date_str[:-8]}/{sym}/XX/XX/{sym}.trd")

# with open("/home/<USER>/repos/data_auditing/done_cash_paths_211", "rb") as f:
#     already_paths_211 = pickle.load(f)

# already_paths = already_paths_211 + already_paths_213


# rem_paths = list(set(all_paths) - set(already_paths))
# rem_paths = all_paths
# rem_paths = list(set(all_paths))
# part_size = len(rem_paths) // 3
# remainder = len(rem_paths) % 3
# rem_paths_213 = rem_paths[: part_size + (1 if remainder > 0 else 0)]
# rem_paths_211 = rem_paths[part_size + (1 if remainder > 0 else 0) : 2 * part_size + (2 if remainder > 1 else 0)]
# rem_paths_206 = rem_paths[2 * part_size + (2 if remainder > 1 else 0) :]

# with open("/home/<USER>/repos/data_auditing/fut_rem_paths_213", "wb") as f:
#     pickle.dump(rem_paths_213, f)

# with open("/home/<USER>/repos/data_auditing/fut_rem_paths_211", "wb") as f:
#     pickle.dump(rem_paths_211, f)

# with open("/home/<USER>/repos/data_auditing/fut_rem_paths_206", "wb") as f:
#     pickle.dump(rem_paths_206, f)

print()


# making of raw_fut

# import os
# import pandas as pd
# import numpy as np
# from arcticdb import Arctic
# import pickle

# optstk_expiry_dict = {}
# with open("/home/<USER>/jupyter/optstk_expiry_dict", "rb") as f:
#     optstk_expiry_dict = pickle.load(f)
# optstk_expiry_df = pd.DataFrame.from_dict(optstk_expiry_dict, orient="index")
# optstk_expiry_df.index.name = "date"
# optstk_expiry_df = optstk_expiry_df.reset_index()


# for sym in os.listdir("/home/<USER>/jupyter/raw_fut_old_arctic"):
#     darc = pd.read_parquet(f"/home/<USER>/jupyter/raw_fut_old_arctic/{sym}")
#     symbol = sym[:-8]
#     darc["ID"] = symbol
#     darc["Vwap"] = np.NaN
#     darc["date"] = darc.index.normalize()
#     darc = darc.reset_index()
#     darcm = pd.merge(darc, optstk_expiry_df, on=["date"], how="left")
#     darcm = darcm.rename(columns={"date": "expiry"})
#     darcm = (
#         darcm[
#             ["timestamp", "ID", "expiry", "Open", "High", "Low", "Vwap", "Cons_Volume"]
#         ]
#         .set_index("timestamp")
#         .sort_index()
#     )

#     darcm.to_parquet(f"/home/<USER>/jupyter/raw_fut_from_fut_raw/{sym}")


# import pandas as pd
# import re

# def _parse_to_date(date):
#     """
#     Internal function not to be used outside this file
#     Function parses string to date, supports following formats : [%d-%m-%Y, %d-%b-%Y]
#     Return pd.Timestamp in case of success, else pd.NaT in case of failure
#     """
#     attempt_formats = ["%d-%m-%Y", "%d-%b-%Y"]
#     for format in attempt_formats:
#         try:
#             parsed_date = pd.Timestamp(datetime.datetime.strptime(str(date), format))
#             print(f"Parsed date : {date}, {parsed_date}")
#             return parsed_date
#         except Exception as e:
#             parsed_date = pd.NaT
#             print(f"Failed to parse date : {date}, format : {format}")
#     return parsed_date

# def previous_date(date):
#     return date - pd.Timedelta(days=1)

# def _calculate_corpact_adj_factor(df, df_bhav, date):
#     """
#     Main function to calculate adjustment factor
#     Corporate action logic

#     Input :
#     1) Corporate Action DF with columns : ["symbol", "face_value", "purpose", "exdate"]
#     2) Bhavcopy DF with columns : ["symbol", "timestamp", "close"]
#     3) date : pd.Timestamp object for which corpact is to be calculated (previous date used by bhavcopy)

#     Output :
#     1) Corporate Action DF with columns : ["exdate", "type", "adj_factor", "symbol"]
#     """

#     yesterday = previous_date(date)
#     df_bhav["timestamp"] = pd.to_datetime(df_bhav.timestamp)
#     df_bhav = df_bhav.set_index(["timestamp", "symbol"])
#     df_bhav = df_bhav.xs(yesterday).close

#     df["exdate"] = df["exdate"].apply(_parse_to_date)
#     df = df[~pd.isna(df.exdate)]
#     df["prev_close"] = df.symbol.map(df_bhav)
#     df["face_value"] = df["face_value"].astype(int)
#     df["adj_factor"] = 1
#     df["corp_act_type"] = ""

#     ## No Corporate action for debentures, hence excluding all debenture PURPOSS:
#     df = df[~df.purpose.str.lower().str.contains("debenture")]

#     ## Find adjustment factor
#     pat1 = re.compile(r"div(\.|-| |ided|ded|dend|idend)|special")
#     pat2 = re.compile(r"sp\w*t")
#     pat3 = re.compile("bonus")
#     pat4 = re.compile("right")
#     corpact_texts = [pat1, pat2, pat3, pat4]
#     row_list = []

#     for _, row in df.iterrows():
#         cppos = []
#         cptype = []
#         corpacts_str = row.purpose.lower()
#         for c in range(len(corpact_texts)):
#             for m in corpact_texts[c].finditer(corpacts_str):
#                 cppos.append(m.start())
#                 cptype.append(c)
#         if not cppos:
#             continue
#         cppos, cptype = zip(*sorted(zip(cppos, cptype)))

#         rights_info = []
#         for cr in range(len(cppos)):
#             if cr == len(cppos) - 1:
#                 posstart = cppos[cr]
#                 posend = len(corpacts_str)
#             else:
#                 posstart = cppos[cr]
#                 posend = cppos[cr + 1]
#             num_array = []
#             for m in re.finditer(r"\d+\.\d+|\d+", corpacts_str[posstart:posend]):
#                 num_array.append(float(m.group()))
#                 temp_str = corpacts_str[posstart:posend][m.end() :].strip()
#                 if temp_str and temp_str[0] == "%":
#                     num_array[-1] = (num_array[-1] / 100) * row.face_value
#             if not num_array:
#                 continue
#             if cptype[cr] == 0:
#                 if len(num_array) != 1:
#                     print(
#                         f"Bad dividend for corporate action:{row.symbol} {row.purpose}"
#                     )
#                     continue
#                 row["adj_factor"] = (row["prev_close"] - num_array[0]) / row[
#                     "prev_close"
#                 ]
#                 row["corp_act_type"] = "DIVIDEND"
#             elif cptype[cr] == 1:
#                 if len(num_array) != 2:
#                     print(f"Bad split for corporate action:{row.symbol} {row.purpose}")
#                     continue
#                 row["adj_factor"] = num_array[1] / num_array[0]
#                 row["corp_act_type"] = "SPLIT"
#             elif cptype[cr] == 2:
#                 if len(num_array) != 2:
#                     print(f"Bad bonus for corporate action:{row.symbol} {row.purpose}")
#                     continue
#                 row["adj_factor"] = num_array[1] / (num_array[0] + num_array[1])
#                 row["corp_act_type"] = "BONUS"
#             elif cptype[cr] == 3:
#                 if len(num_array) == 3 or len(num_array) == 2:
#                     if len(num_array) == 3:
#                         rights_info.append(
#                             [
#                                 num_array[0],
#                                 num_array[1],
#                                 (num_array[2] + row.face_value),
#                                 row.prev_close,
#                             ]
#                         )
#                     elif len(num_array) == 2:
#                         rights_info.append(
#                             [num_array[0], num_array[1], row.face_value, row.prev_close]
#                         )
#                 else:
#                     print(f"Bad rights for corporate action:{row.symbol} {row.purpose}")
#             if cptype[cr] != 3:
#                 row_list.append(row.copy())
#         if rights_info:
#             lcm_ratio = 1
#             for rrow in range(len(rights_info)):
#                 lcm_ratio = np.lcm(int(lcm_ratio), int(rights_info[rrow][1]))
#             additional_equity = []
#             exitsting_equity = []
#             row_1 = []
#             for rrow in range(len(rights_info)):
#                 rights_info[rrow][0] = (rights_info[rrow][0] * lcm_ratio) / rights_info[
#                     rrow
#                 ][1]
#                 rights_info[rrow][1] = lcm_ratio
#                 additional_equity.append(rights_info[rrow][0] * rights_info[rrow][2])
#                 exitsting_equity.append(rights_info[rrow][1] * rights_info[rrow][3])
#                 row_1.append(rights_info[rrow][0])
#             aeq = np.nansum(additional_equity)
#             eeq = rights_info[0][1] * rights_info[0][3]
#             r1 = np.nansum(row_1)
#             row["adj_factor"] = ((eeq + aeq) / (r1 + rights_info[0][1])) / row[
#                 "prev_close"
#             ]
#             row["corp_act_type"] = "RIGHTS"
#             row_list.append(row.copy())
#     df = pd.DataFrame(row_list)

#     if df.empty:
#         print(f"No corpact for {date}, returning")
#         return pd.DataFrame(columns=["exdate", "type", "adj_factor", "symbol"])
#     # Skim for future dates
#     df = df[df["exdate"] >= date]

#     # drop nans and negative adj_factor
#     df = df[~df.adj_factor.isna()]
#     df = df[df.adj_factor > 0]

#     # Eliminate all rights and bonus issue with adj_factor >= 1
#     df = df[~(df.corp_act_type.isin(["RIGHTS", "BONUS"]) & (df.adj_factor >= 1))]

#     df["type"] = df.corp_act_type
#     df["inst_date"] = date
#     df = df.drop_duplicates(["symbol", "adj_factor", "corp_act_type"])

#     # skim to keep only required columns
#     df = df[["exdate", "type", "adj_factor", "symbol"]]

#     return df


# _calculate_corpact_adj_factor(df=pd.read_parquet("/home/<USER>/repos/data_auditing/corpact_flow_testing.parquet"), df_bhav=pd.read_parquet("/home/<USER>/repos/data_auditing/cm_bhav.parquet"), date=pd.Timestamp.today().normalize()-pd.Timedelta(days=8))


# import os

# base_path = "/home/<USER>/repos/data_auditing/raw_cash"


# def delete_daywise_parquets(base_path):
#     for sym in os.listdir(base_path):
#         path1 = f"{base_path}/{sym}"
#         sym_files = os.listdir(path1)
#         if "combined.parquet" in sym_files:
#             sym_files.remove("combined.parquet")
#             for file in sym_files:
#                 os.remove(f"{base_path}/{sym}/{file}")


# delete_daywise_parquets(base_path)


## getting futures path

# from multiprocessing import Manager, Pool
# import os
# import pandas as pd
# import pickle
# import re


# def get_symbol_to_balte_id():
#     # get from balte and save it
#     with open("/home/<USER>/jupyter/symbol_to_balte_id", "rb") as f:
#         return pickle.load(f)


# base_path_211 = "/mnt/companydata/MarketData/eq/tick/root_trd"
# starting_balte_id = 1
# ending_balte_id = 5000
# futures_type = "FF"
# symbol_to_balte_id = get_symbol_to_balte_id()
# pattern = re.compile(r"^[A-Z]+[0-9]{2}-[A-Za-z]{3}-[0-9]{4}\.trd$")

# dates_list = os.listdir(base_path_211)

# valid_dates_list = []
# invalid_dates_list = []

# for date in dates_list:
#     if len(date) == 8 and date.isdigit():
#         valid_dates_list.append(date)
#     else:
#         invalid_dates_list.append(date)

# print("\nGot date list...\n")

# # /mnt/companydata/MarketData/eq/tick/root_trd/01012018/ACC/22022018/FF/ACC22-Feb-2018.trd


# def get_sym_paths(date, shared_list):
#     path1 = f"{base_path_211}/{date}"
#     try:
#         for sym in os.listdir(path1):
#             path2 = f"{path1}/{sym}"
#             if (sym not in symbol_to_balte_id) or (
#                 symbol_to_balte_id[sym] >= starting_balte_id
#                 and symbol_to_balte_id[sym] <= ending_balte_id
#             ):
#                 for expiry in os.listdir(path2):
#                     path3 = f"{path2}/{expiry}"
#                     if len(expiry) == 8 and expiry.isdigit():
#                         path4 = f"{path3}/{futures_type}"
#                         if os.path.exists(path4):
#                             for contracts in os.listdir(path4):
#                                 if pattern.match(contracts):
#                                     shared_list.append(f"{path4}/{contracts}")

#     except Exception as e:
#         print(f"Failed for {date} due to: {e}\n")
#         return


# shared_list = Manager().list()

# print("Started getting all paths...\n")
# with Pool() as pool:
#     pool.starmap(get_sym_paths, [(date, shared_list) for date in valid_dates_list])
# print("Cpmpleted getting all paths\n")

# all_paths = list(shared_list)
# all_paths

# print(f"Storing all_paths list having length {len(all_paths)}...\n")
# with open("fut_trd_all_paths", "wb") as f:
#     pickle.dump(all_paths, f)
# print("Storing all_paths list done\n")


# speed testing of expiry rank calculation

# from arcticdb import Arctic
# from minio import Minio
# import pandas as pd
# import numpy as np
# import datetime
# import os
# import time

# store = Arctic(
#     "s3://*************:9000:daily-appends-121?access=super&secret=doopersecret"
# )

# library = "nse/1_min/raw_futidx_fut/trd"
# lib=store[library]

# df_list=[]
# for sym in lib.list_symbols():
#     df_list.append(lib.read(sym).data)
# df=pd.concat(df_list)
# df=df.sort_index()

# df['date']=df.index.normalize()

# expiry_df=pd.read_parquet("/home/<USER>/repos/data_auditing/index_expiry_df.parquet")


# ost=time.time()
# df['expiry_rank_old']=df.groupby([df.index.date,"ID"])["expiry"].rank("dense").astype("int")
# oet=time.time()

# nst=time.time()
# df = df.merge(expiry_df, how="left", on=["date", "ID"])

# df["expiry_rank"] = np.NaN
# df.loc[df["expiry"] == df["near_month"], "expiry_rank"] = 1
# df.loc[df["expiry"] == df["next_month"], "expiry_rank"] = 2


# import pandas as pd
# import os
# from arcticdb import Arctic

# store=Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
# library="nse/1_min/raw_fut/trd"
# lib=store[library]

# base_path="/home/<USER>/repos/data_auditing/raw_fut_tick"

# symbols=lib.list_symbols()

# for sym in os.listdir(base_path):
#     dfold=pd.DataFrame()
#     if sym in symbols:
#         dfold=lib.read(sym).data
#     df=pd.read_parquet(f"{base_path}/{sym}/combined.parquet")
#     if len(dfold)>0:
#         df=pd.concat([dfold,df])
#         df=df.reset_index()
#         df=df.drop_duplicates(subset=['timestamp', 'expiry'])
#         df=df.set_index('timestamp').sort_index()
#     lib.write(sym, df)

# print()


# import pandas as pd
# import os
# from arcticdb import Arctic

# store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
# library = "nse/1_min/raw_fut/trd"
# lib = store[library]
# library_old = "nse/1_min/raw_fut_old/trd"
# libo = store[library_old]
# symbols = lib.list_symbols()

# for sym in libo.list_symbols():
#     if sym in symbols:
#         dfold = libo.read(sym).data
#         dfold["Volume"] = (
#             dfold.groupby([dfold.index.date, dfold.expiry])["Cons_Volume"]
#             .diff()
#             .fillna(dfold.Cons_Volume)
#         )
#         dfold = dfold.drop(columns=["Cons_Volume"])
#         df = lib.read(sym).data
#         df = pd.concat([df, dfold])
#         df = df.reset_index()
#         df = df.drop_duplicates(subset=["timestamp", "expiry"], keep="first")
#         df = df.set_index("timestamp").sort_index()
#         df["Cons_Volume"] = df.groupby([df.index.date, "expiry"])[
#             "Volume"
#         ].cumsum()
#         df = df.drop(columns=["Volume"])
#         lib.write(sym, df)

# print()


# librc=store['nse/1_min/raw_cash/trd']
# symbols=librc.list_symbols()

# for sym in os.listdir('/home/<USER>/repos/data_auditing/raw_cash_20240313_20240901'):
#     if sym in symbols:
#         dfold=librc.read(sym).data
#         dfold['Volume']=dfold.groupby(dfold.index.date)['Cons_Volume'].diff().fillna(dfold.Cons_Volume)
#         dfold=dfold.drop(columns=['Cons_Volume'])
#     else:
#         dfold=pd.DataFrame()

#     dfnew=pd.read_parquet(f'/home/<USER>/repos/data_auditing/raw_cash_20240313_20240901/{sym}/combined.parquet')
#     df=pd.concat([dfold,dfnew])
#     df=df.reset_index()
#     df=df.drop_duplicates(subset=['timestamp'],keep='first')
#     df=df.set_index('timestamp').sort_index()
#     librc.write(sym, df)


# librc=store['nse/1_min/raw_cash/trd']
# symbols=librc.list_symbols()

# for sym in os.listdir('/home/<USER>/jupyter/raw_cash_sampler_dump_20240313_20240901'):
#     if sym in symbols:
#         dfold=librc.read(sym).data
#     else:
#         dfold=pd.DataFrame()

#     dfnew=pd.read_parquet(f'/home/<USER>/jupyter/raw_cash_sampler_dump_20240313_20240901/{sym}/combined.parquet')
#     dfnew['Volume']=dfnew.groupby(dfnew.index.date)['Cons_Volume'].diff().fillna(dfnew.Cons_Volume)
#     dfnew=dfnew.drop(columns=['Cons_Volume'])

#     df=pd.concat([dfold,dfnew])
#     df=df.reset_index()
#     df=df.drop_duplicates(subset=['timestamp'],keep='first')
#     df=df.set_index('timestamp').sort_index()

#     df['Cons_Volume']=df.groupby(df.index.date)['Volume'].cumsum()
#     df=df.drop(columns=['Volume'])
#     librc.write(sym, df)


## combining all fut

# import pandas as pd
# import os

# old_syms = os.listdir("/home/<USER>/repos/data_auditing/raw_fut_tick")

# for sym in os.listdir("/home/<USER>/repos/data_auditing/raw_fut_tick_20240901"):
#     df = pd.read_parquet(
#         f"/home/<USER>/repos/data_auditing/raw_fut_tick_20240901/{sym}/combined.parquet"
#     )

#     if sym in old_syms:
#         dfold = pd.read_parquet(
#             f"/home/<USER>/repos/data_auditing/raw_fut_tick/{sym}/combined.parquet"
#         )
#     else:
#         dfold = pd.DataFrame()

#     df = pd.concat([df, dfold])
#     df = df.reset_index()
#     df = (
#         df.drop_duplicates(subset=["timestamp", "expiry"], keep="first")
#         .set_index("timestamp")
#         .sort_index()
#     )

#     df.to_parquet(
#         f"/home/<USER>/repos/data_auditing/raw_fut_tick_20240901/{sym}/combined.parquet"
#     )
    
# new_syms = os.listdir("/home/<USER>/repos/data_auditing/raw_fut_tick_20240901")

# for sym in os.listdir("/home/<USER>/repos/data_auditing/raw_fut_tick"):
#     if sym not in new_syms:
#         df=pd.read_parquet(f"/home/<USER>/repos/data_auditing/raw_fut_tick/{sym}/combined.parquet")
#         os.makedirs(f"/home/<USER>/repos/data_auditing/raw_fut_tick_20240901/{sym}")
#         df.to_parquet(f"/home/<USER>/repos/data_auditing/raw_fut_tick_20240901/{sym}/combined.parquet")


# import pandas as pd
# import os
# from arcticdb import Arctic

# store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
# library = "nse/1_min/raw_fut/trd"
# lib = store[library]
# library_old = "nse/1_min/raw_fut_old/trd"
# libo = store[library_old]
# symbols = lib.list_symbols()

# for sym in libo.list_symbols():
#     if sym in symbols:
#         dfold = libo.read(sym).data
#         dfold["Volume"] = (
#             dfold.groupby([dfold.index.date, dfold.expiry])["Cons_Volume"]
#             .diff()
#             .fillna(dfold.Cons_Volume)
#         )
#         dfold = dfold.drop(columns=["Cons_Volume"])
#         df = lib.read(sym).data
#         df = pd.concat([df, dfold])
#         df = df.reset_index()
#         df = df.drop_duplicates(subset=["timestamp", "expiry"], keep="first")
#         df = df.set_index("timestamp").sort_index()
#         df["Cons_Volume"] = df.groupby([df.index.date, "expiry"])[
#             "Volume"
#         ].cumsum()
#         df = df.drop(columns=["Volume"])
#         lib.write(sym, df)

# print()


import pandas as pd
import os
from arcticdb import Arctic

store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
# library = "nse/1_min/raw_fut/trd"
# libf = store[library]
# libfo = store[library.replace("raw_fut", "raw_fut_old_20240901")]

# for sym in os.listdir("/home/<USER>/repos/data_auditing/final_raw_fut_tick_20240901"):
#     df=pd.read_parquet(f"/home/<USER>/repos/data_auditing/final_raw_fut_tick_20240901/{sym}/combined.parquet")
#     libf.write(sym, df)



# import pandas as pd
# import os
# from arcticdb import Arctic

# store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
# library = "nse/1_min/raw_fut/trd"
# libf = store[library]
# libfo = store[library.replace("raw_fut", "raw_fut_old_20240901")]
# syms = libf.list_symbols()

# for sym in os.listdir("/home/<USER>/jupyter/futstk_fut_sampler_dump_20240901"):
#     if sym[0]=='.': continue
    
#     symbol=sym[:-8]
#     if symbol in syms:
#         dfold=libf.read(symbol).data 
#     else:
#         dfold=pd.DataFrame()
        
#     df=pd.read_parquet(f"/home/<USER>/jupyter/futstk_fut_sampler_dump_20240901/{sym}")
#     df=pd.concat([dfold, df])
#     df=df.reset_index()
#     df=df.drop_duplicates(subset=['timestamp','expiry'], keep="first").set_index('timestamp')
#     df=df.sort_index()
    
#     libf.write(symbol, df)


import pandas as pd
import os
from arcticdb import Arctic
import pickle


lib=store['nse/1_min/raw_cash/trd']
# isliq_optstk=pd.read_parquet("/home/<USER>/repos/data_auditing/new_isliquid_optstk.parquet")

# with open("/home/<USER>/repos/data_auditing/ALL_DATES", "rb") as f:
#     all_dates=pickle.load(f)

# with open("/home/<USER>/repos/data_auditing/ALL_DATE_TO_INDEX", "rb") as f:
#     all_date_to_index=pickle.load(f)

# with open("/home/<USER>/repos/data_auditing/INDEX_TO_ALL_DATE", "rb") as f:
#     index_to_all_date=pickle.load(f)

# liboptstk = store['nse/1_min/optstk_unadjusted_spot_from_raw_cash/trd']

# for sym in liboptstk.list_symbols():
#     df=liboptstk.read(sym).data
#     df=df[(df.index.date<datetime.date(2024,9,1))&(df.index.time>datetime.time(9,15))&(df.index.time<=datetime.time(15,30))]
#     df.to_parquet(f"/home/<USER>/jupyter/optstk_unadjusted_spot_from_raw_cash_1min_trd/{sym}.parquet")

dates_not_found_in_all_dates = set()
errors_for_sym = {}

# for sym, islf in isliq_optstk.groupby('Symbol'):
#     print(f"Started for {sym}...")
#     try:
#         df=lib.read(sym).data 
#         lst=[0] * len(all_date_to_index)

#         for date, _ in islf.iterrows():
#             if date not in all_date_to_index: continue
#             lst[max(0, all_date_to_index[date] - 30)] += 1
#             lst[min(len(all_date_to_index) - 1, all_date_to_index[date] + 1)] += -1
        
#         date_to_inc = {}
#         date_to_inc[index_to_all_date[0]] = lst[0]
#         for i in range(1, len(lst)):
#             lst[i] += lst[i - 1]
#             date_to_inc[index_to_all_date[i]] = lst[i]
            
#         df['date']=df.index.date
#         df['date']=pd.to_datetime(df['date'], format='%Y-%m-%d')
#         df['date_to_inc'] = df['date'].map(date_to_inc)

#         dfn=df[df.date_to_inc.isna()]
#         df=df[(~df.date_to_inc.isna()) & (df.date_to_inc != 0)]

#         df=df.drop(columns=['date', 'date_to_inc'])

#         dates_not_found_in_all_dates.update(set(dfn.index.date))

#         liboptstk.write(sym, df)

#     except Exception as e:
#         errors_for_sym[sym] = e


# with open("dates_not_found_in_all_dates", "wb") as f:
#     pickle.dump(dates_not_found_in_all_dates, f)

# with open("errors_while_making_raw_optstk", "wb") as f:
#     pickle.dump(errors_for_sym, f)

# print("Done :)")

print()