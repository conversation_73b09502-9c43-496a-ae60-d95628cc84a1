import pandas as pd
from typing import Optional, Dict, List
import numpy as np
from io import BytesIO
import pickle
from daily_appends.daily_appends_base import DailyAppendsBase
from main.enums import StorageType
from minio import Minio
from main.data.utility import (
    get_library_name,
    previous_date,
    calculate_corpact_adj_factor,
    get_merger_demerger,
    round_to_nearest_lotsize,
    round_to_nearest_lotsize_column_bucket,
    stack_column_bucket_data,
)


class DailyAppendsNSE(DailyAppendsBase):
    def __get_repo_rate(self, date=None):
        """
        Return the repo rate for this date
        """
        ## Fetch repo_rate for give date
        if date is None:
            date = pd.Timestamp.now().normalize()

        repo_rate = pd.read_csv(
            BytesIO(
                self._get_from_minio("commondata", "balte_uploads/repo_rate.csv").data
            )
        )
        repo_rate.columns = ["date", "rate"]
        repo_rate.date = pd.to_datetime(repo_rate.date, format="%m/%d/%Y")
        repo_rate = repo_rate.sort_values("date")
        curr_rate = repo_rate[repo_rate.date < date].iloc[-1].rate
        return curr_rate

    def __preprocess_bhav_data(
        self, bhav_universe, df, prev_date, symbol_to_balte_id, repo_rate
    ):
        if bhav_universe in ["cm_bhav"]:
            ## columns to lower case
            df.columns = [x.lower() for x in df.columns]

            ## keep only eq
            df = df[df.series.isin(["EQ"])]

            ## cast timestamp to date
            df.timestamp = pd.to_datetime(df.timestamp, format="%d-%b-%Y")

            df = df.rename(columns={"timestamp": "date"})
            ## set index on timestamp
            df = df.set_index("date")

            ## balte-id
            df["balte_id"] = df.symbol.map(symbol_to_balte_id)
            df = df[~np.isnan(df.balte_id)]
            df.balte_id = df.balte_id.astype("int")

            ## adding adj_factor and close_raw columns:
            df["close_raw"] = df["close"]

            ## Adding eod_price column for backtesting support
            df["eod_price"] = df["close"]
            df["adj_factor"] = 1
            df["adj_factor"] = df["adj_factor"].astype("float64")
            return df
        elif bhav_universe in ["optstk_bhav", "optidx_bhav"]:
            df.columns = [x.lower() for x in df.columns]

            df = df.rename(columns={"timestamp": "date"})
            if bhav_universe == "optstk_bhav":
                op_bhav = df[df.instrument.isin(["OPTSTK"])]
            elif bhav_universe == "optidx_bhav":
                op_bhav = df[df.instrument.isin(["OPTIDX"])]

            op_bhav["expiry_dt"] = pd.to_datetime(op_bhav.expiry_dt, format="%d-%b-%Y")

            op_bhav["balte_id"] = op_bhav.symbol.map(symbol_to_balte_id)
            op_bhav = op_bhav[~np.isnan(op_bhav.balte_id)]
            op_bhav["balte_id"] = (
                op_bhav["balte_id"].astype(int).astype(str).str.rjust(4, "0")
            )

            ## Generate new balte_id
            if bhav_universe == "optstk_bhav":
                expiry = op_bhav.expiry_dt.dt.strftime("%Y%m%d").str[2:]
                strike = (op_bhav.strike_pr * 100).astype(int).astype(str)
            else:
                expiry = op_bhav.expiry_dt.dt.strftime("%Y%m%d")
                strike = (op_bhav.strike_pr).astype(int).astype(str)

            option = op_bhav.option_typ.map({"CE": "1", "PE": "0"})
            op_bhav["balte_id"] = strike + expiry + option + op_bhav.balte_id
            op_bhav["balte_id"] = op_bhav["balte_id"].astype("uint64")

            op_bhav["close_raw"] = op_bhav["close"]
            op_bhav["adj_factor"] = 1
            op_bhav["adj_factor"] = op_bhav["adj_factor"].astype("float64")

            ## Adding eod_price column for backtesting support
            op_bhav["eod_price"] = op_bhav["close"]

            ## adding expiry rank
            op_bhav["expiry_rank"] = op_bhav.groupby(
                ["date", "balte_id"]
            ).expiry_dt.rank(method="dense")

            ## parse date to timestamp:
            op_bhav.date = pd.to_datetime(op_bhav.date)
            op_bhav = op_bhav.set_index("date")

            col_list = [
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
                "balte_id",
                "close_raw",
                "adj_factor",
                "expiry_rank",
                "eod_price",
            ]

            return op_bhav[col_list]

        elif bhav_universe in ["futstk_bhav", "futidx_bhav"]:
            expiry_dict = pickle.load(
                BytesIO(
                    self._get_from_minio(
                        "commondata", "balte_uploads/optstk_expiry_dict"
                    ).data
                )
            )

            ## columns to lower case
            df.columns = [x.lower() for x in df.columns]

            if bhav_universe == "futstk_bhav":
                fut_bhav = df[df.instrument.isin(["FUTSTK"])]
            elif bhav_universe == "futidx_bhav":
                fut_bhav = df[df.instrument.isin(["FUTIDX"])]

            ## Cast expiry to timestamp
            fut_bhav.expiry_dt = pd.to_datetime(fut_bhav.expiry_dt, format="%d-%b-%Y")

            ## generate balte_id for fut_bhav
            fut_bhav["balte_id"] = fut_bhav.symbol.map(symbol_to_balte_id)
            fut_bhav = fut_bhav[~np.isnan(fut_bhav["balte_id"])]
            fut_bhav["balte_id"] = fut_bhav["balte_id"].astype(
                self._config.DATA_TYPES_DICT["balte_id"]
            )

            ## cast timestamp to date
            fut_bhav.timestamp = pd.to_datetime(fut_bhav.timestamp, format="%d-%b-%Y")
            df = df.rename(columns={"timestamp": "date"})

            fut_bhav = fut_bhav.rename(columns={"timestamp": "date"})

            ## adding expiry rank
            expiry_df = pd.DataFrame.from_dict(expiry_dict, orient="index")[
                ["near_month", "next_month"]
            ]
            expiry_df.index.name = "date"
            fut_bhav = pd.merge(
                fut_bhav, expiry_df.reset_index(), how="left", on=["date"]
            )
            fut_bhav["expiry_rank"] = 3
            fut_bhav.loc[
                fut_bhav["expiry_dt"] == fut_bhav["near_month"], "expiry_rank"
            ] = 1
            fut_bhav.loc[
                fut_bhav["expiry_dt"] == fut_bhav["next_month"], "expiry_rank"
            ] = 2

            ## adding adj_factor and close_raw columns:
            fut_bhav["close_raw"] = fut_bhav["close"]
            fut_bhav["adj_factor"] = 1
            fut_bhav["adj_factor"] = fut_bhav["adj_factor"].astype("float64")

            ## Adding eod_price column for backtesting support
            fut_bhav["eod_price"] = fut_bhav["close"]

            ## Add repo rate
            fut_bhav["repo_rate"] = repo_rate

            ## set index on timestamp
            fut_bhav = fut_bhav.set_index("date")
            col_list = [
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
                "balte_id",
                "expiry_rank",
                "close_raw",
                "adj_factor",
                "repo_rate",
                "eod_price",
            ]
            return fut_bhav[col_list]

        elif bhav_universe == "cd_fo_bhav":
            ## columns to lower case
            df.columns = [x.lower() for x in df.columns]

            ## set timestamp
            df["date"] = prev_date

            ## extract info from contract_d : `FUTCURGBPINR31-MAR-2022`
            df["segment"] = df["contract_d"].str[:6]
            df["symbol"] = df["contract_d"].str[6:12]
            df["expiry"] = pd.to_datetime(df["contract_d"].str[12:], format="%d-%b-%Y")

            ## Keep only trading symbols in bhavcopy
            df = df[df["symbol"].isin(["USDINR", "GBPINR", "EURINR", "JPYINR"])]
            df["balte_id"] = df.symbol.map(symbol_to_balte_id)

            # set expiry rank
            df["expiry_rank"] = df.groupby("balte_id").expiry.rank(method="dense")

            ## Adding eod_price column for backtesting support
            df["eod_price"] = df["close_pric"]

            # set index on date
            df = df.rename(columns={"timestamp": "date"})
            df = df.set_index("date")
            return df

        elif bhav_universe == "cd_op_bhav":
            ## columns to lower case
            df.columns = [x.lower() for x in df.columns]

            ## set timestamp
            df["date"] = prev_date

            ## extract info from contract_d : `OPTCUREURUSD29-MAR-2022PE1.225`
            df["segment"] = df["contract_d"].str[:6]
            df["symbol"] = df["contract_d"].str[6:12]
            df["expiry"] = df["contract_d"].str[12:23]
            df["option_typ"] = df["contract_d"].str[23:25]
            df["strike"] = (df["contract_d"].str[25:]).astype(float)

            ## Keep only trading symbols in bhavcopy
            df = df[df["symbol"].isin(["USDINR", "GBPINR", "EURINR", "JPYINR"])]
            df["balte_id"] = df.symbol.map(symbol_to_balte_id)

            ## generate balte_id for contracts
            df["strike_id"] = (df.strike * 100).astype(int).astype(str)
            df["expiry_id"] = pd.to_datetime(df.expiry, format="%d-%b-%Y").dt.strftime(
                "%Y%m%d"
            )
            df["option_id"] = df.option_typ.map({"CE": "1", "PE": "0"})
            df["id"] = df.symbol.map(symbol_to_balte_id).astype(str)

            df["balte_id"] = (
                df["strike_id"] + df["expiry_id"] + df["option_id"] + df["id"]
            )
            df["balte_id"] = df["balte_id"].astype("uint64")
            df.drop(columns=["strike_id", "expiry_id", "option_id", "id"], inplace=True)
            df.expiry = pd.to_datetime(df.expiry)
            # set index on timestamp
            df = df.rename(columns={"timestamp": "date"})
            df = df.set_index("date")

            ## Adding eod_price column for backtesting support
            df["eod_price"] = df["settlement"]

            return df

        elif bhav_universe == "index_bhav":
            df.columns = [
                "symbol",
                "date",
                "open",
                "high",
                "low",
                "close",
                "pc",
                "chg",
                "volume",
                "turnover",
                "pe",
                "pb",
                "divyield",
            ]
            df["date"] = pd.to_datetime(df.date, format="%d-%m-%Y")
            df["symbol"] = df.symbol.str.replace(" ", "_", regex=True)
            df = df[df.date == prev_date]
            if df.empty:
                raise Exception("Not latest bhavcopy")
            df.set_index(["date"], inplace=True)
            df = df[
                [
                    "symbol",
                    "pb",
                    "divyield",
                    "turnover",
                    "open",
                    "pe",
                    "volume",
                    "low",
                    "high",
                    "close",
                ]
            ]
            df = df.replace("-", np.nan)
            for col in df.columns:
                if col not in ["symbol"]:
                    df[col] = df[col].astype("float")
            return df

        elif bhav_universe == "futidx_fut_gift_bhav":
            df.columns = [x.lower() for x in df.columns]
            df = self._extract_from_contract(symbol=bhav_universe, data=df)
            df["date"] = prev_date
            df = df[~df["close_pric"].isna()]
            df["balte_id"] = df.symbol.map(symbol_to_balte_id)
            df["expiry_rank"] = df.groupby("balte_id").expiry.rank(method="dense")
            df = df[df.expiry_rank == 1]
            df["eod_price"] = df["close_pric"]
            df = df.set_index("date")
            df = df[["balte_id", "eod_price"]]
            return df
        else:
            raise Exception(f"Bhavcopy handling not added for {bhav_universe}")

    def _extract_from_contract(self, symbol: str, data: pd.DataFrame):
        if symbol in ["raw_futidx_fut_gift_bhav", "futidx_fut_gift_bhav"]:
            data["symbol"] = "GIFT_" + data["contract_d"].str[6:-11]
            data["series"] = data["contract_d"].str[:6]
            data["expiry"] = pd.to_datetime(
                data["contract_d"].str[-11:], format="%d-%b-%Y"
            )

        return data

    def append_raw_bhav_copy(self, symbol: str, date: Optional[pd.Timestamp] = None):
        """Appends raw bhav symbol wise

        Args:
            symbol (str): bhav copy universe
        """
        if date is None:
            date = self._config.DATE_TODAY - pd.Timedelta(days=1)

        library_name = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name=f"raw_{symbol}",
            dtype="trd",
            storage=StorageType.DB,
        )
        bhav_copy = pd.read_csv(
            BytesIO(
                self._get_from_minio(
                    file_location="commondata",
                    file_name=self._config.AFTER_MARKET_DICT_MINIO[symbol],
                ).data
            )
        )

        bhav_copy.columns = [col.lower() for col in bhav_copy.columns]

        if "timestamp" not in bhav_copy:
            bhav_copy["timestamp"] = date
        else:
            bhav_copy["timestamp"] = pd.to_datetime(
                bhav_copy["timestamp"], format="%d-%b-%Y"
            )
        bhav_copy = bhav_copy.set_index("timestamp")

        if "expiry_dt" in bhav_copy.columns:
            bhav_copy["expiry_dt"] = pd.to_datetime(
                bhav_copy["expiry_dt"], format="%d-%b-%Y"
            )

        bhav_copy = self._extract_from_contract(symbol=f"raw_{symbol}", data=bhav_copy)

        bhav_copy = self._handle_data(universe=f"raw_{symbol}", data=bhav_copy)

        symbol_list = self._tanki[library_name].list_symbols()
        for symbol_bhav, symbol_bhav_df in bhav_copy.groupby("symbol"):
            if str(symbol_bhav) not in symbol_list:
                self._tanki[library_name].write_metadata(
                    symbol=str(symbol_bhav), data=symbol_bhav_df
                )
                self._tanki[library_name].write(
                    symbol=str(symbol_bhav),
                    data=symbol_bhav_df,
                    comment="Daily data append",
                )
            else:
                metadata_dict = self._tanki[library_name].read_metadata(
                    symbol=str(symbol_bhav)
                )
                if (metadata_dict is not None) and (
                    pd.Timestamp(metadata_dict["last_timestamp"]) >= date
                ):
                    continue
                self._tanki[library_name].append(
                    symbol=str(symbol_bhav),
                    data=symbol_bhav_df,
                    comment="Daily data append",
                )

        return

    def append_daily_data_1440(self, universe, timing, symbol=None):
        library_name = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name=universe,
            dtype="trd",
            storage=StorageType.DB,
        )
        bhav_universe = symbol

        if timing == "morning":
            all_dates = np.load(
                BytesIO(
                    self._get_from_minio(
                        file_location=self._config.FILE_DICT["ALL_DATES"][0],
                        file_name=self._config.FILE_DICT["ALL_DATES"][1],
                    ).data
                ),
                allow_pickle=True,
            )
            prev_date = previous_date(all_dates)
            symbol_to_balte_id = pickle.load(
                BytesIO(
                    self._get_from_minio(
                        self._config.FILE_DICT["MAPPING_DICT"][0],
                        self._config.FILE_DICT["MAPPING_DICT"][1],
                    ).data
                )
            )
            repo_rate = self.__get_repo_rate()

            bhav_copy = pd.read_csv(
                BytesIO(
                    self._get_from_minio(
                        file_location="commondata",
                        file_name=self._config.AFTER_MARKET_DICT_MINIO[bhav_universe],
                    ).data
                )
            )
            try:
                processed_copy = self.__preprocess_bhav_data(
                    bhav_universe,
                    bhav_copy,
                    prev_date,
                    symbol_to_balte_id,
                    repo_rate,
                )

                processed_copy = self._handle_data(bhav_universe, processed_copy)

                if str(bhav_universe) not in self._tanki[library_name].list_symbols():
                    self._tanki[library_name].write_metadata(
                        symbol=str(bhav_universe), data=processed_copy
                    )
                    self._tanki[library_name].write(
                        symbol=str(bhav_universe), data=processed_copy
                    )
                else:
                    metadata_dict = self._tanki[library_name].read_metadata(
                        symbol=str(bhav_universe)
                    )
                    if (metadata_dict is not None) and (
                        pd.Timestamp(metadata_dict["last_timestamp"]) >= prev_date
                    ):
                        return
                    self._tanki[library_name].append(bhav_universe, processed_copy)
            except Exception as exception:
                raise Exception(
                    f"DailyAppendsError: {bhav_universe} failed to append due to {exception}\n"
                )

                # TODO: Needs to be implemented library "nse/1440_min/optstk_bhav/trd"

        elif timing == "evening":
            symbols_failed_to_append = []
            symbol_list = self._tanki[library_name].list_symbols()
            for symbol in self._config.AFTER_MARKET_DICT_GRPC:
                symbol_data = self._toti_obj.get_fresh_1440(
                    symbol, self._config.DATE_TODAY
                )
                if len(symbol_data) == 0:
                    continue
                try:
                    symbol_data = symbol_data.set_index("date")
                    symbol_data = self._handle_data(universe=symbol, data=symbol_data)
                    if str(symbol) not in symbol_list:
                        self._tanki[library_name].write_metadata(
                            symbol=str(symbol), data=symbol_data
                        )
                        self._tanki[library_name].write(
                            symbol=str(symbol), data=symbol_data
                        )
                    else:
                        metadata_dict = self._tanki[library_name].read_metadata(
                            symbol=str(symbol)
                        )
                        if (metadata_dict is not None) and (
                            pd.Timestamp(metadata_dict["last_timestamp"])
                            >= self._config.DATE_TODAY
                        ):
                            continue
                        self._tanki[library_name].append(symbol, symbol_data)
                except Exception as exception:
                    symbols_failed_to_append.append(f"{symbol}: {exception}")

            if symbols_failed_to_append:
                raise Exception(
                    f"DailyAppendsError: Following symbols are failed to append:\n{symbols_failed_to_append}\n"
                )
        else:
            raise Exception("DailyAppendsError: Invalid market timing!")

    def parse_and_upload_corpact(self, date, metafile_path):
        if date is None:
            date = pd.Timestamp.now().normalize()

        bhav_copy = pd.read_csv(
            self._get_from_minio(
                file_location="commondata", file_name="nse_daily_downloads/cm_bhav.csv"
            )
        )
        bhav_copy = bhav_copy[bhav_copy.SERIES.isin(["EQ", "BE", "BZ", "BT"])]
        bhav_copy = bhav_copy[
            [
                "SYMBOL",
                "TIMESTAMP",
                "CLOSE",
            ]
        ]
        bhav_copy.columns = [
            "symbol",
            "timestamp",
            "close",
        ]

        ## Create corpact df using files
        corpact_files_list = []
        corpact_files_to_pull = [
            "nse_daily_downloads/corpacts_1.csv",
            "nse_daily_downloads/corpacts_2.csv",
            "nse_daily_downloads/CA_NEXT_1_MONTH.csv",
        ]

        column_name_map = {
            "Face Value(Rs.)": "face_value",
            "Ex-Date": "exdate",
            "Record Date": "rec_date",
            "FACE VALUE": "face_value",
            "EX-DATE": "exdate",
            "RECORD DATE": "rec_date",
        }

        all_dates = np.load(
            BytesIO(
                self._get_from_minio(
                    file_location="commondata", file_name="balte_uploads/ALL_DATES.npy"
                ).data
            ),
            allow_pickle=True,
        )

        for file in corpact_files_to_pull:
            try:
                corpact_file = pd.read_csv(
                    self._get_from_minio(file_location="commondata", file_name=file)
                )
                corpact_file.columns = [
                    column_name_map[x] if x in column_name_map else x.lower()
                    for x in corpact_file.columns
                ]
                corpact_file = corpact_file[corpact_file["series"].isin(["EQ"])]
                corpact_file = corpact_file[
                    ["symbol", "face_value", "purpose", "exdate"]
                ]
                corpact_files_list.append(corpact_file)
            except Exception as e:
                print(f"Error in fetching {file} from Minio : {e}")

        if len(corpact_files_list) == 0:
            print(f"No file parsed for calculating Corpact for {date}")
            return
        else:
            corpact_file = pd.concat(corpact_files_list)

        ## MAIN CORPORATE ACTION LOGIC : ADJUSTMENT FACTOR CALCULATION:
        corpact_adj, bad_corpact = calculate_corpact_adj_factor(
            corpact_file, bhav_copy, date, all_dates
        )

        # corpact future made today
        corpact_today_and_future = corpact_adj[corpact_adj.exdate >= date]

        # old corpact with for dividend
        corpact_old_dividend = pd.read_csv(
            self._get_from_minio(
                file_location="commondata",
                file_name="arcticdb_files/corpact_futures_dividend.csv",
            ),
            parse_dates=["exdate"],
        )

        corpact_future = pd.merge(
            corpact_today_and_future,
            corpact_old_dividend,
            on=["symbol", "type", "exdate"],
            how="left",
        )
        corpact_future.loc[
            ~corpact_future["adj_factor_y"].isna(), "adj_factor_x"
        ] = corpact_future.loc[~corpact_future["adj_factor_y"].isna()].adj_factor_y
        corpact_future.drop_duplicates(
            subset=["symbol", "type", "exdate", "adj_factor_x"],
            keep="first",
            inplace=True,
        )

        # corpact_future is the corpact future with 2 % dividend picked using their date of announcement
        corpact_future = corpact_future.rename(
            columns={"adj_factor_x": "adj_factor"}
        ).drop(columns=["adj_factor_y"])

        # new dividend corpact
        corpact_future_dividend = corpact_future[(corpact_future["type"] == "DIVIDEND")]

        corpact_today = corpact_today_and_future[
            corpact_today_and_future.exdate == date
        ]
        corpact_in_future = corpact_future

        corpact_today.to_hdf(
            metafile_path + "corpact_today", "corpact_today", format="table"
        )
        corpact_in_future["adj_factor"] = corpact_in_future["adj_factor"].astype(
            "float"
        )
        corpact_in_future.to_hdf(
            metafile_path + "corpact_futures",
            "corpact_futures",
            format="table",
        )
        corpact_future_dividend.to_csv(
            metafile_path + "corpact_futures_dividend.csv", index=False
        )
        ## To CSV
        corpact_today[["symbol", "type", "exdate", "adj_factor"]].to_csv(
            metafile_path + "corpact_today.csv", index=False
        )
        corpact_in_future[["symbol", "type", "exdate", "adj_factor"]].to_csv(
            metafile_path + "corpact_futures.csv", index=False
        )
        bad_corpact.to_csv(metafile_path + "bad_corpact_today.csv", index=False)

        # MINIO Upload
        files_to_minio = [
            "corpact_today",
            "corpact_today.csv",
            "corpact_futures",
            "corpact_futures.csv",
            "corpact_futures_dividend.csv",
            "bad_corpact_today.csv",
        ]

        file_upload_failed = []
        for file in files_to_minio:
            try:
                self._upload_to_minio(
                    file_location="commondata",
                    file_name="arcticdb_files/" + file,
                    file=metafile_path + file,
                )
            except Exception as e:
                file_upload_failed.append(f"Failed to push {file} to Minio: {e}")

        if len(file_upload_failed) > 0:
            raise Exception(file_upload_failed)

        return

    def check_bad_corpact(self):
        """
        Checks the bad corpact file formed during corpact parsing
        """
        bad_copract = pd.read_csv(
            self._get_from_minio(
                file_location="commondata",
                file_name="arcticdb_files/bad_corpact_today.csv",
            ),
        )
        if len(bad_copract) > 0:
            raise Exception(f"Found bad corpacts:\n{bad_copract}")

    def parse_demerger_merger(self, date: pd.Timestamp, metafile_path: str):
        """Parse the corpact files for demerger/merger info and upload demerger_merger.csv on minio

        Args:
            date (pd.Timestamp): date
            metafile_path (str): path for storing csv locally

        Raises:
            Exception: List of demerger/merger
        """

        if date is None:
            date = self._config.DATE_TODAY

        corpact_files_list = []
        corpact_files_to_pull = [
            "nse_daily_downloads/corpacts_1.csv",
            "nse_daily_downloads/corpacts_2.csv",
            "nse_daily_downloads/CA_NEXT_1_MONTH.csv",
        ]

        column_name_map = {
            "Face Value(Rs.)": "face_value",
            "Ex-Date": "exdate",
            "Record Date": "rec_date",
            "FACE VALUE": "face_value",
            "EX-DATE": "exdate",
            "RECORD DATE": "rec_date",
        }

        for file in corpact_files_to_pull:
            try:
                corpact_file = pd.read_csv(
                    self._get_from_minio(file_location="commondata", file_name=file)
                )
                corpact_file.columns = [
                    column_name_map[x] if x in column_name_map else x.lower()
                    for x in corpact_file.columns
                ]
                corpact_file = corpact_file[corpact_file["series"].isin(["EQ"])]
                corpact_file = corpact_file[
                    ["symbol", "face_value", "purpose", "exdate"]
                ]
                corpact_files_list.append(corpact_file)
            except Exception as e:
                print(f"Error in fetching {file} from Minio : {e}")

        if len(corpact_files_list) == 0:
            print(f"No file parsed for calculating Corpact for {date}")
            return
        else:
            corpact_file = pd.concat(corpact_files_list)

        demerger_merger = get_merger_demerger(corpact_df=corpact_file)
        if len(demerger_merger) > 0:
            demerger_merger.to_csv(metafile_path + "demerger_merger.csv", index=False)
            self._upload_to_minio(
                file_location="commondata",
                file_name="arcticdb_files/demerger_merger.csv",
                file=metafile_path + "demerger_merger.csv",
            )
            raise Exception(f"Found Demerger/Merger\n{demerger_merger}")

    def update_symbol_change(self):
        """Updates symbol change metadata"""
        ## new_symbol, old_symbol, modification_date
        ## this file is only updated when any symbol change is observed
        new_symbol_change = pd.read_csv(
            self._get_from_minio(
                file_location="commondata",
                file_name="132_comp_files/changed_symbol.csv",
            )
        )
        if len(new_symbol_change) == 0:
            print("No symbol change to update!")
            return

        new_symbol_change.columns = ["current", "symbol", "date"]
        new_symbol_change["date"] = pd.to_datetime(
            new_symbol_change.date, format="%Y-%m-%d"
        )
        new_symbol_change = new_symbol_change.set_index("date")
        new_symbol_change = new_symbol_change[["symbol", "current"]]
        new_symbol_change = new_symbol_change[
            new_symbol_change.index == self._config.DATE_TODAY
        ]

        if len(new_symbol_change) == 0:
            print("No symbol change to update!")
            return

        after_market_library = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name="after_market",
            dtype="trd",
            storage=StorageType.DB,
        )

        metadata_dict = self._tanki[after_market_library].read_metadata(
            symbol="symbol_change"
        )
        if (metadata_dict is not None) and (
            pd.Timestamp(metadata_dict["last_timestamp"]) >= self._config.DATE_TODAY
        ):
            print("Symbol change already updated!")
            return

        old_symbol_change = self._tanki[after_market_library].read("symbol_change")

        update_symbol_change = old_symbol_change[
            old_symbol_change.current.isin(new_symbol_change.symbol.tolist())
        ]
        old_symbol_change = old_symbol_change[
            ~old_symbol_change.current.isin(new_symbol_change.symbol.tolist())
        ]

        update_symbol_change = update_symbol_change[
            update_symbol_change.symbol != update_symbol_change.current
        ]
        update_symbol_change["current"] = update_symbol_change["current"].map(
            dict(zip(new_symbol_change.symbol, new_symbol_change.current))
        )

        current_to_current = pd.DataFrame(
            {
                "symbol": new_symbol_change.current.values,
                "current": new_symbol_change.current.values,
            },
            index=[self._config.DATE_TODAY] * len(new_symbol_change),
        )

        final_symbol_change = pd.concat(
            [
                old_symbol_change,
                update_symbol_change,
                new_symbol_change,
                current_to_current,
            ]
        )
        final_symbol_change = final_symbol_change.sort_index()
        final_symbol_change.index.name = old_symbol_change.index.name

        self._tanki[after_market_library].write(
            symbol="symbol_change",
            data=final_symbol_change,
            comment="Updated symbol change!",
        )

    def apply_symbol_change_on_demerger_merger(self):
        """Apply symbol change on demerger merger metadata"""
        new_symbol_change = pd.read_csv(
            self._get_from_minio(
                file_location="commondata",
                file_name="132_comp_files/changed_symbol.csv",
            )
        )
        if len(new_symbol_change) == 0:
            print("No symbol change to update!")
            return

        new_symbol_change.columns = ["current", "symbol", "date"]
        new_symbol_change["date"] = pd.to_datetime(
            new_symbol_change.date, format="%Y-%m-%d"
        )
        new_symbol_change = new_symbol_change.set_index("date")
        new_symbol_change = new_symbol_change[["symbol", "current"]]
        new_symbol_change = new_symbol_change[
            new_symbol_change.index == self._config.DATE_TODAY
        ]

        if len(new_symbol_change) == 0:
            print("No symbol change to update!")
            return

        after_market_library = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name="after_market",
            dtype="trd",
            storage=StorageType.DB,
        )

        demerger_merger = self._tanki[after_market_library].read("demerger_merger")

        demerger_merger["Symbol"] = demerger_merger["Symbol"].replace(
            dict(zip(new_symbol_change["symbol"], new_symbol_change["current"]))
        )

        self._tanki[after_market_library].write(
            symbol="demerger_merger",
            data=demerger_merger,
            comment="Applying symbol change on demerger merger!",
        )

    def append_corpact(self, date: pd.Timestamp, metafile_path: str):
        """Append corpact to after_market library

        Args:
            date (pd.Timestamp): date
            metafile_path (str): path for storing csv locally
        """
        if date is None:
            date = self._config.DATE_TODAY

        corpact = pd.read_csv(metafile_path + "corpact_today.csv")
        corpact = corpact[["exdate", "symbol", "adj_factor"]]
        corpact["exdate"] = pd.to_datetime(corpact["exdate"], format="%Y-%m-%d")
        corpact = corpact.set_index("exdate")
        corpact = corpact[corpact.index == date]

        if len(corpact) == 0:
            print("No corpacts for today!")
            return

        after_market_library = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name="after_market",
            dtype="trd",
            storage=StorageType.DB,
        )
        metadata_dict = self._tanki[after_market_library].read_metadata(
            symbol="corpact"
        )
        if metadata_dict is None:
            raise Exception("Metadata not found for corpact data")
        if pd.Timestamp(metadata_dict["last_timestamp"]) >= self._config.DATE_TODAY:
            print("Corpact data already appended!")
            return

        symbol_to_balte_id = pickle.load(
            BytesIO(
                self._get_from_minio(
                    self._config.FILE_DICT["MAPPING_DICT"][0],
                    self._config.FILE_DICT["MAPPING_DICT"][1],
                ).data
            )
        )
        corpact["ID"] = corpact["symbol"].map(symbol_to_balte_id)
        corpact = corpact[["ID", "adj_factor"]]
        try:
            self._tanki[after_market_library].append(
                "corpact", corpact, comment="Daily data append: corpact"
            )
        except Exception as exception:
            raise Exception(f"Appends failed for corpact data due to {exception}")

    def append_filtering_list(self, date: pd.Timestamp, filtering_list_name: str):
        """Append filtering lists like isfno, isliquid, etc. to after_market universe

        Args:
            date (pd.Timestamp): date
            filtering list name (str): list to be appended
        """
        if date is None:
            date = self._config.DATE_TODAY

        after_market_library = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name="after_market",
            dtype="trd",
            storage=StorageType.DB,
        )
        metadata_dict = self._tanki[after_market_library].read_metadata(
            symbol=filtering_list_name
        )
        if metadata_dict is None:
            raise Exception(f"Metadata not found for {filtering_list_name} data")

        if pd.Timestamp(metadata_dict["last_timestamp"]) >= self._config.DATE_TODAY:
            print(f"{filtering_list_name} data already appended!")
            return

        try:
            data = self._toti_obj.get_fresh_1440(
                filtering_list_name, self._config.DATE_TODAY
            )[["date", "ID"]]
            data = data.set_index("date")
            self._tanki[after_market_library].append(
                symbol=filtering_list_name,
                data=data,
                comment=f"Daily data append: {filtering_list_name}",
            )
        except Exception as exception:
            raise (f"Appends failed for {filtering_list_name} data due to {exception}")

    def append_daily_derived_universe(
        self,
        universe: str,
        frequency: int,
        today: pd.Timestamp,
        lookback: int,
        base_universe: str,
        filtering_list_name: str,
    ):
        """
        Creation of EQ, RAW_EQ, FNO from cash universe
        """
        end_date = today
        all_dates = np.load(
            BytesIO(
                self._get_from_minio(
                    file_location="commondata",
                    file_name="balte_uploads/ALL_DATES.npy",
                ).data
            ),
            allow_pickle=True,
        )
        start_date = previous_date(all_dates, end_date, lookback)

        base_library_name = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=frequency,
            universe_name=base_universe,
            dtype="trd",
            storage=StorageType.DB,
        )

        base_library = self._tanki[base_library_name]
        symbol_list = base_library.list_symbols()

        aftermarket_library_name = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name="after_market",
            dtype="trd",
            storage=StorageType.DB,
        )
        aftermarket_library = self._tanki[aftermarket_library_name]
        filtering_list = aftermarket_library.read(
            filtering_list_name, start_date=start_date
        )[["ID"]]
        filtering_list = filtering_list.reset_index()
        df_list = []

        for ID in filtering_list.ID.unique():
            if str(ID) not in symbol_list:
                print(
                    "Warning : SYMBOL_ID = ", ID, " is not present in the cash universe"
                )
                continue
            data_df = base_library.read(symbol=str(ID), start_date=start_date)
            if len(data_df) > 0:
                df_list.append(data_df)

        final_df = pd.concat(df_list).reset_index()
        self.__append_to_derived_columnbucket(universe, final_df, start_date, frequency)

        if universe in ["fno"]:
            self.__append_fut_close(frequency=frequency)
        return

    def __append_to_derived_columnbucket(
        self,
        universe: str,
        final_df: pd.DataFrame,
        start_date: pd.Timestamp,
        frequency: int,
    ):
        """
        Writes df to column bucket library
        """
        print(f"Writing to column bucket for {universe}")
        library_name = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=frequency,
            universe_name=universe,
            dtype="column_bucket",
            storage=StorageType.DB,
        )
        library = self._tanki[library_name]

        if len(final_df) == 0:
            print(f"No data to append for {universe}")
            return
        columns_list = final_df.columns.values.tolist()
        final_df["date"] = final_df.timestamp.dt.normalize()
        final_df["time"] = final_df.timestamp.dt.time
        final_df = final_df.set_index(["date", "ID"]).pivot(columns="time")

        for column in columns_list:
            if column in ["timestamp", "ID", "date", "time"]:
                continue
            df_col = final_df[column].rename_axis(None, axis=1)
            new_col_list = []
            for time_col in df_col.columns:
                new_col_list.append(time_col.strftime("%H:%M"))
            # df_col = column_bucket_casting(
            #     arctic_config.UNIVERSE_LIST[universe_name]["universe"],
            #     col,
            #     df_col.reset_index(),
            # )
            df_col.columns = new_col_list

            complete_historical_data = library.read(column)

            ## TODO: Use arctic update for this operation instead of write
            df_col2 = complete_historical_data[
                complete_historical_data.index.get_level_values(0) >= start_date
            ]
            complete_historical_data = complete_historical_data[
                complete_historical_data.index.get_level_values(0) < start_date
            ]

            df_col3 = pd.concat([df_col, df_col2])
            df_col3 = df_col3[~df_col3.index.duplicated(keep="first")].sort_index()

            final_data = pd.concat([complete_historical_data, df_col3])

            library.write(column, final_data)

        print(f"Completed Column bucket for {universe} with {frequency}")
        return

    def __append_fut_close(self, frequency: int):
        try:
            fno_library_name = get_library_name(
                exchange_name=self._config.EXCHANGE,
                frequency=frequency,
                universe_name="fno",
                dtype="column_bucket",
                storage=StorageType.DB,
            )
            fut_library_name = get_library_name(
                exchange_name=self._config.EXCHANGE,
                frequency=frequency,
                universe_name="fut",
                dtype="column_bucket",
                storage=StorageType.DB,
            )

            fno_library = self._tanki[fno_library_name]
            fut_library = self._tanki[fut_library_name]

            fno_data = fno_library.read("Close").reset_index()
            fut_data = fut_library.read("Close").reset_index()

            fno_data = fno_data[["date", "ID"]]

            fut_close_data = pd.merge(fno_data, fut_data, on=["date", "ID"], how="left")
            fut_close_data = fut_close_data.set_index(["date", "ID"])

            fno_library.write_metadata("fut_close", fut_close_data)
            fno_library.write("fut_close", fut_close_data)
        except Exception as exception:
            raise Exception(f"Appends failed for fut_close due to: {exception}")

    def _get_lotsize(self, universe):
        after_market_library = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name="after_market",
            dtype="trd",
            storage=StorageType.DB,
        )
        lotsize = self._tanki[after_market_library].read(
            self._config.UNIVERSE_TO_LOTSIZE_DICT[universe]
        )
        if "far_month" not in lotsize and "next_month" in lotsize:
            lotsize["far_month"] = lotsize["next_month"]

        return lotsize

    def apply_corpact(
        self,
        universe: str,
        dtype: str = "trd",
        frequency: int = 5,
        metafile_path: str = "",
    ):
        """apply corpact on universe library

        Args:
            universe (str): name of universe
            dtype (str, optional): trd/ord/column_bucket. Defaults to "trd".
            frequency (int, optional): sampled frequency. Defaults to 5.
            metafile_path (str, optional): path to corpact file. Defaults to "".
        """

        symbol_to_balte_id = pickle.load(
            BytesIO(
                self._get_from_minio("commondata", "balte_uploads/mapping_dict").data
            )
        )
        today_corpact = pd.read_csv(
            f"{metafile_path}corpact_today.csv"
        )  # fetch from minio
        today_corpact["date"] = pd.Timestamp.now().normalize()
        today_corpact["ID"] = today_corpact["symbol"].map(symbol_to_balte_id)
        today_corpact = today_corpact[["date", "ID", "adj_factor"]]
        today_corpact = today_corpact.groupby(["ID", "date"], as_index=False).agg(
            {"adj_factor": "prod"}
        )
        library_name = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=frequency,
            universe_name=universe,
            dtype=dtype,
            storage=StorageType.DB,
        )

        if universe == "after_market":
            self.__apply_corpact_after_market_universe(
                library_name=library_name, today_corpact=today_corpact
            )
        elif universe in self._config.CORPACT_UNIVERSES and dtype == "column_bucket":
            try:
                lotsize = None
                if universe in self._config.UNIVERSE_TO_LOTSIZE_DICT.keys():
                    lotsize = self._get_lotsize(universe=universe)
                self.__apply_corpacts_column_bucket(
                    library_name, today_corpact, lotsize
                )
            except Exception as e:
                raise Exception(
                    f"Corpact Apply failed for {universe} column bucket in corpact due to {repr(e)}"
                )
        elif universe in self._config.CORPACT_UNIVERSES:
            lotsize = None
            if universe in self._config.UNIVERSE_TO_LOTSIZE_DICT.keys():
                lotsize = self._get_lotsize(universe=universe)
            failed_syms = self.__apply_corpacts_universe(
                library_name,
                today_corpact,
                symbol_to_balte_id,
                lotsize,
            )
            if len(failed_syms):
                raise Exception(
                    f"Corpact apply failed for following symbols:\n{failed_syms}"
                )
        elif universe == "sector_wts":
            library_name = get_library_name(
                exchange_name=self._config.EXCHANGE,
                frequency=1440,
                universe_name="after_market",
                dtype=dtype,
                storage=StorageType.DB,
            )
            try:
                self.__apply_corpacts_sectors(
                    today_corpact,
                    library_name=library_name,
                    symbol_to_balte_id=symbol_to_balte_id,
                )
            except Exception as e:
                raise Exception(
                    f"Corpact Apply failed for sectors_wts in corpact due to {repr(e)}"
                )

    def __apply_corpact_after_market_universe(
        self, library_name: str, today_corpact: pd.DataFrame
    ):
        for universe in self._config.AFTER_MARKET_CORPACTS_UNIVERSES:
            corpact_applied = False
            data = self._tanki[library_name].read(universe)
            meta_data = self._tanki[library_name].read_metadata(universe)

            if "last_corpact_applied" in meta_data:
                if (
                    pd.Timestamp(meta_data["last_corpact_applied"])
                    >= pd.Timestamp.now().normalize()
                ):
                    print(
                        f"For {universe}, last corpact applied on : {meta_data['last_corpact_applied']}"
                    )
                    print(f"Skipping corpact application for {universe}")
                    continue

            id_col = "balte_id" if "balte_id" in data.columns else "ID"
            for row_index in range(len(today_corpact)):
                row = today_corpact.iloc[row_index]
                print(f"Applying Corpact on {row.ID} adj_factor : {row.adj_factor}")
                for col in data.columns:
                    if col in self._config.CORPACT_ADJUSTMENT_ACTIONS:
                        data.loc[
                            data[id_col] == row.ID, col
                        ] = self._config.CORPACT_ADJUSTMENT_ACTIONS[col](
                            data.loc[data[id_col] == row.ID, col],
                            row.adj_factor,
                        )
                        corpact_applied = True

            try:
                if corpact_applied:
                    self._tanki[library_name].write(
                        universe, data, comment="corpact_applied"
                    )
            except Exception as e:
                raise Exception(
                    f"Corpact Apply failed for {universe} in bhav copy due to {repr(e)}"
                )

    def __apply_corpacts_universe(
        self,
        library_name: str,
        today_corpact: pd.DataFrame,
        symbol_to_balte_id: Dict[str, int],
        lotsize: Optional[int] = None,
    ) -> List[str]:
        """
        Apply given date's corpact action on the data of universe.

        Args:
            library_name (str): library name
            today_corpact (pd.DataFrame): corpact needs to be applied today
            symbol_to_balte_id (Dict[str,int]): symbol to balte ID mapping
            lotsize (Optional[int], optional): lot size. Defaults to None.

        Returns:
            A list of all the symbols whose corpact application failed.
        """
        failed_syms = []
        library = self._tanki[library_name]

        for row_index in range(len(today_corpact)):
            try:
                today_corpact_id = today_corpact.iloc[row_index]

                # Check if we have symbol-id mapping and data of given universe
                if today_corpact_id["ID"] not in symbol_to_balte_id.values():
                    print(
                        f"Corporate Action Error: {today_corpact_id['symbol']} is not present in mapping or {library_name} data is not present in Arctic"
                    )
                    continue
                if library_name not in self._tanki.list_libraries():
                    print(
                        f"Corporate Action Error: {library_name} data is not present in Arctic"
                    )
                    continue

                balte_id = int(today_corpact_id["ID"])
                factor = today_corpact_id["adj_factor"]

                # Do nothing if symbol is not present in database or corporate action is already applied
                if str(balte_id) not in library.list_symbols():
                    print(str(balte_id) + " is not present in " + library_name)
                    continue

                symbol_data = library.read(str(balte_id))
                meta_data = library.read_metadata(str(balte_id))

                if meta_data is None:
                    failed_syms.append(
                        (balte_id, "Skipping corpact as Metadata not found")
                    )
                    continue

                if "last_corpact_applied" in meta_data:
                    if (
                        pd.Timestamp(meta_data["last_corpact_applied"])
                        >= pd.Timestamp.now().normalize()
                    ):
                        print(
                            f"For symbol {balte_id}, last corpact applied on : {meta_data['last_corpact_applied']}"
                        )
                        print(f"Skipping corpact application for {balte_id}")
                        continue

                # Apply adjustment on each column of data
                for col in symbol_data:
                    if col in self._config.CORPACT_ADJUSTMENT_ACTIONS:
                        symbol_data.loc[
                            symbol_data.index < today_corpact_id.date, col
                        ] = self._config.CORPACT_ADJUSTMENT_ACTIONS[col](
                            symbol_data.loc[
                                symbol_data.index < today_corpact_id.date, col
                            ],
                            factor,
                        )

                if lotsize is not None:
                    lotsize_id = lotsize[lotsize.ID == balte_id]
                    symbol_data = round_to_nearest_lotsize(
                        df=symbol_data, lotsize=lotsize_id
                    )

                library.write(str(balte_id), symbol_data, comment="corpact_applied")
            except Exception as e:
                failed_syms.append((balte_id, f"Corpact applied failed due to {e}"))
                continue

        return failed_syms

    def __apply_corpacts_sectors(
        self,
        today_corpact: pd.DataFrame,
        library_name: str,
        symbol_to_balte_id: Dict[str, int],
    ):
        """apply corpacts on sector data

        Args:
            today_corpact (pd.DataFrame): ID whose corpact needs to be applied today
            library_name (str): library name
            symbol_to_balte_id (Dict[str,int]): symbol to balte ID mapping
        """
        library = self._tanki[library_name]
        sector_wts = library.read("sector_wts")

        # Meta data checks, if corpact already applied skip
        meta_data = library.read_metadata("sector_wts")
        if meta_data is None:
            meta_data = {}
        if "last_corpact_applied" in meta_data:
            if (
                pd.Timestamp(meta_data["last_corpact_applied"])
                >= pd.Timestamp.now().normalize()
            ):
                print(
                    f"For sector_wts, last_corpact_applied on : {meta_data['last_corpact_applied']}"
                )
                print("Skipping corpact application for sector_wts")
                return

        id_list = sector_wts["ID"].unique()
        corpact_applied = False
        for row_index in range(len(today_corpact)):
            row = today_corpact.iloc[row_index]
            if row["ID"] not in symbol_to_balte_id.values():
                continue

            balte_id = int(row["ID"])
            factor = row["adj_factor"]

            if balte_id in id_list:
                for col in sector_wts.columns:
                    if col in self._config.CORPACT_ADJUSTMENT_ACTIONS:
                        sector_wts.loc[
                            sector_wts.ID == balte_id, col
                        ] = self._config.CORPACT_ADJUSTMENT_ACTIONS[col](
                            sector_wts.loc[sector_wts.ID == balte_id, col], factor
                        )
                        corpact_applied = True

        if corpact_applied:
            library.write("sector_wts", sector_wts, comment="corpact_applied")
            print("Corporate Action successfully applied on sector_wts")

    def __apply_corpacts_column_bucket(
        self,
        library_name: str,
        today_corpact_combined: pd.DataFrame,
        lotsize: Optional[pd.DataFrame] = None,
    ):
        """Apply corpact on column buckets library

        Args:
            library_name (str): name of the library
            today_corpact_combined (pd.DataFrame): combined corpact for today
        """
        if len(today_corpact_combined) == 0:
            print("No corpact to apply!")
            return

        library = self._tanki[library_name]

        for col in library.list_symbols():
            meta_data = library.read_metadata(col)
            if "last_corpact_applied" in meta_data:
                if (
                    pd.Timestamp(meta_data["last_corpact_applied"])
                    >= pd.Timestamp.now().normalize()
                ):
                    print(
                        f"Coporate action already applied on {library_name} column bucket with {col} data"
                    )
                    continue
            symbol_data = library.read(col)

            for balte_id, adj_fac in list(
                zip(today_corpact_combined.ID, today_corpact_combined.adj_factor)
            ):
                symbol_data.loc[
                    (symbol_data.index.get_level_values("ID") == balte_id)
                ] = self._config.CORPACT_ADJUSTMENT_ACTIONS[col](
                    symbol_data.loc[
                        (symbol_data.index.get_level_values("ID") == balte_id)
                    ],
                    adj_fac,
                )
                if lotsize is not None:
                    lotsize_id = lotsize[lotsize["ID"] == balte_id]
                    symbol_data.loc[
                        symbol_data.index.get_level_values("ID") == balte_id, :
                    ] = round_to_nearest_lotsize_column_bucket(
                        df=symbol_data.loc[
                            symbol_data.index.get_level_values("ID") == balte_id, :
                        ],
                        lotsize_df=lotsize_id,
                        col=col,
                    )

            library.write(col, symbol_data, comment="corpact_applied")

    def append_segment_data(
        self, exchange: str, segment: str, frequency: int, dtype: str
    ):
        """Appends segment raw data to exchange/SAMPLER_DUMP

        Args:
            exchange (str): exchange name
            segment (str): segment name
            frequency (int): sampling frequency
            dtype (str): trd or ord
        """
        library_name = f"{exchange}/1440_min/SAMPLER_DUMP/trd"

        segment_data = self._toti_obj.get_fresh_raw_full_day(
            segment, frequency, dtype, self._config.DATE_TODAY
        )
        segment_symbol = f"{segment}_{frequency}_min_{dtype}"
        if str(segment_symbol) not in self._tanki[library_name].list_symbols():
            self._tanki[library_name].write_metadata(
                symbol=str(segment_symbol), data=segment_data
            )
            self._tanki[library_name].write(
                symbol=str(segment_symbol),
                data=segment_data,
                comment="Daily data append",
            )
        else:
            metadata_dict = self._tanki[library_name].read_metadata(
                symbol=str(segment_symbol)
            )
            if (metadata_dict is not None) and (
                pd.Timestamp(metadata_dict["last_timestamp"]) >= self._config.DATE_TODAY
            ):
                return
            self._tanki[library_name].append(
                symbol=str(segment_symbol),
                data=segment_data,
                comment="Daily data append",
            )
        return

    def create_slippage_data(
        self, universe: str, base_universe: str, date: pd.Timestamp, lookback: int = 300
    ):
        """Creates slippages data and append it in arctic in column bucket format

        Args:
            universe (str): Name of slippage universe
            base_universe (str): Name of base universe for which slippages needs to be calculated
            date (pd.Timestamp): Date of append
            lookback (int, optional): lookback period. Defaults to 300.
        """

        if universe == "new_eq_slippage":
            start_date = previous_date(self._all_dates, date, lookback)

            isnifty_library_name = get_library_name(
                exchange_name=self._config.EXCHANGE,
                frequency=1440,
                universe_name="after_market",
                dtype="trd",
                storage=StorageType.DB,
            )
            library_name = get_library_name(
                exchange_name=self._config.EXCHANGE,
                frequency=5,
                universe_name=base_universe,
                dtype="column_bucket",
                storage=StorageType.DB,
            )

            isnifty = self._tanki[isnifty_library_name].read(
                symbol="isnifty", start_date=start_date
            )
            isnifty = isnifty.reset_index()
            isnifty["isnifty"] = True

            library = self._tanki[library_name]
            columns_required = ["High", "Low", "Close", "Cons_Volume"]
            data = {}
            for column in columns_required:
                data[column] = library.read(column, start_date=start_date)
                data[column] = data[column].ffill(axis=1)

            # Feature 1 -- volatility
            volat_5min = np.log(data["High"] / data["Low"])
            feature1 = stack_column_bucket_data(
                volat_5min, "volatility_5min"
            ).reset_index()

            # Feature 2 -- 5 min turnover
            vol_diff = data["Cons_Volume"].diff(axis=1)
            vol_diff["09:20"] = data["Cons_Volume"]["09:20"]
            turnover = data["Close"] * vol_diff
            feature2 = stack_column_bucket_data(turnover, "turnover_5min").reset_index()

            features = feature1.merge(feature2, on=["timestamp", "ID"], how="inner")
            features["date"] = features.timestamp.dt.normalize()
            features = features.merge(isnifty, on=["date", "ID"], how="left")
            features.isnifty = features.isnifty.fillna(False)
            features = features.drop(columns=["date"]).set_index("timestamp")

            eq_slippage_library_name = get_library_name(
                exchange_name=self._config.EXCHANGE,
                frequency=5,
                universe_name="slippages",
                dtype="trd",
                storage=StorageType.DB,
            )
            self.__add_slippage_data_to_arctic(
                eq_slippage_library_name, universe, features, date, lookback
            )

        elif universe == "new_fno_slippage":
            start_date = previous_date(self._all_dates, date, lookback + 10)
            library_name = get_library_name(
                exchange_name=self._config.EXCHANGE,
                frequency=5,
                universe_name=base_universe,
                dtype="column_bucket",
                storage=StorageType.DB,
            )

            library = self._tanki[library_name]
            columns_required = ["High", "Low", "Close", "Cons_Volume"]
            data = {}
            for column in columns_required:
                data[column] = library.read(symbol=column, start_date=start_date)
                data[column] = data[column].ffill(axis=1)

            # Feature 1 -- volatility
            volat_5min = np.log(data["High"] / data["Low"])
            feature1 = stack_column_bucket_data(
                volat_5min, "volatility_5min"
            ).reset_index()

            # Feature 2 -- 5 min turnover
            vol_diff = data["Cons_Volume"].diff(axis=1)
            vol_diff["09:20"] = data["Cons_Volume"]["09:20"]
            turnover = data["Close"] * vol_diff
            feature2 = stack_column_bucket_data(turnover, "turnover_5min").reset_index()
            features = feature1.merge(feature2, on=["timestamp", "ID"], how="inner")
            features["date"] = features.timestamp.dt.normalize()

            # Feature 3 -- Cross sectional rank of last 10 days mean turnover
            turnover = data["Close"]["15:25"] * data["Cons_Volume"]["15:25"]
            rolling_turnover_mean = (
                turnover.groupby(level="ID", group_keys=False)
                .rolling(window=10)
                .mean()
                .sort_index(level=0)
            )
            if int(pd.__version__[0]) >= 1:
                rolling_turnover_mean.index = rolling_turnover_mean.index.droplevel(0)
            turnover_cr = rolling_turnover_mean.groupby(level=0).rank(pct=True)
            feature3 = turnover_cr.groupby(level=[1]).shift(1)
            feature3.name = "turnover_cr"
            features = features.merge(
                feature3.reset_index(), on=["date", "ID"], how="left"
            )
            features = features.drop(columns=["date"]).set_index("timestamp")

            fno_slippage_library_name = get_library_name(
                exchange_name=self._config.EXCHANGE,
                frequency=5,
                universe_name="slippages",
                dtype="trd",
                storage=StorageType.DB,
            )
            self.__add_slippage_data_to_arctic(
                fno_slippage_library_name, universe, features, date, lookback
            )

    def __add_slippage_data_to_arctic(
        self,
        arctic_library_name: str,
        universe_name: str,
        data: pd.DataFrame,
        end_date: pd.Timestamp,
        lookback: int,
    ):
        """Appends passed data to given arctic library with symbol as column name

        Args:
            arctic_library_name (str): Name of the Arctic library.
            universe_name (str): Name of the universe.
            data (pandas.DataFrame): Data to be appended.
            end_date (pandas.Timestamp): Date till which data needs to be pushed
            lookback (int): Number of lookback days.
        """
        start_date = previous_date(self._all_dates, end_date, lookback)
        metadata_dict = self._tanki[arctic_library_name].read_metadata(
            symbol=universe_name
        )
        if "last_timestamp" in metadata_dict:
            if pd.Timestamp(metadata_dict["last_timestamp"]) >= end_date.normalize():
                print(f"Data already appended for {universe_name}, skipping it!")
                return

        slippage_data = self._tanki[arctic_library_name].read(universe_name)
        old_slippage_data = slippage_data[
            slippage_data.index.get_level_values(0) <= start_date.normalize()
        ]
        data = data[(data.index.get_level_values(0) > start_date.normalize())]
        data = pd.concat([old_slippage_data, data])

        self._tanki[arctic_library_name].write(symbol=universe_name, data=data)
