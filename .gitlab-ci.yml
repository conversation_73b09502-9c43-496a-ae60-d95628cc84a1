image: continuumio/miniconda3:latest

before_script:
  - conda env create -f environment.yml
  - source activate data_auditing_env

stages:
  - linting
  - mypy
  - test
  - coverage

black_and_flake8_check:
  stage: linting
  script:
    - black --check .
    - flake8 .

mypy_check:
  stage: mypy
  script:
    - mypy .

run_tests:
  stage: test
  script:
    - COVERAGE_FILE=.coverage.unit coverage run --rcfile=.coveragerc -m pytest
  artifacts:
    paths:
      - .coverage.unit
    expire_in: 1 hour

coverage_report:
  stage: coverage
  script:
    - coverage combine --rcfile=.coveragerc
    - coverage report
    - coverage xml -o coverage.xml
    - coverage html
  coverage: '/\d+\%\s*$/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - htmlcov
  dependencies:
    - run_tests