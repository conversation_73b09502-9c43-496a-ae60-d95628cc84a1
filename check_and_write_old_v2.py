from arcticdb import Arctic as Arcticnew 
from arctic import Arctic as Arcticold
from arctic.date import DateRange
import pandas as pd
from pymongo import MongoClient


mongo_connection_path = "mongodb://192.168.0.142:27016/"
MONGO_CLIENT_USERNAME = "kiviArcticReader"
MONGO_CLIENT_PASSWORD = "kivi"

store_new = Arcticnew("s3://192.168.0.121:9000:kivi-arcticdb?access=super&secret=doopersecret")
mongo_client = MongoClient(mongo_connection_path,username=MONGO_CLIENT_USERNAME,password=MONGO_CLIENT_PASSWORD)
store_old = Arcticold(mongo_client)

libs = store_new.list_libraries()
exchange = "bse"
libs = [x for x in libs if f"{exchange}_old_v2" in x]
libs = [x for x in libs if "SAMPLER_DUMP" not in x]
    
for lib in libs:
    old_lib = lib.replace("_old_v2","")
    lib_new = store_new[lib]
    lib_old = store_old[old_lib]
    
    if set(lib_new.list_symbols()) != set(lib_old.list_symbols()):
        print(f"Symbol mismatch in {lib}")
        continue
    
    error_list = []
    write_fail = []
    for symbol in lib_new.list_symbols():
        new = lib_new.read(symbol, date_range=(pd.Timestamp(2023, 4, 1), None))
        old = lib_old.read(symbol, date_range=DateRange(pd.Timestamp(2023, 4, 1), None))
        
        new_data = new.data
        old_data = old.data
        nmeta = new.metadata
        ometa = old.metadata

        if len(new_data) != len(old_data):
            error_list.append(f"Length mismatch for {symbol} - Old: {len(old)}, New: {len(new)}")
            try:
                new.write(symbol, old, metadata=nmeta, prune_previuos_version=True)
            except Exception as e:
                write_fail.append(f"Writing failed for {symbol} due to {e}")
            
    if len(error_list) > 0:
        print(f"{lib} errors - ")
        for error in error_list:
            print(error)
        print("-------------------------")
