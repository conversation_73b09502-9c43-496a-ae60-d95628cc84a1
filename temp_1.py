from main.tanki import Tanki
from main.data.operation import Operator
from main.config.config_factory import ConfigFactory
from arcticdb import Arctic
import multiprocessing


opt = Operator(config=ConfigFactory(exchange_type="nse"))


storek = Arctic(
    "s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret"
)


def resample_and_write(sym):
    try:
        print(f"started for {sym}")
        storek = Arctic(
            "s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret"
        )
        libk1 = storek["nse/1_min/optstk/trd"]

        df1 = libk1.read(sym).data
        df5 = opt.resample_data_1_min_to_5_min(universe="optstk", data=df1)

        tanki = Tanki(exchange_type="nse")
        tanki.login(username="mantraraj", password="mantraraj")

        # tanki["nse/5_min/optstk/trd"].write_metadata(sym, df5)
        tanki["nse/5_min/optstk/trd"].write(sym, df5)

        print(f"done for {sym}")

    except Exception as e:
        with open("failed_syms", "a") as f:
            f.write(f"Failed for {sym} : {e}")
        return


libk1 = storek["nse/1_min/optstk/trd"]
syms = libk1.list_symbols()


resample_and_write(sym="2930")

# with multiprocessing.Pool(processes=8) as pool:
#     pool.map(resample_and_write, syms)


print()


# import boto3

# # Setup your MinIO credentials and endpoint
# s3 = boto3.client(
#     "s3",
#     endpoint_url="http://*************:9000",  # your MinIO URL
#     aws_access_key_id="super",
#     aws_secret_access_key="doopersecret",
# )

# bucket = "kivi-arcticdb"  # your bucket name
# prefix = "nse/5_min/optstk/trd"  # ArcticDB usually stores everything under this

# symbol_name = "*1013*"

# # Restore all non-latest versions matching symbol
# for page in s3.get_paginator('list_object_versions').paginate(Bucket=bucket, Prefix=prefix):
#     for obj in page.get("Versions", []):
#         key = obj["Key"]
#         version_id = obj["VersionId"]

#         if symbol_name in key:
#             print(f"Restoring: {key} (version {version_id})")
#             s3.copy_object(
#                 Bucket=bucket,
#                 CopySource={'Bucket': bucket, 'Key': key, 'VersionId': version_id},
#                 Key=key
#             )
