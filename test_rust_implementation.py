#!/usr/bin/env python3
"""
Test script to validate that the Rust implementation produces identical results
to the original Python implementation.
"""

import pandas as pd
import numpy as np
import sys
import os
from typing import Dict, Tuple
import warnings

# Add paths
sys.path.append('/home/<USER>/repos/data_auditing/rust_data_processor')

# Import implementations
try:
    from python_wrapper import (
        process_chunk_data as rust_process_chunk,
        resample_futures_trade as rust_resample_fut_trd,
        resample_futures_order as rust_resample_fut_ord,
        resample_options_trade as rust_resample_opt_trd,
        RUST_AVAILABLE
    )
except ImportError:
    RUST_AVAILABLE = False
    print("Rust implementation not available")

def create_test_data() -> pd.DataFrame:
    """Create deterministic test data for validation."""
    np.random.seed(12345)  # Fixed seed for reproducible results
    
    # Create timestamps
    timestamps = pd.date_range('2024-01-01 09:15:00', periods=1000, freq='10S')
    
    # Create symbols and expiries
    symbols = np.random.choice(['NIFTY', 'BANKNIFTY'], 1000)
    expiries = np.random.choice(['2024-01-25', '2024-02-29'], 1000)
    
    # Create futures (30%) and options (70%)
    is_future = np.random.choice([True, False], 1000, p=[0.3, 0.7])
    strike_prices = np.where(is_future, np.nan, 
                            np.random.choice([18000, 18500, 19000], 1000))
    option_types = np.where(is_future, np.nan,
                           np.random.choice(['CE', 'PE'], 1000))
    
    # Create prices (some zero values)
    close_prices = np.where(np.random.random(1000) > 0.2,  # 80% have close prices
                           np.random.uniform(100, 1000, 1000), 0)
    ord_prices = np.where(np.random.random(1000) > 0.2,   # 80% have order prices
                         np.random.uniform(100, 1000, 1000), 0)
    volumes = np.random.uniform(1, 100, 1000)
    
    df = pd.DataFrame({
        'timestamp': timestamps,
        'symbol': symbols,
        'expiry': expiries,
        'strike_price': strike_prices,
        'option_type': option_types,
        'close': close_prices,
        'ord_price': ord_prices,
        'volume': volumes
    })
    
    df.set_index('timestamp', inplace=True)
    return df

def python_process_chunk(df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
    """Original Python implementation for comparison."""
    df_fut = df[df.strike_price.isna()].drop(columns=["strike_price", "option_type"])
    df_opt = df[~df.strike_price.isna()]
    
    return {
        'fut_trd': df_fut[df_fut.close != 0].drop(columns=["ord_price"]),
        'fut_ord': df_fut[df_fut.ord_price != 0].drop(columns=["close", "volume"]),
        'opt_trd': df_opt[df_opt.close != 0].drop(columns=["ord_price"]),
        'opt_ord': df_opt[df_opt.ord_price != 0].drop(columns=["close", "volume"])
    }

def python_resample_futures_trade(df: pd.DataFrame) -> pd.DataFrame:
    """Original Python futures trade resampling."""
    if df.empty:
        return df
    
    result = (
        df.groupby(["symbol", "expiry"])
        .resample("1min", label="right", closed="right")
        .agg({"close": ["first", "max", "min", "last"], "volume": "sum"})
        .dropna()
    )
    
    # Flatten column names
    result.columns = ["_".join(col).strip() for col in result.columns.values]
    result = result.rename(columns={
        "close_first": "open",
        "close_max": "high",
        "close_min": "low",
        "close_last": "close",
        "volume_sum": "volume",
    })
    
    return result.reset_index().set_index("timestamp").sort_index()

def python_resample_futures_order(df: pd.DataFrame) -> pd.DataFrame:
    """Original Python futures order resampling."""
    if df.empty:
        return df
    
    result = (
        df.groupby(["symbol", "expiry"])
        .resample("1min", label="right", closed="right")
        .agg({"ord_price": ["first", "max", "min", "last"]})
        .dropna()
    )
    
    # Flatten column names
    result.columns = ["_".join(col).strip() for col in result.columns.values]
    result = result.rename(columns={
        "ord_price_first": "open",
        "ord_price_max": "high",
        "ord_price_min": "low",
        "ord_price_last": "close",
    })
    
    return result.reset_index().set_index("timestamp").sort_index()

def python_resample_options_trade(df: pd.DataFrame) -> pd.DataFrame:
    """Original Python options trade resampling."""
    if df.empty:
        return df
    
    result = (
        df.groupby(["symbol", "expiry", "strike_price", "option_type"])
        .resample("1min", label="right", closed="right")
        .agg({"close": ["first", "max", "min", "last"]})
        .dropna()
    )
    
    # Flatten column names
    result.columns = ["_".join(col).strip() for col in result.columns.values]
    result = result.rename(columns={
        "close_first": "open",
        "close_max": "high",
        "close_min": "low",
        "close_last": "close",
    })
    
    return result.reset_index().set_index("timestamp").sort_index()

def compare_dataframes(df1: pd.DataFrame, df2: pd.DataFrame, name: str) -> bool:
    """Compare two DataFrames and report differences."""
    if df1.empty and df2.empty:
        print(f"✓ {name}: Both DataFrames are empty")
        return True
    
    if df1.empty or df2.empty:
        print(f"✗ {name}: One DataFrame is empty, the other is not")
        print(f"  df1 shape: {df1.shape}, df2 shape: {df2.shape}")
        return False
    
    # Check shapes
    if df1.shape != df2.shape:
        print(f"✗ {name}: Shape mismatch - df1: {df1.shape}, df2: {df2.shape}")
        return False
    
    # Check columns
    if not df1.columns.equals(df2.columns):
        print(f"✗ {name}: Column mismatch")
        print(f"  df1 columns: {list(df1.columns)}")
        print(f"  df2 columns: {list(df2.columns)}")
        return False
    
    # Check index
    if not df1.index.equals(df2.index):
        print(f"✗ {name}: Index mismatch")
        return False
    
    # Check values (with tolerance for floating point)
    try:
        pd.testing.assert_frame_equal(df1, df2, check_dtype=False, atol=1e-10)
        print(f"✓ {name}: DataFrames are identical")
        return True
    except AssertionError as e:
        print(f"✗ {name}: Value differences found")
        print(f"  Error: {str(e)[:200]}...")
        return False

def test_chunk_processing():
    """Test chunk data processing."""
    print("Testing chunk data processing...")
    print("-" * 40)
    
    df = create_test_data()
    
    # Python implementation
    python_result = python_process_chunk(df)
    
    if not RUST_AVAILABLE:
        print("Rust implementation not available - skipping comparison")
        return False
    
    # Rust implementation
    rust_result = rust_process_chunk(df)
    
    # Compare results
    all_match = True
    for key in ['fut_trd', 'fut_ord', 'opt_trd', 'opt_ord']:
        match = compare_dataframes(python_result[key], rust_result[key], f"chunk_{key}")
        all_match = all_match and match
    
    return all_match

def test_futures_trade_resampling():
    """Test futures trade resampling."""
    print("\nTesting futures trade resampling...")
    print("-" * 40)
    
    df = create_test_data()
    processed = python_process_chunk(df)
    df_fut_trd = processed['fut_trd']
    
    if df_fut_trd.empty:
        print("No futures trade data to test")
        return True
    
    # Python implementation
    python_result = python_resample_futures_trade(df_fut_trd)
    
    if not RUST_AVAILABLE:
        print("Rust implementation not available - skipping comparison")
        return False
    
    # Rust implementation
    rust_result = rust_resample_fut_trd(df_fut_trd, interval_minutes=1)
    
    return compare_dataframes(python_result, rust_result, "futures_trade_resample")

def test_futures_order_resampling():
    """Test futures order resampling."""
    print("\nTesting futures order resampling...")
    print("-" * 40)
    
    df = create_test_data()
    processed = python_process_chunk(df)
    df_fut_ord = processed['fut_ord']
    
    if df_fut_ord.empty:
        print("No futures order data to test")
        return True
    
    # Python implementation
    python_result = python_resample_futures_order(df_fut_ord)
    
    if not RUST_AVAILABLE:
        print("Rust implementation not available - skipping comparison")
        return False
    
    # Rust implementation
    rust_result = rust_resample_fut_ord(df_fut_ord, interval_minutes=1)
    
    return compare_dataframes(python_result, rust_result, "futures_order_resample")

def test_options_trade_resampling():
    """Test options trade resampling."""
    print("\nTesting options trade resampling...")
    print("-" * 40)
    
    df = create_test_data()
    processed = python_process_chunk(df)
    df_opt_trd = processed['opt_trd']
    
    if df_opt_trd.empty:
        print("No options trade data to test")
        return True
    
    # Python implementation
    python_result = python_resample_options_trade(df_opt_trd)
    
    if not RUST_AVAILABLE:
        print("Rust implementation not available - skipping comparison")
        return False
    
    # Rust implementation
    rust_result = rust_resample_opt_trd(df_opt_trd, interval_minutes=1)
    
    return compare_dataframes(python_result, rust_result, "options_trade_resample")

def run_all_tests():
    """Run all validation tests."""
    print("=" * 60)
    print("RUST IMPLEMENTATION VALIDATION TESTS")
    print("=" * 60)
    print(f"Rust available: {RUST_AVAILABLE}")
    print()
    
    if not RUST_AVAILABLE:
        print("Rust implementation not available. Please build the extension first:")
        print("  cd rust_data_processor")
        print("  python build.py")
        return False
    
    # Run tests
    test_results = []
    test_results.append(test_chunk_processing())
    test_results.append(test_futures_trade_resampling())
    test_results.append(test_futures_order_resampling())
    test_results.append(test_options_trade_resampling())
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! Rust implementation is working correctly.")
        print("\nYou can now use the optimized version:")
        print("  from sample_snap_tick_data_optimized import process_snap_file_optimized")
        return True
    else:
        print("✗ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\nNext steps:")
        print("1. Run benchmark_performance.py to see performance improvements")
        print("2. Use sample_snap_tick_data_optimized.py for production workloads")
        print("3. Replace process_snap_file with process_snap_file_optimized in your scripts")
    else:
        print("\nTroubleshooting:")
        print("1. Ensure Rust is installed: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh")
        print("2. Build the extension: cd rust_data_processor && python build.py")
        print("3. Check for any compilation errors")
