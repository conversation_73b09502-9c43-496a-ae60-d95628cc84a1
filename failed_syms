Failed for 2930 : DataChecksFailed: Data inconsistencies found for the symbol: 2930.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Passed

check_all_timestamps:
Passed

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.5160813455072215% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.44084698056115595% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.05015624329737703% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.04553658930946072% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.04883634215797237% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.026068047503242008% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
0.5445744251714401% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
0.24203307785397338% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
0.5244050020169423% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
0.19160951996772893% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
0.5546591367486889% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
0.24203307785397338% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
0.4235578862444534% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
0.2117789431222267% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

Failed for 268 : DataChecksFailed: Data inconsistencies found for the symbol: 268.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Found 1368 missing dates

check_all_timestamps:
Found 23294 missing times

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.5907498634379558% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.5266032885142551% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.058233062547922% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.04841061826273034% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.045303518539863585% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.02866550066902873% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
0.7444104373087098% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
0.4694713907765731% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
0.7262540851792292% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
0.4694713907765731% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
0.6977226746900451% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
0.513565388805312% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
0.7055039684598226% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
0.5005965658556829% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

Failed for 1306 : DataChecksFailed: Data inconsistencies found for the symbol: 1306.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Found 1047 missing dates

check_all_timestamps:
Found 13534 missing times

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.5729772716228905% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.49616926603419365% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.05658712248473376% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.04958557639705664% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.041434522742745956% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.026752176096199152% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
0.5503682377081776% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
0.35630776082841553% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
0.5026484483115148% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
0.3435824836559721% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
0.45651931856140743% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
0.3817583151733024% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
0.46447261679418456% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
0.36585171870774813% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

Failed for 2323 : DataChecksFailed: Data inconsistencies found for the symbol: 2323.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Passed

check_all_timestamps:
Found 39 missing times

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.41305805641559773% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.35027893903291624% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.06991310799434988% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.04851113615934481% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.0684863098720162% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.05207813146517899% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
1.0814582243153672% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
0.9070294784580499% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
1.098901098901099% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
0.8895866038723181% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
0.9593581022152451% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
0.9942438513867086% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
1.0116867259724402% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
0.9419152276295133% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

Failed for 86 : DataChecksFailed: Data inconsistencies found for the symbol: 86.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Found 1340 missing dates

check_all_timestamps:
Found 24792 missing times

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.6825355405342608% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.5696494382504441% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.04310976547000726% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.037604870045147294% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.045826467108249835% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.02945476513041955% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
0.5585768273573601% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
0.38528896672504376% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
0.5124896303806803% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
0.3631671121762374% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
0.5327679970504194% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
0.40741082127385014% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
0.4829938243156051% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
0.39266291824131255% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

Failed for 871 : DataChecksFailed: Data inconsistencies found for the symbol: 871.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Found 1615 missing dates

check_all_timestamps:
Found 11366 missing times

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.5055933693487709% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.4426854386003063% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.06073333561148062% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.06026735093926977% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.06663580812615137% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.05296692440796647% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
1.4247584677267773% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
1.3119807526032856% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
1.3796473816773807% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
1.3007029810909365% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
1.3570918386526822% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
1.3570918386526822% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
1.3533325814818993% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
1.3608510958234652% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

Failed for 1849 : DataChecksFailed: Data inconsistencies found for the symbol: 1849.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Passed

check_all_timestamps:
Found 1 missing times

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.3262986541088922% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.29504956251271763% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.045783552803697565% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.044330106682945264% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.045783552803697565% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.034155983837679135% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
0.8859118409680208% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
0.7130509939498704% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
1.0155574762316335% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
0.7346585998271391% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
0.9507346585998271% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
0.756266205704408% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
0.8643042350907519% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
0.756266205704408% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

Failed for 55 : DataChecksFailed: Data inconsistencies found for the symbol: 55.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Found 1396 missing dates

check_all_timestamps:
Found 21109 missing times

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.43806435138632616% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.36578697939734234% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.04017813991802794% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.03440749145583362% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.04190933445668624% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.02878110920519416% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
0.7715297060132686% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
0.5383751245257424% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
0.7778884673265648% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
0.523538014794718% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
0.710061679984739% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
0.5574514084656309% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
0.707942092880307% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
0.5574514084656309% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

Failed for 131 : DataChecksFailed: Data inconsistencies found for the symbol: 131.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Found 1384 missing dates

check_all_timestamps:
Found 20965 missing times

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.55084354714794% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.45250172083500617% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.03967766453151847% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.03285898440462048% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.035210253413895644% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.02615786772818625% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
0.5581786903846858% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
0.40628088283737784% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
0.567329160718861% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
0.37516928370118224% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
0.5252369971816552% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
0.44471285824091356% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
0.5490282200505106% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
0.41726144723838804% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

Failed for 200 : WriteError: Found error during writing 200 at nse/5_min/optstk/trd from store StorageType.DB: Exception("OperationAuditError: Found error during writing operation history for 200 in nse/5_min/optstk/trd: E_ASSERTION_FAILURE Can't append dataframe with start index 2025-06-03 12:06:14.0 to existing sequence ending at 2025-06-03 12:06:15.0")Failed for 2765 : DataChecksFailed: Data inconsistencies found for the symbol: 2765.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Passed

check_all_timestamps:
Found 25 missing times

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.7519186756278301% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.6384785720888962% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.041038879802736905% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.033522284975709304% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.03715098592668815% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.02738805241572126% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
0.4486151445537688% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
0.2600667504659529% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
0.3987690173811278% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
0.2492306358632049% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
0.37276234233453254% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
0.281738979671449% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
0.35109011312903643% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
0.2774045338303498% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

Failed for 730 : DataChecksFailed: Data inconsistencies found for the symbol: 730.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Found 1360 missing dates

check_all_timestamps:
Found 23486 missing times

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.6223329686637288% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.5414625235273471% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.040332595099743636% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.03427757446135973% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.03612486889340906% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.02196227824769755% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
0.6247020748137183% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
0.5895782633784089% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
0.6171755437918663% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
0.5845605760305075% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
0.6171755437918663% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
0.5895782633784089% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
0.6322286058355704% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
0.612157856443965% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

Failed for 2534 : DataChecksFailed: Data inconsistencies found for the symbol: 2534.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Passed

check_all_timestamps:
Passed

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.4725420166669992% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.3890740038707875% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.06517820919544817% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.06218533224259595% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.08247038714526095% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.03524943966692605% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
0.49517207229512256% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
0.2847239415696955% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
0.45803416687298837% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
0.2847239415696955% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
0.4208962614508542% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
0.3218618469918297% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
0.4085169596434761% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
0.27234463976231743% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

Failed for 636 : DataChecksFailed: Data inconsistencies found for the symbol: 636.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Passed

check_all_timestamps:
Passed

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.4302145994765005% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.36817184738366154% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.05519817801853996% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.03488524850771726% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.04669765860368481% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.023514423835898024% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
0.8449695667740781% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
0.66953097028285% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
0.7841031149301826% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
0.6122448979591837% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
0.719656283566058% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
0.7303974221267454% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
0.6874328678839957% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
0.7053347654851414% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

Failed for 447 : DataChecksFailed: Data inconsistencies found for the symbol: 447.
check_columns_set:
Passed

check_columns_dtype:
Passed

check_nan_entries:
Passed

check_duplicate_entries:
Passed

check_monotonic_nature:
Passed

check_all_dates:
Found 1287 missing dates

check_all_timestamps:
Found 19797 missing times

check_forward_fill:
Column: OI is not forward filled

check_OHLC:
Passed
0.7130667465362861% data for which open-close percentage change is 10 times greater than average open-close percentage for the specific ID and Date combination
0.6154787980719386% data for which low-high percentage change is 10 times greater than average low-high percentage for the specific ID and Date combination

check_intraday_sudden_jumps:
Passed
0.04262272959909949% data for which open percentage change is 10 times greater than average open percentage change for the specific ID and Date combination
0.04410382831103731% data for which high percentage change is 10 times greater than average high percentage change for the specific ID and Date combination
0.03817943346328603% data for which low percentage change is 10 times greater than average low percentage change for the specific ID and Date combination
0.03225503861553475% data for which close percentage change is 10 times greater than average close percentage change for the specific ID and Date combination

check_overnight_sudden_jumps:
Passed
0.6784604654111978% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day first
0.538963921121045% data for which open percentage change is 10 times greater than average open percentage change current day last to previous day last
0.6943123454441696% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day first
0.5294527931012618% data for which high percentage change is 10 times greater than average high percentage change current day last to previous day last
0.6213936972924989% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day first
0.5738380571935832% data for which low percentage change is 10 times greater than average low percentage change current day last to previous day last
0.6752900894046033% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day first
0.5674973051803944% data for which close percentage change is 10 times greater than average close percentage change current day last to previous day last

