from arctic.date import DateRange
import logging
import sys
import pandas as pd
import numpy as np
import warnings

warnings.filterwarnings("ignore")
from arcticdb import Arctic as Arcticnew
from arctic import Arctic as Arcticold
from pymongo import MongoClient
import logging

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

MONGO_CLIENT_IP = "*************"
MONGO_CLIENT_PORT = "27016"
MONGO_CLIENT_USERNAME = "kiviArcticReader"
MONGO_CLIENT_PASSWORD = "kivi"


INDEX_COLUMNS = ["timestamp", "ID", "date", "exdate", "balte_id"]


def compare_dataframes(
    df1: pd.DataFrame,
    df2: pd.DataFrame,
    rtol=1e-5,
    atol=1e-5,
    ignore_dtype=True,
    ignore_column_order=False,
    ignore_values=False,
    ignore_nan_mismatches=False,
    nan_equal_columns=None,
    show_mismatches=True,
    logger=None,
):
    """
    Compare two DataFrames with tolerance and per-column NaN handling.

    Parameters:
    - df1, df2: DataFrames to compare
    - rtol, atol: Tolerance for numeric comparison
    - ignore_dtype: If True, skip dtype comparison
    - ignore_column_order: If True, sort columns before comparing
    - ignore_values: If True, skip value comparison
    - ignore_nan_mismatches: If True, ignore NaN/inf mismatches
    - nan_equal_columns: dict with column names as keys and bools as values.
                         True = treat NaN/inf and 0 as equal in that column
    - show_mismatches: Print mismatches if True
    - logger: Logger object for logging

    Returns:
    - True if DataFrames match under given rules
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    if nan_equal_columns is None:
        nan_equal_columns = {}

    if ignore_column_order:
        df1 = df1[sorted(df1.columns)]
        df2 = df2[sorted(df2.columns)]

    if df1.shape != df2.shape:
        logger.info(f"Shape mismatch: {df1.shape} vs {df2.shape}")
        return False

    if not df1.columns.equals(df2.columns):
        logger.info("Column mismatch:")
        logger.info(f"df1 columns: {df1.columns}")
        logger.info(f"df2 columns: {df2.columns}")
        return False

    if df1.empty or ignore_values:
        return True

    def get_sort_keys(df):
        keys = []
        all_cols = list(df.columns) + list(df.index.names)
        for col in INDEX_COLUMNS:
            if col in all_cols:
                keys.append(col)
        return keys

    sort_keys = get_sort_keys(df1)

    # Reset index to allow sorting by index fields
    if sort_keys:
        df1 = df1.reset_index().sort_values(by=sort_keys).reset_index(drop=True)
        df2 = df2.reset_index().sort_values(by=sort_keys).reset_index(drop=True)
    else:
        df1 = df1.reset_index(drop=True)
        df2 = df2.reset_index(drop=True)

    for col in df1.columns:
        s1 = df1[col]
        s2 = df2[col]

        if nan_equal_columns.get(col, False):
            # Replace NaN with 0 for this column
            s1 = s1.replace([np.nan, np.inf, -np.inf], 0)
            s2 = s2.replace([np.nan, np.inf, -np.inf], 0)

        if ignore_nan_mismatches:
            # Ignore all rows where either side is NaN
            nan_mask = s1.isna() | s2.isna()
        else:
            # Only ignore rows where both are NaN
            nan_mask = s1.isna() & s2.isna()

        s1_clean = s1[~nan_mask]
        s2_clean = s2[~nan_mask]
        df1_clean = df1[~nan_mask]
        try:
            if np.issubdtype(s1_clean.dtype, np.number):
                if not np.allclose(
                    s1_clean, s2_clean, rtol=rtol, atol=atol, equal_nan=True
                ):
                    if show_mismatches:
                        mismatches = ~np.isclose(
                            s1_clean, s2_clean, rtol=rtol, atol=atol, equal_nan=True
                        )
                        logger.info(f"Mismatch in numeric column '{col}':")
                        dt = {}
                        for col in INDEX_COLUMNS:
                            if col in df1.columns:
                                dt[col] = df1_clean[col][mismatches]
                        dt["df1"] = s1_clean[mismatches]
                        dt["df2"] = s2_clean[mismatches]
                        logger.info(pd.DataFrame(dt))
                    return False
            else:
                if not s1_clean.reset_index(drop=True).equals(
                    s2_clean.reset_index(drop=True)
                ):
                    if show_mismatches:
                        logger.info(f"Mismatch in non-numeric column '{col}':")
                        dt = {}
                        for col in INDEX_COLUMNS:
                            if col in df1.columns:
                                dt[col] = df1_clean[col]
                        dt["df1"] = s1_clean
                        dt["df2"] = s2_clean
                        logger.info(pd.DataFrame(dt))
                    return False
        except Exception as e:
            logger.info(f"Error comparing column '{col}': {e}")
            return False

    if not ignore_dtype and not df1.dtypes.equals(df2.dtypes):
        logger.info("Dtype mismatch:")
        logger.info(pd.DataFrame({"df1": df1.dtypes, "df2": df2.dtypes}))
        return False

    return True


if __name__ == "__main__":
    # take positional arguments exchange
    args = sys.argv[1:]
    exchange = args[0].lower()
    mongo_connection_path = (
        "mongodb://" + MONGO_CLIENT_IP + ":" + MONGO_CLIENT_PORT + "/"
    )
    mongo_client = MongoClient(
        mongo_connection_path,
        username=MONGO_CLIENT_USERNAME,
        password=MONGO_CLIENT_PASSWORD,
    )
    today = pd.Timestamp.now().date()
    date_2weeks = str(today - pd.Timedelta(days=14))
    store_old = Arcticold(mongo_client)
    store_new = Arcticnew(
        "s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret"
    )
    old_v2_libs = store_new.list_libraries()
    old_v2_libs = [lib for lib in old_v2_libs if f"{exchange}_old_v2" in lib]
    mismatches = []
    logger.info(f"Checking {exchange} exchange")
    for lib in old_v2_libs:
        new_lib = lib.replace("_old_v2", "")
        syms_old_v2 = store_new[lib].list_symbols()
        syms_old = store_old[new_lib].list_symbols()
        if set(syms_old_v2) != set(syms_old):
            mismatches.append(
                f"{new_lib} has extra symbols, extra_old: {set(syms_old_v2) - set(syms_old)}, extra_old_v2: {set(syms_old) - set(syms_old_v2)}"
            )
        for sym in set(syms_old_v2).intersection(set(syms_old)):
            if "ncdex" in sym.lower() or sym in ["fno_slippage"]:
                continue
            try:
                ignore_values = False
                if sym == "corporate_action":
                    df = store_old[new_lib].read(sym).data
                    df2 = store_new[lib].read(sym).data
                else:
                    df = (
                        store_old[new_lib]
                        .read(sym, date_range=DateRange(date_2weeks, None))
                        .data
                    )
                    df2 = (
                        store_new[lib]
                        .read(sym, date_range=DateRange(date_2weeks, None))
                        .data
                    )
                if sym in ["delta", "theta", "gamma", "vega", "iv"]:
                    df = df.replace([np.inf, -np.inf], np.nan).fillna(0)
                    df2 = df2.replace([np.inf, -np.inf], np.nan).fillna(0)
                if new_lib in [
                    "nse/1_min/fno/trd",
                    "nse/1_min/fut/trd",
                    "nse/5_min/fut/trd",
                    "nse/1_min/fut_raw/trd",
                    "nse/5_min/fut_raw/trd",
                    "nse/1_min/futidx_fut/trd",
                    "nse/5_min/futidx_fut/trd",
                    "nse/1_min/futstk_oi/trd",
                ]:
                    drop_list = []
                    for col in ["Next_Cons_Volume", "Cons_Volume"]:
                        if col in df.columns:
                            drop_list.append(col)
                    df = df.drop(columns=drop_list)
                    df2 = df2.drop(columns=drop_list)
                elif new_lib in [
                    "nse/1_min/fno/column_bucket",
                    "nse/5_min/fno/column_bucket",
                    "nse/1_min/fut/column_bucket",
                    "nse/5_min/fut/column_bucket",
                    "nse/1_min/fut_raw/column_bucket",
                    "nse/5_min/fut_raw/column_bucket",
                    "nse/1_min/futidx_fut/column_bucket",
                    "nse/5_min/futidx_fut/column_bucket",
                    "nse/1_min/futidx/column_bucket",
                    "nse/5_min/futidx/column_bucket",
                ]:
                    if sym in ["Cons_Volume", "Next_Cons_Volume", "fut_close"]:
                        ignore_values = True
                ignore_nan_mismatches = any(
                    univ in new_lib
                    for univ in ["/fut_raw/", "futidx_raw", "/fut/", "/futidx_fut/"]
                )
                if not compare_dataframes(
                    df,
                    df2,
                    ignore_nan_mismatches=ignore_nan_mismatches,
                    ignore_values=ignore_values,
                    nan_equal_columns={
                        "Cons_Volume": True,
                        "delta": True,
                        "theta": True,
                        "gamma": True,
                        "vega": True,
                        "iv": True,
                    },
                    logger=logger,
                    show_mismatches=True,
                ):
                    logger.info(f"{new_lib}-{sym}")
                    mismatches.append(f"{new_lib}-{sym}")
            except Exception as e:
                logger.error(f"{new_lib}-{sym} {repr(e)}")
    if len(mismatches) > 0:
        mismatch = "\n".join(mismatches)
        raise Exception(f"Data mismatch found for (library-symbol):\n{mismatch}")
