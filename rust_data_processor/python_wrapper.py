"""
Python wrapper for the Rust data processing module.

This module provides a clean Python interface to the high-performance Rust functions
for financial data processing, with automatic data type conversions and pandas integration.
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional, Union
import warnings

try:
    import rust_data_processor
    RUST_AVAILABLE = True
except ImportError:
    RUST_AVAILABLE = False
    warnings.warn("Rust data processor not available. Falling back to pure Python implementation.")


class RustDataProcessor:
    """
    High-performance data processor using Rust backend.
    
    This class provides optimized implementations of common financial data processing
    operations like resampling, aggregation, and filtering.
    """
    
    def __init__(self):
        if not RUST_AVAILABLE:
            raise ImportError("Rust data processor module not available. Please build the extension first.")
    
    def process_chunk_data(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        Process a chunk of financial data, separating futures and options,
        and trade vs order data.
        
        Args:
            df: DataFrame with columns including 'strike_price', 'close', 'ord_price'
            
        Returns:
            Dictionary with keys: 'fut_trd', 'fut_ord', 'opt_trd', 'opt_ord'
        """
        # Prepare data for Rust function
        strike_prices = df['strike_price'].fillna(np.nan).values.astype(np.float64)
        close_prices = df['close'].fillna(0.0).values.astype(np.float64)
        ord_prices = df['ord_price'].fillna(0.0).values.astype(np.float64)
        
        # Call Rust function
        indices = rust_data_processor.partition_data(strike_prices, close_prices, ord_prices)
        
        # Create result DataFrames
        result = {}
        for key, idx_array in indices.items():
            if len(idx_array) > 0:
                result[key.replace('_indices', '')] = df.iloc[idx_array].copy()
            else:
                result[key.replace('_indices', '')] = pd.DataFrame()
        
        return result
    
    def resample_futures_trade(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """
        Resample futures trade data to OHLC format.
        
        Args:
            df: DataFrame with timestamp index and columns: symbol, expiry, close, volume
            interval_minutes: Resampling interval in minutes
            
        Returns:
            Resampled DataFrame with OHLC data
        """
        if df.empty:
            return df
        
        # Prepare data
        timestamps = df.index.astype(np.int64).values
        symbols = pd.Categorical(df['symbol']).codes.astype(np.int32)
        expiries = pd.Categorical(df['expiry']).codes.astype(np.int32)
        close_prices = df['close'].values.astype(np.float64)
        volumes = df['volume'].values.astype(np.float64)
        
        # Call Rust function
        result = rust_data_processor.aggregate_futures_trade(
            timestamps, symbols, expiries, close_prices, volumes, interval_minutes
        )
        
        # Convert back to DataFrame
        result_df = pd.DataFrame(result)
        result_df['timestamp'] = pd.to_datetime(result_df['timestamp'])
        result_df.set_index('timestamp', inplace=True)
        
        # Map back categorical codes to original values
        symbol_categories = df['symbol'].astype('category').cat.categories
        expiry_categories = df['expiry'].astype('category').cat.categories
        
        result_df['symbol'] = symbol_categories[result_df['symbol']]
        result_df['expiry'] = expiry_categories[result_df['expiry']]
        
        return result_df.sort_index()
    
    def resample_futures_order(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """
        Resample futures order data to OHLC format.
        
        Args:
            df: DataFrame with timestamp index and columns: symbol, expiry, ord_price
            interval_minutes: Resampling interval in minutes
            
        Returns:
            Resampled DataFrame with OHLC data
        """
        if df.empty:
            return df
        
        # Prepare data
        timestamps = df.index.astype(np.int64).values
        symbols = pd.Categorical(df['symbol']).codes.astype(np.int32)
        expiries = pd.Categorical(df['expiry']).codes.astype(np.int32)
        ord_prices = df['ord_price'].values.astype(np.float64)
        
        # Call Rust function
        result = rust_data_processor.aggregate_futures_order(
            timestamps, symbols, expiries, ord_prices, interval_minutes
        )
        
        # Convert back to DataFrame
        result_df = pd.DataFrame(result)
        result_df['timestamp'] = pd.to_datetime(result_df['timestamp'])
        result_df.set_index('timestamp', inplace=True)
        
        # Map back categorical codes to original values
        symbol_categories = df['symbol'].astype('category').cat.categories
        expiry_categories = df['expiry'].astype('category').cat.categories
        
        result_df['symbol'] = symbol_categories[result_df['symbol']]
        result_df['expiry'] = expiry_categories[result_df['expiry']]
        
        return result_df.sort_index()
    
    def resample_options_trade(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """
        Resample options trade data to OHLC format.
        
        Args:
            df: DataFrame with timestamp index and columns: symbol, expiry, strike_price, option_type, close
            interval_minutes: Resampling interval in minutes
            
        Returns:
            Resampled DataFrame with OHLC data
        """
        if df.empty:
            return df
        
        # Prepare data
        timestamps = df.index.astype(np.int64).values
        symbols = pd.Categorical(df['symbol']).codes.astype(np.int32)
        expiries = pd.Categorical(df['expiry']).codes.astype(np.int32)
        strikes = df['strike_price'].values.astype(np.float64)
        option_types = pd.Categorical(df['option_type']).codes.astype(np.int32)
        close_prices = df['close'].values.astype(np.float64)
        
        # Call Rust function
        result = rust_data_processor.aggregate_options_trade(
            timestamps, symbols, expiries, strikes, option_types, close_prices, interval_minutes
        )
        
        # Convert back to DataFrame
        result_df = pd.DataFrame(result)
        result_df['timestamp'] = pd.to_datetime(result_df['timestamp'])
        result_df.set_index('timestamp', inplace=True)
        
        # Map back categorical codes to original values
        symbol_categories = df['symbol'].astype('category').cat.categories
        expiry_categories = df['expiry'].astype('category').cat.categories
        option_type_categories = df['option_type'].astype('category').cat.categories
        
        result_df['symbol'] = symbol_categories[result_df['symbol']]
        result_df['expiry'] = expiry_categories[result_df['expiry']]
        result_df['option_type'] = option_type_categories[result_df['option_type']]
        
        return result_df.sort_index()


# Fallback pure Python implementations
class PythonDataProcessor:
    """
    Pure Python fallback implementation for when Rust is not available.
    """
    
    def process_chunk_data(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Pure Python implementation of chunk data processing."""
        df_fut = df[df.strike_price.isna()].drop(columns=["strike_price", "option_type"])
        df_opt = df[~df.strike_price.isna()]
        
        result = {
            'fut_trd': df_fut[df_fut.close != 0].drop(columns=["ord_price"]),
            'fut_ord': df_fut[df_fut.ord_price != 0].drop(columns=["close", "volume"]),
            'opt_trd': df_opt[df_opt.close != 0].drop(columns=["ord_price"]),
            'opt_ord': df_opt[df_opt.ord_price != 0].drop(columns=["close", "volume"])
        }
        
        return result
    
    def resample_futures_trade(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """Pure Python implementation of futures trade resampling."""
        if df.empty:
            return df
        
        return (
            df.groupby(["symbol", "expiry"])
            .resample(f"{interval_minutes}min", label="right", closed="right")
            .agg({
                "close": ["first", "max", "min", "last"],
                "volume": "sum"
            })
            .dropna()
        )
    
    def resample_futures_order(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """Pure Python implementation of futures order resampling."""
        if df.empty:
            return df
        
        return (
            df.groupby(["symbol", "expiry"])
            .resample(f"{interval_minutes}min", label="right", closed="right")
            .agg({
                "ord_price": ["first", "max", "min", "last"]
            })
            .dropna()
        )
    
    def resample_options_trade(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """Pure Python implementation of options trade resampling."""
        if df.empty:
            return df
        
        return (
            df.groupby(["symbol", "expiry", "strike_price", "option_type"])
            .resample(f"{interval_minutes}min", label="right", closed="right")
            .agg({
                "close": ["first", "max", "min", "last"]
            })
            .dropna()
        )


# Create the appropriate processor instance
if RUST_AVAILABLE:
    data_processor = RustDataProcessor()
else:
    data_processor = PythonDataProcessor()


# Convenience functions
def process_chunk_data(df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
    """Process chunk data using the best available implementation."""
    return data_processor.process_chunk_data(df)


def resample_futures_trade(df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
    """Resample futures trade data using the best available implementation."""
    return data_processor.resample_futures_trade(df, interval_minutes)


def resample_futures_order(df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
    """Resample futures order data using the best available implementation."""
    return data_processor.resample_futures_order(df, interval_minutes)


def resample_options_trade(df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
    """Resample options trade data using the best available implementation."""
    return data_processor.resample_options_trade(df, interval_minutes)
