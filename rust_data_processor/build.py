#!/usr/bin/env python3
"""
Build script for the Rust data processor module.
This script handles the compilation and installation of the Rust extension.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run a command and handle errors."""
    print(f"Running: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, cwd=cwd, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def check_rust_installed():
    """Check if Rust is installed."""
    try:
        subprocess.run(["rustc", "--version"], check=True, capture_output=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def install_rust():
    """Install Rust using rustup."""
    print("Rust not found. Installing Rust...")
    try:
        # Download and run rustup installer
        if os.name == 'nt':  # Windows
            subprocess.run(["powershell", "-Command", 
                          "Invoke-WebRequest -Uri https://win.rustup.rs/ -OutFile rustup-init.exe; ./rustup-init.exe -y"], 
                          check=True)
        else:  # Unix-like systems
            subprocess.run(["curl", "--proto", "=https", "--tlsv1.2", "-sSf", 
                          "https://sh.rustup.rs", "|", "sh", "-s", "--", "-y"], 
                          shell=True, check=True)
        
        # Source the environment
        if os.name != 'nt':
            os.environ['PATH'] = f"{os.path.expanduser('~/.cargo/bin')}:{os.environ.get('PATH', '')}"
        
        return True
    except subprocess.CalledProcessError:
        print("Failed to install Rust automatically.")
        print("Please install Rust manually from https://rustup.rs/")
        return False

def install_maturin():
    """Install maturin for building Python extensions."""
    print("Installing maturin...")
    return run_command([sys.executable, "-m", "pip", "install", "maturin"])

def build_extension():
    """Build the Rust extension."""
    print("Building Rust extension...")
    rust_dir = Path(__file__).parent
    return run_command(["maturin", "develop", "--release"], cwd=rust_dir)

def main():
    """Main build process."""
    print("Building Rust data processor extension...")
    
    # Check if Rust is installed
    if not check_rust_installed():
        if not install_rust():
            sys.exit(1)
    
    # Install maturin
    if not install_maturin():
        print("Failed to install maturin")
        sys.exit(1)
    
    # Build the extension
    if not build_extension():
        print("Failed to build Rust extension")
        sys.exit(1)
    
    print("Successfully built Rust data processor extension!")
    print("You can now import it in Python with: import rust_data_processor")

if __name__ == "__main__":
    main()
