# Rust Data Processor

High-performance financial data processing library with Rust backend and Python interface.

## Overview

This library provides optimized implementations of common financial data processing operations:

- **Data partitioning**: Separate futures vs options, trade vs order data
- **OHLC aggregation**: Fast groupby and resampling operations
- **Time-based resampling**: Efficient 1-minute interval processing
- **Memory optimization**: Reduced memory allocation and copying

The Rust backend provides significant performance improvements over pure Python implementations, especially for large datasets.

## Installation

### Prerequisites

1. **Rust**: Install from [https://rustup.rs/](https://rustup.rs/)
   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   source ~/.cargo/env
   ```

2. **Python dependencies**:
   ```bash
   pip install pandas numpy maturin
   ```

### Build and Install

1. **Automatic build**:
   ```bash
   cd rust_data_processor
   python build.py
   ```

2. **Manual build**:
   ```bash
   cd rust_data_processor
   maturin develop --release
   ```

3. **Install as package**:
   ```bash
   cd rust_data_processor
   pip install .
   ```

## Usage

### Basic Usage

```python
import pandas as pd
from python_wrapper import process_chunk_data, resample_futures_trade

# Load your data
df = pd.read_parquet("your_data.parquet")

# Process chunk data (separate futures/options, trade/order)
processed = process_chunk_data(df)
fut_trd = processed['fut_trd']
fut_ord = processed['fut_ord']
opt_trd = processed['opt_trd']
opt_ord = processed['opt_ord']

# Resample to 1-minute OHLC
ohlc_data = resample_futures_trade(fut_trd, interval_minutes=1)
```

### Integration with Existing Code

Replace the slow pandas operations in your existing code:

```python
# Before (slow pandas)
df_fut_trd = (
    df_fut_trd.groupby(["symbol", "expiry"])
    .resample("1min", label="right", closed="right")
    .agg({"close": ["first", "max", "min", "last"], "volume": "sum"})
    .dropna()
)

# After (fast Rust)
from python_wrapper import resample_futures_trade
df_fut_trd = resample_futures_trade(df_fut_trd, interval_minutes=1)
```

### Performance Comparison

Typical performance improvements:
- **Data partitioning**: 5-10x faster
- **OHLC aggregation**: 10-50x faster
- **Memory usage**: 30-50% reduction

## API Reference

### `process_chunk_data(df: pd.DataFrame) -> Dict[str, pd.DataFrame]`

Separates financial data into futures/options and trade/order categories.

**Parameters:**
- `df`: DataFrame with columns `strike_price`, `close`, `ord_price`

**Returns:**
- Dictionary with keys: `fut_trd`, `fut_ord`, `opt_trd`, `opt_ord`

### `resample_futures_trade(df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame`

Resamples futures trade data to OHLC format.

**Parameters:**
- `df`: DataFrame with timestamp index and columns `symbol`, `expiry`, `close`, `volume`
- `interval_minutes`: Resampling interval (default: 1)

**Returns:**
- Resampled DataFrame with OHLC columns

### `resample_futures_order(df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame`

Resamples futures order data to OHLC format.

### `resample_options_trade(df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame`

Resamples options trade data to OHLC format.

## Fallback Behavior

If the Rust extension fails to build or load, the library automatically falls back to pure Python implementations. This ensures compatibility while still providing performance benefits when possible.

## Development

### Building for Development

```bash
# Build in debug mode
maturin develop

# Build in release mode (faster)
maturin develop --release

# Build with specific Python version
maturin develop --python python3.11
```

### Testing

```bash
# Run Python tests
python -m pytest tests/

# Benchmark performance
python benchmark.py
```

## Troubleshooting

### Common Issues

1. **Rust not found**: Install Rust from rustup.rs
2. **Maturin not found**: `pip install maturin`
3. **Build fails**: Check Rust version with `rustc --version`
4. **Import error**: Verify the extension built successfully

### Performance Tips

1. **Use categorical data**: Convert string columns to categories before processing
2. **Sort by timestamp**: Pre-sorted data processes faster
3. **Batch processing**: Process data in chunks for memory efficiency
4. **Use appropriate dtypes**: Ensure numeric columns are float64/int64

## License

MIT License - see LICENSE file for details.
