use pyo3::prelude::*;
use pyo3::types::PyDict;
use numpy::{PyArray1, PyArray2, PyReadonlyArray1, PyReadonlyArray2};
use std::collections::HashMap;

/// Data processing module for high-performance financial data resampling
///
/// This module provides Rust implementations of data processing functions
/// that are performance-critical in the Python data pipeline.

/// Fast groupby aggregation for OHLC data
#[pyfunction]
fn fast_ohlc_aggregation(
    timestamps: PyReadonlyArray1<i64>,
    prices: PyReadonlyArray1<f64>,
    volumes: Option<PyReadonlyArray1<f64>>,
    group_keys: PyReadonlyArray2<i32>,
    interval_minutes: i64,
) -> PyResult<PyDict> {
    let py = Python::acquire_gil().python();
    let result = PyDict::new(py);

    let ts_data = timestamps.as_slice()?;
    let price_data = prices.as_slice()?;
    let vol_data = volumes.map(|v| v.as_slice()).transpose()?;
    let group_data = group_keys.as_array();

    // Convert interval to nanoseconds (assuming timestamps are in nanoseconds)
    let interval_ns = interval_minutes * 60 * 1_000_000_000;

    // Group data by keys and time intervals
    let mut groups: HashMap<(Vec<i32>, i64), OhlcData> = HashMap::new();

    for i in 0..ts_data.len() {
        let timestamp = ts_data[i];
        let price = price_data[i];
        let volume = vol_data.map(|v| v[i]).unwrap_or(0.0);

        // Create group key from the row
        let mut group_key = Vec::new();
        for j in 0..group_data.shape()[1] {
            group_key.push(group_data[[i, j]]);
        }

        // Calculate time bucket
        let time_bucket = (timestamp / interval_ns) * interval_ns;
        let full_key = (group_key, time_bucket);

        // Update OHLC data
        let entry = groups.entry(full_key).or_insert(OhlcData::new(price, volume));
        entry.update(price, volume);
    }

    // Convert results back to Python arrays
    let mut result_timestamps = Vec::new();
    let mut result_opens = Vec::new();
    let mut result_highs = Vec::new();
    let mut result_lows = Vec::new();
    let mut result_closes = Vec::new();
    let mut result_volumes = Vec::new();
    let mut result_groups = Vec::new();

    for ((group_key, timestamp), ohlc) in groups {
        result_timestamps.push(timestamp);
        result_opens.push(ohlc.open);
        result_highs.push(ohlc.high);
        result_lows.push(ohlc.low);
        result_closes.push(ohlc.close);
        result_volumes.push(ohlc.volume);
        result_groups.push(group_key);
    }

    result.set_item("timestamps", PyArray1::from_vec(py, result_timestamps))?;
    result.set_item("open", PyArray1::from_vec(py, result_opens))?;
    result.set_item("high", PyArray1::from_vec(py, result_highs))?;
    result.set_item("low", PyArray1::from_vec(py, result_lows))?;
    result.set_item("close", PyArray1::from_vec(py, result_closes))?;
    result.set_item("volume", PyArray1::from_vec(py, result_volumes))?;

    Ok(result.into())
}

#[derive(Debug, Clone)]
struct OhlcData {
    open: f64,
    high: f64,
    low: f64,
    close: f64,
    volume: f64,
    first_seen: bool,
}

impl OhlcData {
    fn new(price: f64, volume: f64) -> Self {
        Self {
            open: price,
            high: price,
            low: price,
            close: price,
            volume,
            first_seen: true,
        }
    }

    fn update(&mut self, price: f64, volume: f64) {
        if self.first_seen {
            self.first_seen = false;
        } else {
            self.high = self.high.max(price);
            self.low = self.low.min(price);
        }
        self.close = price;
        self.volume += volume;
    }
}

/// Fast data filtering and partitioning
#[pyfunction]
fn partition_data(
    strike_prices: PyReadonlyArray1<f64>,
    close_prices: PyReadonlyArray1<f64>,
    ord_prices: PyReadonlyArray1<f64>,
) -> PyResult<PyDict> {
    let py = Python::acquire_gil().python();
    let result = PyDict::new(py);

    let strikes = strike_prices.as_slice()?;
    let closes = close_prices.as_slice()?;
    let ords = ord_prices.as_slice()?;

    let mut fut_trd_indices = Vec::new();
    let mut fut_ord_indices = Vec::new();
    let mut opt_trd_indices = Vec::new();
    let mut opt_ord_indices = Vec::new();

    for i in 0..strikes.len() {
        let is_future = strikes[i].is_nan();
        let has_close = closes[i] != 0.0;
        let has_ord = ords[i] != 0.0;

        if is_future {
            if has_close {
                fut_trd_indices.push(i as i32);
            }
            if has_ord {
                fut_ord_indices.push(i as i32);
            }
        } else {
            if has_close {
                opt_trd_indices.push(i as i32);
            }
            if has_ord {
                opt_ord_indices.push(i as i32);
            }
        }
    }

    result.set_item("fut_trd_indices", PyArray1::from_vec(py, fut_trd_indices))?;
    result.set_item("fut_ord_indices", PyArray1::from_vec(py, fut_ord_indices))?;
    result.set_item("opt_trd_indices", PyArray1::from_vec(py, opt_trd_indices))?;
    result.set_item("opt_ord_indices", PyArray1::from_vec(py, opt_ord_indices))?;

    Ok(result.into())
}

/// Fast futures trade data aggregation
#[pyfunction]
fn aggregate_futures_trade(
    timestamps: PyReadonlyArray1<i64>,
    symbols: PyReadonlyArray1<i32>,
    expiries: PyReadonlyArray1<i32>,
    close_prices: PyReadonlyArray1<f64>,
    volumes: PyReadonlyArray1<f64>,
    interval_minutes: i64,
) -> PyResult<PyDict> {
    let py = Python::acquire_gil().python();

    let ts_data = timestamps.as_slice()?;
    let sym_data = symbols.as_slice()?;
    let exp_data = expiries.as_slice()?;
    let close_data = close_prices.as_slice()?;
    let vol_data = volumes.as_slice()?;

    let interval_ns = interval_minutes * 60 * 1_000_000_000;
    let mut groups: HashMap<(i32, i32, i64), OhlcData> = HashMap::new();

    for i in 0..ts_data.len() {
        let timestamp = ts_data[i];
        let symbol = sym_data[i];
        let expiry = exp_data[i];
        let close = close_data[i];
        let volume = vol_data[i];

        let time_bucket = (timestamp / interval_ns) * interval_ns;
        let key = (symbol, expiry, time_bucket);

        let entry = groups.entry(key).or_insert(OhlcData::new(close, volume));
        entry.update(close, volume);
    }

    let mut result_timestamps = Vec::new();
    let mut result_symbols = Vec::new();
    let mut result_expiries = Vec::new();
    let mut result_opens = Vec::new();
    let mut result_highs = Vec::new();
    let mut result_lows = Vec::new();
    let mut result_closes = Vec::new();
    let mut result_volumes = Vec::new();

    for ((symbol, expiry, timestamp), ohlc) in groups {
        result_timestamps.push(timestamp);
        result_symbols.push(symbol);
        result_expiries.push(expiry);
        result_opens.push(ohlc.open);
        result_highs.push(ohlc.high);
        result_lows.push(ohlc.low);
        result_closes.push(ohlc.close);
        result_volumes.push(ohlc.volume);
    }

    let result = PyDict::new(py);
    result.set_item("timestamp", PyArray1::from_vec(py, result_timestamps))?;
    result.set_item("symbol", PyArray1::from_vec(py, result_symbols))?;
    result.set_item("expiry", PyArray1::from_vec(py, result_expiries))?;
    result.set_item("open", PyArray1::from_vec(py, result_opens))?;
    result.set_item("high", PyArray1::from_vec(py, result_highs))?;
    result.set_item("low", PyArray1::from_vec(py, result_lows))?;
    result.set_item("close", PyArray1::from_vec(py, result_closes))?;
    result.set_item("volume", PyArray1::from_vec(py, result_volumes))?;

    Ok(result.into())
}

/// Fast futures order data aggregation
#[pyfunction]
fn aggregate_futures_order(
    timestamps: PyReadonlyArray1<i64>,
    symbols: PyReadonlyArray1<i32>,
    expiries: PyReadonlyArray1<i32>,
    ord_prices: PyReadonlyArray1<f64>,
    interval_minutes: i64,
) -> PyResult<PyDict> {
    let py = Python::acquire_gil().python();

    let ts_data = timestamps.as_slice()?;
    let sym_data = symbols.as_slice()?;
    let exp_data = expiries.as_slice()?;
    let ord_data = ord_prices.as_slice()?;

    let interval_ns = interval_minutes * 60 * 1_000_000_000;
    let mut groups: HashMap<(i32, i32, i64), OhlcData> = HashMap::new();

    for i in 0..ts_data.len() {
        let timestamp = ts_data[i];
        let symbol = sym_data[i];
        let expiry = exp_data[i];
        let ord_price = ord_data[i];

        let time_bucket = (timestamp / interval_ns) * interval_ns;
        let key = (symbol, expiry, time_bucket);

        let entry = groups.entry(key).or_insert(OhlcData::new(ord_price, 0.0));
        entry.update(ord_price, 0.0);
    }

    let mut result_timestamps = Vec::new();
    let mut result_symbols = Vec::new();
    let mut result_expiries = Vec::new();
    let mut result_opens = Vec::new();
    let mut result_highs = Vec::new();
    let mut result_lows = Vec::new();
    let mut result_closes = Vec::new();

    for ((symbol, expiry, timestamp), ohlc) in groups {
        result_timestamps.push(timestamp);
        result_symbols.push(symbol);
        result_expiries.push(expiry);
        result_opens.push(ohlc.open);
        result_highs.push(ohlc.high);
        result_lows.push(ohlc.low);
        result_closes.push(ohlc.close);
    }

    let result = PyDict::new(py);
    result.set_item("timestamp", PyArray1::from_vec(py, result_timestamps))?;
    result.set_item("symbol", PyArray1::from_vec(py, result_symbols))?;
    result.set_item("expiry", PyArray1::from_vec(py, result_expiries))?;
    result.set_item("open", PyArray1::from_vec(py, result_opens))?;
    result.set_item("high", PyArray1::from_vec(py, result_highs))?;
    result.set_item("low", PyArray1::from_vec(py, result_lows))?;
    result.set_item("close", PyArray1::from_vec(py, result_closes))?;

    Ok(result.into())
}

/// Fast options trade data aggregation
#[pyfunction]
fn aggregate_options_trade(
    timestamps: PyReadonlyArray1<i64>,
    symbols: PyReadonlyArray1<i32>,
    expiries: PyReadonlyArray1<i32>,
    strikes: PyReadonlyArray1<f64>,
    option_types: PyReadonlyArray1<i32>,
    close_prices: PyReadonlyArray1<f64>,
    interval_minutes: i64,
) -> PyResult<PyDict> {
    let py = Python::acquire_gil().python();

    let ts_data = timestamps.as_slice()?;
    let sym_data = symbols.as_slice()?;
    let exp_data = expiries.as_slice()?;
    let strike_data = strikes.as_slice()?;
    let opt_type_data = option_types.as_slice()?;
    let close_data = close_prices.as_slice()?;

    let interval_ns = interval_minutes * 60 * 1_000_000_000;
    let mut groups: HashMap<(i32, i32, i64, i32, i64), OhlcData> = HashMap::new();

    for i in 0..ts_data.len() {
        let timestamp = ts_data[i];
        let symbol = sym_data[i];
        let expiry = exp_data[i];
        let strike = (strike_data[i] * 100.0) as i64; // Convert to integer for grouping
        let option_type = opt_type_data[i];
        let close = close_data[i];

        let time_bucket = (timestamp / interval_ns) * interval_ns;
        let key = (symbol, expiry, strike, option_type, time_bucket);

        let entry = groups.entry(key).or_insert(OhlcData::new(close, 0.0));
        entry.update(close, 0.0);
    }

    let mut result_timestamps = Vec::new();
    let mut result_symbols = Vec::new();
    let mut result_expiries = Vec::new();
    let mut result_strikes = Vec::new();
    let mut result_option_types = Vec::new();
    let mut result_opens = Vec::new();
    let mut result_highs = Vec::new();
    let mut result_lows = Vec::new();
    let mut result_closes = Vec::new();

    for ((symbol, expiry, strike, option_type, timestamp), ohlc) in groups {
        result_timestamps.push(timestamp);
        result_symbols.push(symbol);
        result_expiries.push(expiry);
        result_strikes.push(strike as f64 / 100.0);
        result_option_types.push(option_type);
        result_opens.push(ohlc.open);
        result_highs.push(ohlc.high);
        result_lows.push(ohlc.low);
        result_closes.push(ohlc.close);
    }

    let result = PyDict::new(py);
    result.set_item("timestamp", PyArray1::from_vec(py, result_timestamps))?;
    result.set_item("symbol", PyArray1::from_vec(py, result_symbols))?;
    result.set_item("expiry", PyArray1::from_vec(py, result_expiries))?;
    result.set_item("strike_price", PyArray1::from_vec(py, result_strikes))?;
    result.set_item("option_type", PyArray1::from_vec(py, result_option_types))?;
    result.set_item("open", PyArray1::from_vec(py, result_opens))?;
    result.set_item("high", PyArray1::from_vec(py, result_highs))?;
    result.set_item("low", PyArray1::from_vec(py, result_lows))?;
    result.set_item("close", PyArray1::from_vec(py, result_closes))?;

    Ok(result.into())
}

/// A Python module implemented in Rust for high-performance data processing
#[pymodule]
fn rust_data_processor(_py: Python, m: &PyModule) -> PyResult<()> {
    m.add_function(wrap_pyfunction!(fast_ohlc_aggregation, m)?)?;
    m.add_function(wrap_pyfunction!(partition_data, m)?)?;
    m.add_function(wrap_pyfunction!(aggregate_futures_trade, m)?)?;
    m.add_function(wrap_pyfunction!(aggregate_futures_order, m)?)?;
    m.add_function(wrap_pyfunction!(aggregate_options_trade, m)?)?;
    Ok(())
}
