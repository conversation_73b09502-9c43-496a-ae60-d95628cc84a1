#!/usr/bin/env python3
"""
Setup script for the Rust data processor module.
"""

from setuptools import setup, find_packages
import subprocess
import sys
import os
from pathlib import Path

def check_rust():
    """Check if Rust is installed."""
    try:
        subprocess.run(["rustc", "--version"], check=True, capture_output=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def install_rust():
    """Install Rust if not present."""
    if not check_rust():
        print("Rust not found. Please install Rust from https://rustup.rs/")
        print("Or run: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh")
        sys.exit(1)

def build_rust_extension():
    """Build the Rust extension using maturin."""
    try:
        # Install maturin if not present
        subprocess.run([sys.executable, "-m", "pip", "install", "maturin"], check=True)
        
        # Build the extension
        subprocess.run(["maturin", "develop", "--release"], check=True, cwd=Path(__file__).parent)
        print("Successfully built Rust extension!")
        
    except subprocess.CalledProcessError as e:
        print(f"Failed to build Rust extension: {e}")
        print("Falling back to Python-only installation...")

# Check for Rust and build extension
install_rust()
build_rust_extension()

setup(
    name="rust-data-processor",
    version="0.1.0",
    description="High-performance financial data processing with Rust backend",
    long_description=open("README.md").read() if os.path.exists("README.md") else "",
    long_description_content_type="text/markdown",
    author="Data Auditing Team",
    python_requires=">=3.8",
    py_modules=["python_wrapper"],
    install_requires=[
        "pandas>=1.5.0",
        "numpy>=1.20.0",
        "maturin>=1.0.0",
    ],
    extras_require={
        "dev": [
            "pytest>=6.0",
            "black>=22.0",
            "mypy>=0.900",
        ]
    },
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Rust",
    ],
)
