import datetime
import calendar
from functools import partial
import numpy as np
import pandas as pd
import os
import time
from multiprocessing import Pool, Manager
import pickle


# def get_symbol_to_balte_id():
#     # get from balte and save it
#     with open("/home/<USER>/repos/data_auditing/symbol_to_balte_id", "rb") as f:
#         return pickle.load(f)


# base_path_206 = "/mnt/companydata/MarketData/eq/tick/root_trd"
# starting_balte_id = 1
# ending_balte_id = 5000
# cash_type = "XX"
# symbol_to_balte_id = get_symbol_to_balte_id()
# errors_on_the_fly_shared_dict = {}


# # dates_list = os.listdir(base_path_206)
# # valid_dates_list = []
# # invalid_dates_list = []

# # for date in dates_list:
# #     if len(date) == 8 and date.isdigit():
# #         valid_dates_list.append(date)
# #     else:
# #         invalid_dates_list.append(date)

# # print("\nGot date list...\n")

# # # /mnt/companydata/MarketData/eq/tick/root_trd/01072022/ZEEL/XX/XX/ZEEL.trd

# # def get_sym_paths(date, shared_list):
# #     path1 = f"{base_path_206}/{date}"
# #     try:
# #         for sym in os.listdir(path1):
# #             if (sym not in symbol_to_balte_id) or (symbol_to_balte_id[sym] >= starting_balte_id and symbol_to_balte_id[sym] <= ending_balte_id):
# #                 shared_list.append(f"{base_path_206}/{date}/{sym}/{cash_type}/{cash_type}/{sym}.trd")
# #     except Exception as e:
# #         print(f"Failed for {date} due to: {e}\n")
# #         return

# # shared_list = Manager().list()

# # print("Started getting all paths...\n")
# # with Pool() as pool:
# #     pool.starmap(get_sym_paths, [(date, shared_list) for date in valid_dates_list])
# # print("Cpmpleted getting all paths\n")

# # all_paths = list(shared_list)
# # all_paths

# # print("Storing all_paths list...\n")
# # with open('cash_trd_all_paths', 'wb') as f:
# #     pickle.dump(all_paths, f)
# # print("Storing all_paths list done\n")


# def _get_raw_trd_data(path: str):
#     try:
#         path_split = path.split("/")[1:]
#         date_str = path_split[6]
#         sym = path_split[-1][:-4]

#         tick_data_csv = pd.read_csv(path, header=None)
#         tick_data_csv = tick_data_csv[(tick_data_csv > 0).all(axis=1)]

#         tick_data_csv["timestamp"] = pd.to_datetime(
#             tick_data_csv[0],
#             unit="s",
#             origin=pd.Timestamp(
#                 day=int(date_str[0:2]),
#                 month=int(date_str[2:4]),
#                 year=int(date_str[4:]),
#             ),
#         )
#         tick_data_csv = tick_data_csv.set_index("timestamp")

#         tick_data_csv = tick_data_csv.rename(
#             columns={0: "time_in_seconds_count", 2: "ltp", 1: "Volume", 3: "Vwap"}
#         )
#         tick_data_csv["Volume"] = tick_data_csv["Volume"].astype("float64")

#         tick_data_csv["ltp"] = tick_data_csv["ltp"] / 100
#         tick_data_csv["Vwap"] = tick_data_csv["Vwap"] / 100

#         tick_data_csv = tick_data_csv.resample("1T", closed="right", label="right").agg(
#             {"ltp": ["first", "max", "min", "last"], "Volume": "sum", "Vwap": "last"}
#         )[["ltp", "Volume", "Vwap"]]

#         tick_data_csv = pd.concat(
#             [tick_data_csv["ltp"], tick_data_csv["Volume"], tick_data_csv["Vwap"]],
#             axis=1,
#         )

#         tick_data_csv = tick_data_csv.dropna()

#         tick_data_csv.columns = ["Open", "High", "Low", "Close", "Volume", "Vwap"]
#         tick_data_csv["ID"] = sym
#         tick_data_csv = tick_data_csv[
#             ["ID", "Open", "High", "Low", "Close", "Vwap", "Volume"]
#         ]

#         if not os.path.exists(f"/home/<USER>/repos/data_auditing/raw_cash/{sym}/"):
#             os.makedirs(f"/home/<USER>/repos/data_auditing/raw_cash/{sym}/")

#         tick_data_csv.to_parquet(
#             f"/home/<USER>/repos/data_auditing/raw_cash/{sym}/{date_str}.parquet"
#         )
#     except Exception as e:
#         errors_on_the_fly_shared_dict[path] = str(e)


# rem_paths_206 = []

# print(f"Loading rem_paths_206 list...\n")
# with open("/home/<USER>/repos/data_auditing/rem_paths_206", "rb") as f:
#     rem_paths_206 = pickle.load(f)
# print(f"Loading rem_paths_206 list completed\n")

# _get_raw_trd_data(path=rem_paths_206[0])

# start_time = time.time()
# print(f"Started getting data from paths at {pd.Timestamp.now()}...\n")

# with Pool() as pool:
#     pool.map(_get_raw_trd_data, rem_paths_206)

# end_time = time.time()
# print(f"Completed getting data for {len(rem_paths_206)} paths in {end_time - start_time} seconds\n")


# print("Dumping errors on the fly...")
# with open(
#     "/home/<USER>/repos/data_auditing/errors_on_the_fly_for_getting_raw_cash_from_tick_for_rem_paths_206",
#     "wb",
# ) as f:
#     pickle.dump(dict(errors_on_the_fly_shared_dict), f)
# print("Dumping completed errors on the fly dict\n")


## merging into one parquet

# base_path = "/home/<USER>/repos/data_auditing/raw_cash"

# print(f"Started combining data files at {pd.Timestamp.now()}...\n")
# start_time = time.time()
# count = 0

# for sym in os.listdir(base_path):
#     path1 = f"{base_path}/{sym}"
#     data_list = []

#     for date in os.listdir(path1):
#         count += 1
#         data_list.append(pd.read_parquet(f"{path1}/{date}"))

#     data = pd.concat(data_list)
#     data = data.sort_index()

#     data.to_parquet(f"{path1}/combined.parquet")

# end_time = time.now()
# print(f"Completed combining {count} data files in {end_time - start_time} seconds\n")


# import os

# base_path = "/home/<USER>/repos/data_auditing/raw_cash"


# def delete_daywise_parquets(base_path):
#     for sym in os.listdir(base_path):
#         path1 = f"{base_path}/{sym}"
#         sym_files = os.listdir(path1)
#         if "combined.parquet" in sym_files:
#             sym_files.remove("combined.parquet")
#             for file in sym_files:
#                 os.remove(f"{base_path}/{sym}/{file}")


# delete_daywise_parquets(base_path)
