## Base class for compiler, checker, operator
## Providing read & write functionality using different store objects

from typing import Any, Dict, List, Union, Optional, cast
import pandas as pd
from main.data.utility import create_metadata_from_report
from main.storage.store_factory import StoreFactory
from main.enums import StorageType
from main.config.config_base import ConfigBase
import logging
from main.storage.storage_base import StoreBase


logger = logging.getLogger("tanki")


class Auditor:
    def __init__(self, config: ConfigBase):
        self._config = config
        self.__stores: Dict[StorageType, StoreBase] = {}

    def list_file_locations(self, storage_type: StorageType) -> List[str]:
        """list all file locations/libraries

        Args:
            storage_type (StorageType): type of storage

        Raises:
            Exception: In case any error occurred while listing file locations

        Returns:
            List[str]: list containing all file locations
        """

        if storage_type not in self.__stores:
            self.__stores[storage_type] = StoreFactory(storage_type=storage_type)

        try:
            file_locations: List[str] = self.__stores[
                storage_type
            ].list_file_locations()
            return file_locations
        except Exception:
            raise Exception(
                f"An error occurred while listing the file locations from store {StorageType.DB}"
            )

    def list_file_names(
        self, storage_type: StorageType, file_location: str
    ) -> List[str]:
        """list all file names/symbols of file location/library

        Args:
            storage_type (StorageType): type of storage
            file_location (str): location/library of file

        Raises:
            Exception: In case any error occurred while listing file names

        Returns:
            List[str]: list containing all file names
        """

        if storage_type not in self.__stores:
            self.__stores[storage_type] = StoreFactory(storage_type=storage_type)

        try:
            file_names: List[str] = self.__stores[storage_type].list_file_names(
                file_location=file_location
            )
            return file_names
        except Exception:
            raise Exception(
                f"An error occurred while listing the file names at {file_location} from store {storage_type}"
            )

    def read(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        start_date: Optional[pd.Timestamp] = None,
        end_date: Optional[pd.Timestamp] = None,
    ) -> Any:
        """For reading file at file_location through different type of store objects.
        Also, if the start and end dates are not specified then whole data is queried.
        If both the start and end dates are specified then data is queried between this date range.
        However, specifying just the start date will query the data from this start date till the end.

        Args:
            storage_type (StorageType): type of storage
            file_location (str): location/library of file
            file_name (str): name/symbol of file
            start_date (Optional[pd.Timestamp]): start_date. Defaults to None.
            end_date (Optional[pd.Timestamp]): end_date. Defaults to None.

        Raises:
            Exception: Read error when found

        Returns:
            pd.DataFrame: fetched data
        """

        logger.info(
            f"Reading {file_name} at {file_location} inside store: {storage_type} with start_date kept as {start_date} and end_date kept as {end_date}..."
        )

        if start_date and end_date:
            if start_date > end_date:
                raise Exception(
                    f"DateRangeError: start_date {start_date} can't be more than end_date {end_date}"
                )
        elif start_date is None and end_date:
            raise Exception(
                f"DateRangeError: start_date can't be None since end_date {end_date} is provided"
            )

        if storage_type not in self.__stores:
            self.__stores[storage_type] = StoreFactory(storage_type=storage_type)

        try:
            data: Any = self.__stores[storage_type].read(
                file_location=file_location,
                file_name=file_name,
                start_date=start_date,
                end_date=end_date,
            )
        except Exception as exception:
            raise Exception(
                f"ReadError: Found error during reading {file_name} at {file_location} from store {storage_type} due to:\n{exception}"
            )

        logger.info(
            f"Successfully read file {file_name} located at {file_location} inside store: {storage_type} "
        )

        return data

    def append(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> int:
        """Appends data in file_name at file_location depending on the storage_type

        Args:
            storage_type (StorageType): storage type
            file_location (str): file location / library name
            file_name (str): file name / symbol
            data (pd.DataFrame): data to be appended
            metadata (Dict[str, Union[str, Dict[str, float]]]): metadata dict

        Raises:
            Exception: Append error when found

        Returns:
            int: version number of the data if call was successful
        """
        logger.info(
            f"Appending to {file_name} at {file_location}inside store: {storage_type} ..."
        )

        if storage_type not in self.__stores:
            self.__stores[storage_type] = StoreFactory(storage_type=storage_type)

        try:
            version_number: int = self.__stores[storage_type].append(
                file_location=file_location,
                file_name=file_name,
                data=data,
                metadata=metadata,
            )
            logger.info(
                f"Successfully appended to {file_name} located at {file_location} inside store: {storage_type}"
            )
            return version_number
        except Exception as exception:
            raise Exception(
                f"AppendError: Found error during appending {file_name} at {file_location} from store {storage_type} due to:\n{exception}"
            )

    def update(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> int:
        """Updates data in file_name at file_location depending on the storage_type

        Args:
            storage_type (StorageType): storage type
            file_location (str): file location / library name
            file_name (str): file name / symbol
            data (pd.DataFrame): data to be appended
            metadata (Dict[str, Union[str, Dict[str, float]]]): metadata dict

        Raises:
            Exception: Update error when found

        Returns:
            int: version number of the data if call was successful
        """
        logger.info(
            f"Updating {file_name} at {file_location} inside store: {storage_type} ..."
        )

        if storage_type not in self.__stores:
            self.__stores[storage_type] = StoreFactory(storage_type=storage_type)

        try:
            version_number: int = self.__stores[storage_type].update(
                file_location=file_location,
                file_name=file_name,
                data=data,
                metadata=metadata,
            )
            logger.info(
                f"Successfully updated {file_name} located at {file_location} inside store: {storage_type}"
            )
            return version_number
        except Exception as exception:
            raise Exception(
                f"UpdateError: Found error during updating {file_name} at {file_location} from store {storage_type} due to:\n{exception}"
            )

    def write(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> int:
        """writes data in file_name at file_location depending on the storage_type

        Args:
            storage_type (StorageType): storage type
            file_location (str): file location / library name
            file_name (str): file name / symbol
            data (pd.DataFrame): data to be appended
            metadata (Dict[str, Union[str, Dict[str, float]]]): metadata dict

        Raises:
            Exception: Write error when found

        Returns:
            int: version number of the data if call was successful
        """
        logger.info(
            f"Writing to {file_name} at {file_location} inside store: {storage_type} ..."
        )

        if storage_type not in self.__stores:
            self.__stores[storage_type] = StoreFactory(storage_type=storage_type)

        try:
            version_number: int = self.__stores[storage_type].write(
                file_location=file_location,
                file_name=file_name,
                data=data,
                metadata=metadata,
            )
            logger.info(
                f"Successfully wrote to {file_name} located at {file_location} inside store: {storage_type}"
            )
            return version_number
        except Exception as exception:
            raise Exception(
                f"WriteError: Found error during writing {file_name} at {file_location} from store {storage_type}: {exception}"
            )

    def read_metadata(
        self, storage_type: StorageType, file_location: str, file_name: str
    ) -> Optional[Dict[str, Union[str, Dict[str, float]]]]:
        """reads metadata dict for file_name in file_location

        Args:
            storage_type (_type_): storage type
            file_location (str): file location / library name
            file_name (str): file name / symbol

        Raises:
            Exception: Metadata read error when found

        Returns:
            Optional[Dict[str, Union[str, Dict[str, float]]]]: metadata dict consisting of file_name's structural information
        """

        logger.info(
            f"Reading metadata dict for file {file_name} at {file_location} inside store: {storage_type} ..."
        )

        if storage_type not in self.__stores:
            self.__stores[storage_type] = StoreFactory(storage_type=storage_type)

        try:
            metadata_dict: Optional[
                Dict[str, Union[str, Dict[str, float]]]
            ] = self.__stores[storage_type].read_metadata(
                file_location=file_location, file_name=file_name
            )
        except Exception:
            logger.exception(
                f"MetadataReadError: Found error during reading metadata_dict from {file_name} located at {file_location} inside store: {storage_type}"
            )
            return None

        logger.info(
            f"Successfully read metadata dict for file {file_name} located at {file_location} inside store: {storage_type}"
        )

        return metadata_dict

    def write_metadata(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> bool:
        """writes the metadata dict for file_name in file_location

        Args:
            storage_type (_type_): storage type
            file_location (str): file location / library name
            file_name (str): file name / symbol
            metadata (Dict[str, Union[str, Dict[str, float]]]): new metadata dict

        Raises:
            Exception: Metadata write error when found

        Returns:
            bool: True if call was successful
        """

        logger.info(
            f"Writing metadata_dict for {file_name} at {file_location} inside store: {storage_type} ..."
        )

        if storage_type not in self.__stores:
            self.__stores[storage_type] = StoreFactory(storage_type=storage_type)

        try:
            self.__stores[storage_type].write_metadata(
                file_location=file_location,
                file_name=file_name,
                metadata=metadata,
            )
        except Exception as exception:
            raise Exception(
                f"MetadataWriteError: Found error during writing metadata_dict to {file_name} located at {file_location} inside store: {storage_type}: {exception}"
            )

        logger.info(
            f"Successfully wrote to {file_name} located at {file_location} inside store: {storage_type}"
        )

        return True

    def update_metadata(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        operation: str,
        checker_report: str,
    ) -> Dict[str, Union[str, Dict[str, float]]]:
        """updates the metadata dict for file_name in file_location

        Args:
            storage_type (_type_): storage type
            file_location (str): file location / library name
            file_name (str): file name / symbol
            data (pd.DataFrame): data to be appended
            operation (str): operation type (append/update)
            checker_report (str): checker report

        Raises:
            Exception: Metadata update error when found

        Returns:
            Dict[str, Union[str, Dict[str, float]]]: updated metadata dict
        """

        file_location_split = file_location.split("/")
        exchange = file_location_split[0]
        universe = file_location_split[2]
        dtype = file_location_split[-1]
        universe_dtype = f"{universe}_{dtype}"

        if (
            ("column_bucket" in file_location)
            or ("slippages" in file_location)
            or ("1440_min" in file_location)
            or ("orderbook" in file_location)
        ):
            col_list = []
        else:
            col_list = self._config.UNIVERSE_DTYPE_TO_COLUMN_DICT.get(
                universe_dtype, self._config.COLUMNS_DICT[universe]
            )
        current_report_metadata = create_metadata_from_report(
            column_list=col_list,
            checked_messages=checker_report,
        )

        index_name = data.index.name
        data = data.reset_index()
        if "column_bucket" in file_location:
            index_name = "date"

        metadata_dict = self.read_metadata(
            storage_type=storage_type,
            file_location=file_location,
            file_name=file_name,
        )

        if metadata_dict is None:
            raise Exception(
                f"MetadataReadError: Not available for {file_name} in {file_location}"
            )

        if "corpact_applied" in checker_report:
            metadata_dict["last_corpact_applied"] = str(self._config.DATE_TODAY)
            return metadata_dict

        if (
            "start_timestamp" not in metadata_dict
            or metadata_dict["start_timestamp"] == ""
        ):
            metadata_dict["start_timestamp"] = str(data.iloc[0]["timestamp"])

        if operation == "update":
            return metadata_dict
        elif operation == "append":
            if (
                metadata_dict["last_timestamp"]
                and pd.Timestamp(cast(str, metadata_dict["last_timestamp"]))
                >= data["timestamp"].iloc[0]
            ):
                raise Exception(
                    f"Data is already appended for {file_name} in {file_location}"
                )

        old_length = int(cast(str, metadata_dict["length"]))
        additional_length = len(data)

        if (
            ("column_bucket" not in file_location)
            and ("slippages" not in file_location)
            and ("1440_min" not in file_location)
            and ("orderbook" not in file_location)
        ):
            for column in data.columns:
                try:
                    additional_nan_count = data[column].isna().sum()
                    metadata_column = cast(Dict[str, float], metadata_dict[column])
                    current_report_metadata_column = cast(
                        Dict[str, float], current_report_metadata[column]
                    )
                    old_nan_ratio = metadata_column["nan_ratio"]
                    metadata_column["nan_ratio"] = float(
                        old_nan_ratio * old_length + additional_nan_count
                    ) / (old_length + additional_length)
                    metadata_column["intraday_jump"] = max(
                        metadata_column["intraday_jump"],
                        current_report_metadata_column["intraday_jump"],
                    )
                    metadata_column["overnight_open_jump"] = max(
                        metadata_column["overnight_open_jump"],
                        current_report_metadata_column["overnight_open_jump"],
                    )
                    metadata_column["overnight_close_jump"] = max(
                        metadata_column["overnight_close_jump"],
                        current_report_metadata_column["overnight_close_jump"],
                    )
                except Exception as exception:
                    raise Exception(
                        f"MetadataUpdateError: Unexpected column {column} found in data while updating metadata_dict after appending to {file_name} located at {file_location} inside store: {storage_type}: {exception}"
                    )

        metadata_dict["length"] = str(old_length + additional_length)
        metadata_dict["last_timestamp"] = str(data[index_name].iloc[-1])
        return metadata_dict

    def update_operation_history(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        version_number: int,
        operation_type: str,
        username: str,
        comment: str,
    ) -> None:
        """adds the operation to the operation_audit_log

        Args:
            storage_type (StorageType): storage type
            file_location (str): file location/library name
            file_name (str): file name/symbol name
            version_number (int): version number of the data
            operation_type (str): type of operation performed (read, append, update, write)
            username (str): username
            comment (str): user comment for the operation

        Raises:
            Exception: In case any error occurred during writing operation history
        """

        if storage_type != StorageType.DB:
            raise Exception(
                f"OperationAuditError: invalid store {storage_type} for writing operation history for the library {file_location}"
            )

        if storage_type not in self.__stores:
            self.__stores[storage_type] = StoreFactory(storage_type=storage_type)

        today_timestamp: pd.Timestamp = pd.Timestamp.today()
        data = {
            "timestamp": [
                pd.to_datetime(today_timestamp.strftime("%Y-%m-%d %H:%M:%S"))
            ],
            "symbol": [file_name],
            "operation_type": [operation_type],
            "username": [username],
            "version_number": [version_number],
            "comment": [comment],
        }
        operation_df = pd.DataFrame(data).set_index("timestamp")

        try:
            self.__stores[storage_type].append(
                file_location="operation_audit_log",
                file_name=file_location,
                data=operation_df,
                metadata={},
            )
        except Exception as exception:
            raise Exception(
                f"OperationAuditError: Found error during writing operation history for {file_name} in {file_location}: {exception}"
            )
