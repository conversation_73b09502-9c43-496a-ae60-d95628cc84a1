## For compiling the list of universes from raw data
## Dumping the compiled data in local storage for futher validation check

from typing import Any, List, Dict, Optional, cast
import pandas as pd
from main.data.auditor import Auditor
from main.data.operation import Operator
from main.enums import StorageType, Operation
from main.config.config_base import ConfigBase
from main.data.utility import (
    get_balte_id,
    get_library_name,
    handle_expiry,
    handle_strike,
)
import logging


class Compiler(Auditor):
    def __init__(self, config: ConfigBase) -> None:
        """Initialize the Compiler object with a given configuration.

        The Compiler is responsible for compiling data from raw universes and their dependencies
        into processed datasets according to the provided configuration.
        Initially it stores compiled data in a temporary library (default location: exchange/compilation) as specified in the LOCAL_FILE_LOCATION config parameter

        Args:
            config (ConfigBase): The configuration settings for the compiler.

        Attributes:
            dag (Dict[str, List[str]]): A dictionary representing the directed acyclic graph.
            _universe_list (List[str]): A list of all universes used in the compilation process.
            symbol_to_ID_dict (Dict[str, int]): A mapping from symbols to their IDs.
            ID_to_symbol_dict (Dict[int, str]): A reverse mapping from IDs back to symbols.
            after_market_library (str): The name of the library for after-market data.
            symbol_change (pd.DataFrame): Data related to changes in symbols.
            demerger_merger (pd.DataFrame): Data related to mergers and demergers.
            operator (Operator): An Operator object used for operations.
            logger (Logger): A logger object for logging messages.
            data_store (object): An object to store universe-wise after-market data and MinIO-related files.
        """
        super().__init__(config=config)
        self.logger = logging.getLogger("tanki")
        self.dag: Dict[
            str, List[str]
        ] = {}  # dict for storing the directed acyclic graph
        self._universe_list: List[
            str
        ] = []  # list of all universes which will be used and compiled in process
        self.symbol_to_ID_dict: Dict[str, int] = self.read(
            storage_type=StorageType.FILE,
            file_location=self._config.FILE_DICT["MAPPING_DICT"][0],
            file_name=self._config.FILE_DICT["MAPPING_DICT"][1],
        )

        self.ID_to_symbol_dict: Dict[int, str] = {}

        for symbol, ID in self.symbol_to_ID_dict.items():
            self.ID_to_symbol_dict[ID] = symbol

        self.after_market_library: str = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name="after_market",
            dtype="trd",
            storage=StorageType.DB,
        )

        try:
            self.symbol_change: pd.DataFrame = self.read(
                storage_type=StorageType.DB,
                file_location=self.after_market_library,
                file_name="symbol_change",
            )
        except Exception as e:
            self.logger.warning(f"Failed to load symbol_change: {e}")
            self.logger.warning("Initializing empty symbol_change")

            self.symbol_change = pd.DataFrame(columns=["symbol", "current"])
            self.symbol_change.index.name = "date"

        try:
            self.demerger_merger: pd.DataFrame = self.read(
                storage_type=StorageType.DB,
                file_location=self.after_market_library,
                file_name="demerger_merger",
            )
        except Exception as e:
            self.logger.warning(f"Failed to load demerger_merger: {e}")
            self.logger.warning("Initializing empty demerger_merger")

            self.demerger_merger = pd.DataFrame(columns=["Symbol", "ID"])
            self.demerger_merger.index.name = "exdate"

        self.operator: Operator = Operator(config)
        self.data_store = type(
            "DataStore", (), {}
        )()  # object to store universe wise after market data and minio related files

    def _check_universe_list(self, universe_list: List[str]) -> None:
        """Checks if all universes in the provided list belong to the exchange.

        Args:
            universe_list (List[str]): A list of universe identifiers to be validated.

        Raises:
            Exception: If there are invalid universes in the list.
                The exception message includes the list of invalid universes.

        Examples:
            >>> compiler._check_universe_list(['valid_universe'])
            None

            >>> compiler._check_universe_list(['invalid_universe'])
            Traceback (most recent call last):
                ...
            Exception: CompileError: ['invalid_universe'] are invalid universes in universe_list!
        """
        universe_list_for_exchange: List[str] = list(self._config.COLUMNS_DICT.keys())
        invalid_universe: List[str] = [
            universe
            for universe in universe_list
            if universe not in universe_list_for_exchange
        ]

        if invalid_universe:
            raise Exception(
                f"CompileError: {invalid_universe} are invalid universes in universe_list!"
            )

        self.logger.info(
            f"Compiler: {universe_list} belongs to {self._config.EXCHANGE}"
        )

    def _extract_raw_dependencies(self, universe: str) -> None:
        """Extracts raw universes and adds them to self._universe_list.

        Note:
            1. Raw universes are universes which don't need any processing or compilation.
            2. These raw universes are the nodes in DAG which are not dependent on any other universe.

        Args:
            universe (str): Name of the universe to extract raw dependencies for.

        Returns:
            None
        """
        if universe[:3] == "raw":
            self._universe_list.append(universe)
            return

        ## Recursive call to PRE_DEPENDENCIES
        for pre_universe in self._config.PRE_DEPENDENCIES[universe]:
            self._extract_raw_dependencies(universe=pre_universe)

        return

    def _add_post_dependencies(self, universe: str) -> None:
        """Adds universe node in DAG and its dependencies.

        Note:
            1. Adds the dependencies in DAG by adding edge from universe to post_dependency.
            2. Also adds the post_universe to self._universe_list for further dependency extraction.

        Args:
            universe (str): Name of the universe to add post dependencies for.

        Returns:
            None
        """
        if universe not in self.dag:
            ## Adding universe node in DAG
            self.dag[universe] = []

        for post_universe in self._config.POST_DEPENDENCIES[universe]:
            ## Adding edge from universe to post_universe
            if post_universe not in self.dag[universe]:
                self.dag[universe].append(post_universe)

            ## Adding post_universe to self._universe_list
            ## so that its post dependencies are also extracted
            if post_universe not in self._universe_list:
                self._universe_list.append(post_universe)

        return

    def _add_universe_list(self, universe_list: List[str]) -> None:
        """Loads all universes in self._universe_list
        Also, form directed graph with the help of _extract_raw_dependencies and _add_post_dependencies

        Args:
            universe_list (List[str]): list of universes to be compiled.

        Returns:
            None
        """

        ## Extracting the raw universes i.e. the nodes with no dependencies
        for universe in universe_list:
            self._extract_raw_dependencies(universe=universe)

        ## Extracting all the dependencies and forming DAG based on them.
        for universe in self._universe_list:
            self._add_post_dependencies(universe=universe)

        self.logger.info(
            f"Compiler: Final list of universes to be compiled = {self._universe_list}"
        )

        return

    def _load_after_market_data(self, universe: str) -> None:
        """Loads after-market data for a specified universe.

        Args:
            universe (str): The name of the universe.

        Returns:
            None

        Raises:
            ValueError: If the universe is not in the configuration.
        """

        if universe not in self._config.UNIVERSE_TO_AFTER_MARKET_SYMBOL_LIST:
            return
        for symbol in self._config.UNIVERSE_TO_AFTER_MARKET_SYMBOL_LIST[universe]:
            data = self.read(
                storage_type=StorageType.DB,
                file_location=self.after_market_library,
                file_name=symbol,
            )
            setattr(self.data_store, symbol, data)

    def _load_minio_files_list(self, universe: str) -> None:
        """Loads files from MinIO for a given universe.

        Args:
            universe (str): The name of the universe to load files for.

        Returns:
            None
        """
        if universe not in self._config.UNIVERSE_TO_MINIO_FILES_LIST:
            return

        for file, location in self._config.UNIVERSE_TO_MINIO_FILES_LIST[
            universe
        ].items():
            data = self.read(
                storage_type=StorageType.FILE,
                file_location=self._config.FILE_DICT[location][0],
                file_name=self._config.FILE_DICT[location][1],
            )

            setattr(self.data_store, file, data)

    def __clear_universe_data(self) -> None:
        """Clears all data stored in the data_store object.

        This method removes all attributes from the data_store object to free up memory
        and prepare it for storing data for a new universe.

        Returns:
            None
        """
        self.data_store.__dict__.clear()

    ## TODO: define/shift more functions in operations to reduce the calculation part here in compiler
    def _add_columns(
        self,
        universe: str,
        universe_df: pd.DataFrame,
        frequency: int,
        dtype: str,
        symbol: str,
        start_date: pd.Timestamp,
        end_date: pd.Timestamp,
    ) -> pd.DataFrame:
        """Add columns which are missing in dataframe as per the universe column dict.

        This method checks the required columns for a universe and adds any missing columns
        by performing appropriate operations based on the column type. It handles various
        column types including balte_id, close_raw, OI, pcr, greeks, and more.

        Args:
            universe (str): Name of the universe.
            universe_df (pd.DataFrame): Universe DataFrame.
            frequency (int): Sampling frequency of data.
            dtype (str): Data type of data.
            symbol (str): Symbol identifier.
            start_date (pd.Timestamp): Start date for the data. Defaults to None.
            end_date (pd.Timestamp]): End date for the data. Defaults to None.

        Raises:
            Exception: If a column that is expected to be present in the data is not found
                      and cannot be derived from existing columns.

        Returns:
            pd.DataFrame: Final universe DataFrame containing all required columns.
        """

        store_info = (
            "_"
            + symbol
            + "_"
            + start_date.strftime("%Y%m%d")
            + "_"
            + end_date.strftime("%Y%m%d")
        )

        columns_added: List[str] = []

        universe_dtype = f"{universe}_{dtype}"
        col_list = (
            self._config.UNIVERSE_DTYPE_TO_COLUMN_DICT[universe_dtype]
            if universe_dtype in self._config.UNIVERSE_DTYPE_TO_COLUMN_DICT
            else self._config.COLUMNS_DICT[universe]
        )
        for column in col_list:
            if (
                column == "timestamp" or column == "date"
            ) and column in universe_df.index.names:
                continue
            if column not in universe_df:
                ## then look for possible column operations
                ## and their respective requirements

                if column in ["near_month", "next_month", "far_month"]:
                    continue

                elif column == "balte_id":
                    expiry_col_list = [
                        exp_col
                        for exp_col in [
                            "expiry",
                            "expiry_date",
                            "expiry_dt",
                            "expiry date",
                        ]
                        if exp_col in universe_df.columns
                    ]
                    option_type_col_list = [
                        opt_typ_col
                        for opt_typ_col in ["option_typ", "option_type", "option type"]
                        if opt_typ_col in universe_df.columns
                    ]
                    strike_col_list = [
                        strk_col
                        for strk_col in [
                            "strike",
                            "strike_price",
                            "strike price",
                            "strike_pr",
                        ]
                        if strk_col in universe_df.columns
                    ]

                    if len(universe_df) > 0:
                        universe_df["ID"] = get_balte_id(
                            expiry=universe_df[expiry_col_list[0]]
                            .dt.strftime("%Y%m%d")
                            .astype("uint64")
                            if expiry_col_list
                            else None,
                            sym_id=universe_df["ID"],
                            option_type=universe_df[option_type_col_list[0]]
                            if option_type_col_list
                            else None,
                            strike=universe_df[strike_col_list[0]]
                            if strike_col_list
                            else None,
                            universe=universe,
                        ).astype(self._config.DATA_TYPES_DICT["ID"])

                    else:
                        universe_df["ID"] = []

                    universe_df = universe_df.rename(columns={"ID": "balte_id"})

                    columns_added.append("balte_id")

                elif column == "close_raw":
                    universe_df["close_raw"] = universe_df["close"]
                    columns_added.append("close_raw")

                elif column == "eod_price":
                    universe_df["eod_price"] = universe_df["close"]
                    columns_added.append("eod_price")

                elif column == "adj_factor":
                    universe_df["adj_factor"] = 1

                    universe_df["adj_factor"] = universe_df["adj_factor"].astype(
                        self._config.DATA_TYPES_DICT["adj_factor"]
                    )

                    columns_added.append("adj_factor")

                elif column == "expiry_rank":
                    expiry_df = pd.DataFrame.from_dict(
                        self.data_store.expiry_dict, orient="index"
                    )[["near_month", "next_month"]]
                    expiry_df.index.name = universe_df.index.name
                    universe_df = pd.merge(
                        universe_df,
                        expiry_df,
                        left_index=True,
                        right_index=True,
                        how="left",
                    )

                    universe_df["expiry_rank"] = 3
                    universe_df.loc[
                        universe_df["expiry_dt"] == universe_df["near_month"],
                        "expiry_rank",
                    ] = 1
                    universe_df.loc[
                        universe_df["expiry_dt"] == universe_df["next_month"],
                        "expiry_rank",
                    ] = 2

                    universe_df["expiry_rank"] = universe_df["expiry_rank"].astype(
                        self._config.DATA_TYPES_DICT["expiry_rank"]
                    )
                    universe_df = universe_df.drop(columns=["near_month", "next_month"])
                    columns_added.append("expiry_rank")

                elif column == "repo_rate":
                    self.data_store.repo_rate.index.name = universe_df.index.name

                    universe_df = pd.merge(
                        universe_df,
                        self.data_store.repo_rate,
                        left_index=True,
                        right_index=True,
                        how="left",
                    )

                    universe_df["repo_rate"] = universe_df["repo_rate"].fillna(
                        method="ffill"
                    )

                    first_date = universe_df.index[0]
                    first_repo_rate = self.data_store.repo_rate["repo_rate"].asof(
                        first_date
                    )

                    universe_df["repo_rate"] = universe_df["repo_rate"].fillna(
                        first_repo_rate
                    )

                    columns_added.append("repo_rate")

                elif column in ["OI", "Open_int"]:
                    option_oi_df = self.read(
                        storage_type=StorageType.LOCAL,
                        file_location=self._config.LOCAL_FILE_LOCATION,
                        file_name=get_library_name(
                            exchange_name=self._config.EXCHANGE,
                            frequency=frequency,
                            universe_name=f"raw_{universe}_oi",
                            dtype="trd",
                            storage=StorageType.LOCAL,
                        )
                        + store_info,
                    )
                    universe_df = self.operator.apply_operation(
                        operation_name=Operation.OI,
                        universe_name=universe,
                        option_df=universe_df,
                        option_oi_df=option_oi_df,
                        symbol=symbol,
                    )
                    columns_added.append("OI")
                elif column == "pcr":
                    option_universe = self._config.UNDERLYING_TO_OPTION_MAP[universe]
                    try:
                        option_oi_df = self.read(
                            storage_type=StorageType.LOCAL,
                            file_location=self._config.LOCAL_FILE_LOCATION,
                            file_name=get_library_name(
                                exchange_name=self._config.EXCHANGE,
                                frequency=frequency,
                                universe_name=f"raw_{option_universe}_oi",
                                dtype=dtype,
                                storage=StorageType.LOCAL,
                            )
                            + store_info,
                        )
                    except Exception as e:
                        option_oi_df = pd.DataFrame()

                    universe_df = self.operator.apply_operation(
                        operation_name=Operation.PCR,
                        universe_name=universe,
                        underlying_df=universe_df,
                        option_oi_df=option_oi_df,
                        symbol=symbol,
                    )
                    columns_added.append("pcr")
                elif column in ["iv", "delta", "theta", "vega", "gamma"]:
                    underlying_universe = self._config.OPTION_TO_UNDERLYING_MAP[
                        universe
                    ]
                    underlying_df = self.read(
                        storage_type=StorageType.LOCAL,
                        file_location=self._config.LOCAL_FILE_LOCATION,
                        file_name=get_library_name(
                            exchange_name=self._config.EXCHANGE,
                            frequency=frequency,
                            universe_name=f"raw_{underlying_universe}",
                            dtype="trd",
                            storage=StorageType.LOCAL,
                        )
                        + store_info,
                    )
                    universe_df = self.operator.apply_operation(
                        operation_name=Operation.GREEKS,
                        universe_name=universe,
                        option_df=universe_df,
                        underlying_df=underlying_df,
                        symbol=symbol,
                    )
                    columns_added.extend(["iv", "delta", "theta", "vega", "gamma"])
                elif column == "Cons_Volume" and dtype == "ord":
                    raw_universe = f"raw_{universe}"
                    raw_df = self.read(
                        storage_type=StorageType.LOCAL,
                        file_location=self._config.LOCAL_FILE_LOCATION,
                        file_name=get_library_name(
                            exchange_name=self._config.EXCHANGE,
                            frequency=frequency,
                            universe_name=raw_universe,
                            dtype="trd",
                            storage=StorageType.LOCAL,
                        )
                        + store_info,
                    )
                    universe_df = self.operator.apply_operation(
                        symbol=symbol,
                        operation_name=Operation.CONS_VOLUME,
                        universe_name=universe,
                        option_df=universe_df,
                        underlying_df=raw_df,
                    )
                elif column == "Next_Cons_Volume" and dtype != "ord":
                    raw_universe = f"raw_{universe}"
                    raw_df = self.read(
                        storage_type=StorageType.LOCAL,
                        file_location=self._config.LOCAL_FILE_LOCATION,
                        file_name=get_library_name(
                            exchange_name=self._config.EXCHANGE,
                            frequency=frequency,
                            universe_name=raw_universe,
                            dtype=dtype,
                            storage=StorageType.LOCAL,
                        )
                        + store_info,
                    )
                    universe_df = self.operator.apply_operation(
                        symbol=symbol,
                        operation_name=Operation.NEXT_CONS_VOLUME,
                        universe_name=universe,
                        option_df=universe_df,
                        underlying_df=raw_df,
                        expiry_dict=self.data_store.expiry_dict,
                    )
                    columns_added.append("Next_Cons_Volume")

                elif column == "sym_id":
                    universe_df["sym_id"] = (universe_df["ID"] % 10000).astype(
                        self._config.DATA_TYPES_DICT["sym_id"]
                    )
                    columns_added.append("sym_id")

                elif column in [
                    "expiry",
                    "expiry_date",
                    "expiry_dt",
                    "expiry date",
                ]:
                    universe_df[column] = handle_expiry(universe_df["ID"], universe)

                    try:
                        universe_df[column] = pd.to_datetime(
                            universe_df[column], format="%Y%m%d"
                        )

                    except ValueError as e:
                        universe_df[column] = pd.to_datetime(
                            universe_df[column], format="%y%m%d"
                        )

                    columns_added.append(column)

                elif column in [
                    "strike",
                    "strike_price",
                    "strike price",
                    "strike_pr",
                ]:
                    universe_df[column] = handle_strike(universe_df["ID"], universe)
                    universe_df[column] = universe_df[column].astype(
                        self._config.DATA_TYPES_DICT[column]
                    )
                    columns_added.append(column)

                elif column in ["option_typ", "option_type", "option type"]:
                    universe_df[column] = universe_df["ID"] // 10000 % 10
                    universe_df[column] = universe_df[column].astype(
                        self._config.DATA_TYPES_DICT["option_type"]
                    )
                    columns_added.append(column)
                else:
                    raise Exception(
                        f"CompileError: {column} should be there by default in {universe}!!"
                    )

        self.logger.info(
            f"Compiler: {columns_added} columns added to {universe} dataframe"
        )

        return universe_df

    def _modify_columns(
        self,
        symbol: str,
        universe: str,
        universe_df: pd.DataFrame,
        frequency: int,
        dtype: str,
    ) -> pd.DataFrame:
        """Modify columns in the dataframe based on universe-specific operations.

        This method applies various operations to modify existing columns in the dataframe
        according to the configuration for the given universe. Operations include rounding
        to nearest lot size, restructuring open interest data, applying corporate actions,
        basis adjustments, and dividend removal.

        Args:
            symbol (str): The symbol identifier.
            universe (str): Name of the universe.
            universe_df (pd.DataFrame): Universe DataFrame.
            frequency (int): Sampling frequency of data.
            dtype (str): Data type of data.

        Returns:
            pd.DataFrame: Modified universe DataFrame with updated column values.

        Raises:
            KeyError: If a necessary key is not found in the data_store.
        """
        if universe not in self._config.MODIFY_COLUMNS_OPERATIONS:
            return universe_df
        for operation in self._config.MODIFY_COLUMNS_OPERATIONS[universe]:
            if operation == "round_to_nearest_lotsize":
                lotsize = self.data_store.lotsize_stk[
                    self.data_store.lotsize_stk.ID == int(symbol)
                ]
                lotsize["far_month"] = lotsize["next_month"]

                universe_df = self.operator.apply_operation(
                    operation_name=Operation.MODIFY,
                    symbol=symbol,
                    universe_name=universe,
                    universe_df=universe_df,
                    lotsize=lotsize,
                )

            elif operation == "restructure_oi":
                universe_df = self.operator.apply_operation(
                    operation_name=Operation.MODIFY,
                    symbol=symbol,
                    universe_name=universe,
                    universe_df=universe_df,
                    expiry_dict=self.data_store.expiry_dict,
                    symbol_map=self.symbol_to_ID_dict,
                    value_column="OI",
                    expiry_categories=["near_month", "next_month", "far_month"],
                    ffill=True,
                )

            elif operation == "apply_corpact":
                corpact_info = self.data_store.corpact[
                    self.data_store.corpact.ID == int(symbol)
                ]

                if len(corpact_info) == 0:
                    continue

                universe_df = self.operator.apply_operation(
                    operation_name=Operation.MODIFY,
                    symbol=symbol,
                    universe_name=universe,
                    universe_df=universe_df,
                    corpact_info=corpact_info,
                )

            elif operation == "apply_basis_adjustment":
                universe_df = self.operator.apply_operation(
                    symbol=symbol,
                    operation_name=Operation.MODIFY,
                    universe_name=universe,
                    universe_df=universe_df,
                    reporate_info=self.data_store.repo_rate,
                )

            elif operation == "apply_dividend_removal":
                corpact_dividend_info = self.data_store.corpact_dividend_info[
                    self.data_store.corpact_dividend_info["ID"] == int(symbol)
                ]

                if len(corpact_dividend_info) == 0:
                    continue

                universe_df = self.operator.apply_operation(
                    symbol=symbol,
                    operation_name=Operation.MODIFY,
                    universe_name=universe,
                    universe_df=universe_df,
                    corpact_dividend_info=corpact_dividend_info,
                    expiry_dict=self.data_store.expiry_dict,
                )

        return universe_df

    ## TODO: instead of making ID in this function, lets have some dedicated function for this
    def _filter_data(
        self,
        symbol: str,
        universe: str,
        universe_df: pd.DataFrame,
        frequency: int,
        dtype: str,
        start_date: pd.Timestamp,
        end_date: pd.Timestamp,
    ) -> pd.DataFrame:
        """Filters the data based on the given parameters.

        This method applies various filtering operations to the dataframe based on the
        universe configuration. For raw universes, it handles symbol changes, ID assignments,
        and balte_id generation. For other universes, it applies specific filtering operations
        like filtering by F&O list, near contracts, or series.

        Args:
            symbol (str): The symbol of the data.
            universe (str): The name of the universe.
            universe_df (pd.DataFrame): The initial DataFrame containing the data.
            frequency (int): Frequency of the data.
            dtype (str): Data type of the data.
            start_date (Optional[pd.Timestamp], optional): Start date for filtering. Defaults to None.
            end_date (Optional[pd.Timestamp], optional): End date for filtering. Defaults to None.

        Returns:
            pd.DataFrame: Filtered DataFrame based on the specified criteria.

        Raises:
            KeyError: If required keys are not found in the configuration or data store.
            ValueError: If data validation fails during filtering operations.
        """
        if universe[:3] == "raw":
            universe_df = self.operator.apply_operation(
                operation_name=Operation.FILTER,
                symbol=symbol,
                universe_name=universe,
                frequency=frequency,
                dtype=dtype,
                symbol_change=self.symbol_change,
                start_date=start_date,
                end_date=end_date,
            )
            universe_df["ID"] = self.symbol_to_ID_dict[symbol]
            universe_df["ID"] = universe_df["ID"].astype(
                self._config.DATA_TYPES_DICT["ID"]
            )
            universe_df = self.operator.apply_operation(
                operation_name=Operation.FILTER,
                symbol=symbol,
                ID=self.symbol_to_ID_dict[symbol],
                universe_name=universe,
                universe_df=universe_df,
                demerger_merger=self.demerger_merger,
            )
            expiry_col_list = [
                exp_col
                for exp_col in ["expiry", "expiry_date", "expiry_dt", "expiry date"]
                if exp_col in universe_df.columns
            ]
            option_type_col_list = [
                opt_typ_col
                for opt_typ_col in ["option_typ", "option_type", "option type"]
                if opt_typ_col in universe_df.columns
            ]
            strike_col_list = [
                strk_col
                for strk_col in [
                    "strike",
                    "strike_price",
                    "strike price",
                    "strike_pr",
                ]
                if strk_col in universe_df.columns
            ]

            if len(universe_df) > 0:
                universe_df["ID"] = get_balte_id(
                    expiry=universe_df[expiry_col_list[0]]
                    .dt.strftime("%Y%m%d")
                    .astype("uint64")
                    if expiry_col_list
                    else None,
                    sym_id=universe_df["ID"],
                    option_type=universe_df[option_type_col_list[0]]
                    if option_type_col_list
                    else None,
                    strike=universe_df[strike_col_list[0]] if strike_col_list else None,
                    universe=universe,
                ).astype(cast(Any, self._config.DATA_TYPES_DICT["ID"]))

            else:
                universe_df["ID"] = []

        if universe not in self._config.FILTER_DATA_OPERATIONS:
            return universe_df
        for operation in self._config.FILTER_DATA_OPERATIONS[universe]:
            if operation == "filter_id_fno":
                universe_df = self.operator.apply_operation(
                    symbol=symbol,
                    operation_name=Operation.FILTER,
                    universe_name=universe,
                    universe_df=universe_df,
                    id_list=self.data_store.isfno,
                )
            elif operation == "filter_near_contract":
                universe_df = self.operator.apply_operation(
                    symbol=symbol,
                    operation_name=Operation.FILTER,
                    universe_name=universe,
                    universe_df=universe_df,
                    expiry_dict=self.data_store.expiry_dict,
                    expiry_categories=["near_month"],
                    filter_rank=1,
                )
            elif operation == "filter_series":
                universe_df = self.operator.apply_operation(
                    operation_name=Operation.FILTER,
                    symbol=symbol,
                    universe_name=universe,
                    universe_df=universe_df,
                    series_filter_list=self._config.SERIES_FILTER_MAP[universe],
                )

        return universe_df

    def _compile_dag(
        self,
        frequency: int,
        dtype: str,
        start_date: Optional[pd.Timestamp],
        end_date: Optional[pd.Timestamp],
    ) -> None:
        """Internal function for compilation using DAG.

        Apply topological sort to determine the order of compilation based on dependencies.
        Note that each exchange should have a raw library in the DB store where its raw
        universes data is stored. This raw library data acts as root data for compilation.
        For storing temporary/compiled data, the LOCAL_FILE_LOCATION library will be used.

        Args:
            frequency (int): Sampling frequency.
            dtype (str): Data dtype.
            start_date (Optional[pd.Timestamp], optional): The start date for data compilation. Defaults to None.
            end_date (Optional[pd.Timestamp], optional): The end date for data compilation. Defaults to None.

        Returns:
            None

        Raises:
            KeyError: If required configuration keys are missing.
            ValueError: If there's an issue with the topological sort or data processing.
        """
        ## 1. storing in degrees for each universe i.e measure of dependencies the node have
        in_degree: Dict[str, int] = {}

        for key, value in self.dag.items():
            if key not in in_degree:
                in_degree[key] = 0

            for child in value:
                if child not in in_degree:
                    in_degree[child] = 1
                else:
                    in_degree[child] += 1

        ## 2. Maintaining a queue and pushing nodes with in_degree = 0 i.e raw universes
        queue: List[str] = []
        start_pointer: int = 0

        for key in self.dag:
            if in_degree[key] == 0:
                queue.append(key)

        ## 3. Topological Sort
        while len(queue) > start_pointer:
            ## processing for node at start_pointer
            node = queue[start_pointer]  ## universe name
            start_pointer += 1

            ## updating in degree of nodes
            ## i.e. decreasing the measure of dependencies of its child
            ## as the node will be compiled and ready after current iteration
            for child in self.dag[node]:
                in_degree[child] -= 1

                ## When child have no dependency left to be compiled
                if in_degree[child] == 0:
                    queue.append(child)

            universe_dtype = f"{node}_{dtype}"
            dtype_list = ["trd"]
            dtype_orig = dtype
            if (
                universe_dtype in self._config.PRE_DEPENDENCIES
                or universe_dtype in self._config.POST_DEPENDENCIES
            ):
                dtype_list.append("ord")

            if dtype_orig not in dtype_list:
                dtype_list = [dtype_orig]

            self._load_after_market_data(universe=node)
            self._load_minio_files_list(universe=node)

            for dtype in dtype_list:
                ## symbol extraction
                library = get_library_name(
                    exchange_name=self._config.EXCHANGE,
                    frequency=frequency,
                    universe_name=node,
                    dtype=dtype,
                    storage=StorageType.DB,
                )

                if len(self._config.PRE_DEPENDENCIES[node]) > 0:
                    parent = self._config.PRE_DEPENDENCIES[node][0]
                    symbols = []
                    for symbol in self.list_file_names(
                        storage_type=StorageType.LOCAL,
                        file_location=self._config.LOCAL_FILE_LOCATION,
                    ):
                        if f"{parent}_{dtype}" in symbol:
                            date_pattern = f"{start_date.strftime('%Y%m%d') if start_date is not None else ''}_{end_date.strftime('%Y%m%d') if end_date is not None else ''}"
                            if date_pattern in symbol:
                                symbols.append(symbol.split("_")[-3])
                else:
                    symbols = self.list_file_names(
                        storage_type=StorageType.DB, file_location=library
                    )

                if node[:3] == "raw":
                    symbols = [sym for sym in symbols if sym in self.symbol_to_ID_dict]

                for symbol in symbols:
                    metadata = None
                    if start_date is None:
                        metadata = self.read_metadata(
                            storage_type=StorageType.DB,
                            file_location=library,
                            file_name=symbol,
                        )
                        assert metadata is not None
                        start_date = pd.Timestamp(
                            cast(str, metadata["start_timestamp"])
                        )
                    if end_date is None:
                        if metadata is None:
                            metadata = self.read_metadata(
                                storage_type=StorageType.DB,
                                file_location=library,
                                file_name=symbol,
                            )
                            assert metadata is not None
                        end_date = pd.Timestamp(cast(str, metadata["last_timestamp"]))
                    store_info = (
                        "_"
                        + symbol
                        + "_"
                        + start_date.strftime("%Y%m%d")
                        + "_"
                        + end_date.strftime("%Y%m%d")
                    )

                    lib_name = (
                        get_library_name(
                            exchange_name=self._config.EXCHANGE,
                            frequency=frequency,
                            universe_name=node,
                            dtype=dtype,
                            storage=StorageType.LOCAL,
                        )
                        + store_info
                    )

                    try:
                        if int(symbol) in self.ID_to_symbol_dict:
                            sym_name = self.ID_to_symbol_dict[int(symbol)]

                            if (
                                f"{sym_name.lower()}_expiry_dict"
                                in self.data_store.__dict__.keys()
                            ):
                                self.data_store.expiry_dict = getattr(
                                    self.data_store, f"{sym_name.lower()}_expiry_dict"
                                )
                    except Exception as e:
                        pass

                    ## In case of raw universe, just load data and filter
                    if node[:3] == "raw":
                        raw_df = self.read(
                            storage_type=StorageType.DB,
                            file_location=get_library_name(
                                exchange_name=self._config.EXCHANGE,
                                frequency=frequency,
                                universe_name=node,
                                dtype=dtype,
                                storage=StorageType.DB,
                            ),
                            file_name=symbol,
                            start_date=start_date,
                            end_date=end_date,
                        )

                        raw_df = self._filter_data(
                            symbol=symbol,
                            universe=node,
                            universe_df=raw_df,
                            frequency=frequency,
                            dtype=dtype,
                            start_date=start_date,
                            end_date=end_date,
                        )

                        for ID in (raw_df["ID"] % 10000).unique():
                            data_ID = raw_df[raw_df["ID"] % 10000 == ID]
                            self.write(
                                storage_type=StorageType.LOCAL,
                                file_location=self._config.LOCAL_FILE_LOCATION,
                                file_name=lib_name.replace(symbol, str(ID)),
                                data=data_ID,
                                metadata={},
                            )
                        continue

                    ## First pre_dependency is the main parent
                    parent = self._config.PRE_DEPENDENCIES[node][0]
                    universe_df = self.read(
                        storage_type=StorageType.LOCAL,
                        file_location=self._config.LOCAL_FILE_LOCATION,
                        file_name=get_library_name(
                            exchange_name=self._config.EXCHANGE,
                            frequency=frequency,
                            universe_name=parent,
                            dtype=dtype,
                            storage=StorageType.LOCAL,
                        )
                        + store_info,
                    )

                    ## add missing columns
                    universe_df = self._add_columns(
                        universe=node,
                        universe_df=universe_df,
                        frequency=frequency,
                        dtype=dtype,
                        symbol=symbol,
                        start_date=start_date,
                        end_date=end_date,
                    )

                    ## update data in columns
                    universe_df = self._modify_columns(
                        universe=node,
                        universe_df=universe_df,
                        frequency=frequency,
                        dtype=dtype,
                        symbol=symbol,
                    )

                    # filter data
                    universe_df = self._filter_data(
                        symbol=symbol,
                        universe=node,
                        universe_df=universe_df,
                        frequency=frequency,
                        dtype=dtype,
                        start_date=start_date,
                        end_date=end_date,
                    )

                    # Skip the first column, which is assumed to be an index
                    universe_df = universe_df[self._config.COLUMNS_DICT[node][1:]]

                    self.write(
                        storage_type=StorageType.LOCAL,
                        file_location=self._config.LOCAL_FILE_LOCATION,
                        file_name=lib_name,
                        data=universe_df,
                        metadata={},
                    )

            dtype = dtype_orig
            self.__clear_universe_data()
            self.logger.info(f"Compiler: Compilation done for {node}")

        return

    def compile(
        self,
        universe_list: List[str],
        frequency: int = 1,
        dtype: str = "trd",
        start_date: Optional[pd.Timestamp] = None,
        end_date: Optional[pd.Timestamp] = None,
    ) -> str:
        """Compiles data for a list of universes with the specified frequency and data type.

        Note: Universes should belong to the same exchange.

        Args:
            universe_list (List[str]): List of universes to compile data for.
            frequency (int, optional): Sampling frequency. Defaults to 1.
            dtype (str, optional): Data type. Defaults to "trd".
            start_date (Optional[pd.Timestamp], optional): Start date for the compilation period. Defaults to None.
            end_date (Optional[pd.Timestamp], optional): End date for the compilation period. Defaults to None.

        Returns:
            str: Response message indicating the success if compilation was successful.

        Raises:
            ValueError: If the universe list is invalid.
            Exception: If there are errors during the compilation process.

        Example:
            >>> compiler.compile(['UNIVERSE1', 'UNIVERSE2'], 5, 'trd')
            'Compilation Success!!'
        """

        self.logger.info(
            f"Compiler: Received compile request for {universe_list} with frequency = {frequency} and dtype = {dtype}"
        )

        ## Reset Compiler's universe_list and dag
        self._universe_list = []
        self.dag = {}

        ## Validate whether valid universes or not
        self._check_universe_list(universe_list=universe_list)

        ## Directed Graph formation
        self._add_universe_list(universe_list=universe_list)

        ## Apply topological sort and store in local storage/ temporary storage
        self._compile_dag(
            frequency=frequency, dtype=dtype, start_date=start_date, end_date=end_date
        )

        self.logger.info(
            f"Compiler: Compilation done for {self._universe_list} with frequency = {frequency} and dtype = {dtype} at {self._config.LOCAL_FILE_LOCATION}"
        )

        return "Compilation Success!!"
