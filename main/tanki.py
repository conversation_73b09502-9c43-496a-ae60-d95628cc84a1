import pandas as pd
from typing import List, Optional, Dict, cast
import bcrypt
from main.library import Library
from main.data.checker import Checker
from main.data.compiler import Compiler
from main.enums import StorageType
from main.config.config_factory import ConfigFactory
from main.storage.config import HASH_SALT
import logging

logger = logging.getLogger("tanki")


class Tanki:
    """A data management system for market data with authentication and access control.

    Tanki provides a unified interface for accessing, validating, and compiling market
    data across different libraries. It manages user authentication, permissions,
    and data integrity checks.

    Example:
        >>> tanki = Tanki(exchange_type="nse")
        >>> tanki.login("username", "password")
        >>> libraries = tanki.list_libraries()
        >>> symbols = tanki["nse/5_min/opt/trd"].list_symbols()
        >>> data = tanki["nse/5_min/opt/trd"].read(symbol="5001")
    """

    def __init__(self, exchange_type: str = "nse") -> None:
        """Initialize a new Tanki instance with the specified exchange type.

        Args:
            exchange_type (str, optional): The exchange type to use. Defaults to "nse".
                This determines which configuration will be loaded.

        Raises:
            Exception: If the specified exchange_type is not implemented.

        Example:
            >>> tanki = Tanki(exchange_type="nse")
            >>> isinstance(tanki, Tanki)
            True
        """
        self.__config = ConfigFactory(exchange_type=exchange_type)
        self.__library_list: Dict[str, Library] = {}
        self.__checker: Optional[Checker] = None
        self.__logged_in: bool = False
        self.__user_data: Optional[pd.DataFrame] = None
        self.__compiler: Optional[Compiler] = None

    def __getitem__(self, lib_name: str) -> Library:
        """Access a library by name, caching the library if it has already been accessed.

        This method enables dictionary-style access to libraries (e.g., tanki["library_name"]).
        If the requested library doesn't exist yet, it will be created. Access to any library
        requires the user to be logged in.

        Args:
            lib_name (str): Name of the library to access or initialize.

        Raises:
            Exception: If the user tries to access a library without being logged in.

        Returns:
            Library: The requested library object.

        Example:
            >>> tanki = Tanki()
            >>> tanki.login("username", "password")
            >>> metadata_lib = tanki["nse/5_min/opt/trd"]
            >>> isinstance(metadata_lib, Library)
            True
        """

        if (self.__logged_in == False) and (
            lib_name != self.__config.META_DATA_LIBRARY
        ):
            raise Exception("Permission denied: user not logged in")

        if lib_name not in self.__library_list:
            self.__library_list[lib_name] = Library(
                config=self.__config, lib_name=lib_name
            )
            self.__library_list[lib_name].user_data = self.__user_data
        return self.__library_list[lib_name]

    def list_libraries(self) -> List[str]:
        """Retrieve a list of all available libraries.

        This method returns a list of all libraries that are available in the system.
        The user must be logged in to access this information.

        Raises:
            Exception: If the user is not logged in.

        Returns:
            List[str]: A list containing the names of all available libraries.

        Example:
            >>> tanki = Tanki()
            >>> tanki.login("username", "password")
            >>> libraries = tanki.list_libraries()
            >>> libraries
            ['nse/5_min/opt/trd', 'nse/5_min/opt/ord', ...]
        """

        if self.__logged_in == False:
            raise Exception("Permission denied: user not logged in")

        # Auditor is not accessible via Tanki, so we're utilizing a Library class for accessing the Auditor instead
        # accessing Library class through the metadata library, which has already been initialized when the user is logged in
        library_list: List[str] = self.__library_list[
            self.__config.META_DATA_LIBRARY
        ].list_file_locations()
        return library_list

    def get_read_permissions(self) -> List[str]:
        """Retrieve the list of libraries that the current user has read access to.

        This method returns a sorted list of all libraries that the currently logged-in
        user has permission to read from. The user must be logged in to access this information.

        Raises:
            Exception: If the user is not logged in.
            Exception: If the user data cannot be found.

        Returns:
            List[str]: A sorted list of library names that the current user has read access to.

        Example:
            >>> tanki = Tanki()
            >>> tanki.login("username", "password")
            >>> read_permissions = tanki.get_read_permissions()
            >>> read_permissions
            ['nse/5_min/opt/trd', 'nse/5_min/opt/ord', ...]
        """
        if self.__logged_in == False:
            raise Exception("Permission denied: user not logged in")
        if self.__user_data is None:
            raise Exception("UserInfoError: user data not found")

        return sorted(list(set(self.__user_data.read_permissions.dropna())))

    def get_write_permissions(self) -> List[str]:
        """Retrieve the list of libraries that the current user has write access to.

        This method returns a sorted list of all libraries that the currently logged-in
        user has permission to write to. The user must be logged in to access this information.

        Raises:
            Exception: If the user is not logged in.
            Exception: If the user data cannot be found.

        Returns:
            List[str]: A sorted list of library names that the current user has write access to.

        Example:
            >>> tanki = Tanki()
            >>> tanki.login("username", "password")
            >>> write_permissions = tanki.get_write_permissions()
            >>> write_permissions
            ['nse/5_min/opt/trd', 'nse/5_min/opt/ord', ...]
        """
        if self.__logged_in == False:
            raise Exception("Permission denied: user not logged in")
        if self.__user_data is None:
            raise Exception("UserInfoError: user data not found")

        return sorted(list(set(self.__user_data.write_permissions.dropna())))

    def check_data(
        self,
        storage_type: str,
        file_location: str,
        file_name: str,
        data: Optional[pd.DataFrame] = None,
        check_list: Optional[List[str]] = None,
        ignore_checks: Optional[List[str]] = None,
    ) -> str:
        """Perform validation checks on market data.

        This method runs a series of data quality and integrity checks on the specified
        market data. The user can specify which checks to run and which to ignore.
        The user must be logged in to use this functionality.

        Args:
            storage_type (str): The type of storage where the data is located.
                Can be "db", "local", or "file".
            file_location (str): The location of the file or universe name.
            file_name (str): The name of the file or symbol to check.
            data (Optional[pd.DataFrame], optional): The data to check. If None, the data
                will be loaded from the specified location. Defaults to None.
            check_list (Optional[List[str]], optional): List of specific checks to perform.
                Defaults to None, which means all checks will be performed except those in ignore_checks.
                Available checks:
                    1. check_columns_set - Validates the set of columns in the data
                    2. check_columns_dtype - Validates the data types of columns
                    3. check_nan_entries - Checks for NaN values in the data
                    4. check_duplicate_entries - Identifies duplicate entries
                    5. check_monotonic_nature - Verifies time series monotonicity of coloumns like "Cons_Volume"
                    6. check_all_dates - Validates date formats and completeness
                    7. check_all_timestamps - Validates timestamp formats and completeness
                    8. check_forward_fill - Checks for proper forward filling of data
                    9. check_OHLC - Validates Open-High-Low-Close price relationships
                    10. check_intraday_sudden_jumps - Identifies unusual price movements within a day
                    11. check_overnight_sudden_jumps - Identifies unusual price movements between days
                Pass ["all"] to run all available checks.
            ignore_checks (Optional[List[str]], optional): List of checks to skip.
                Defaults to None. Pass ["all"] to skip all checks.

        Raises:
            Exception: If the specified storage type is not implemented.
            Exception: If the user is not logged in.

        Returns:
            str: A detailed report of all performed checks, including any issues found.

        Example:
            >>> tanki = Tanki()
            >>> tanki.login("username", "password")
            >>> result = tanki.check_data(
            ...     storage_type="db",
            ...     file_location="nse/5_min/opt/trd",
            ...     file_name="5001",
            ...     check_list=["check_columns_set", "check_nan_entries"]
            ... )
            >>> "check_columns_set" in result
            True
        """
        if storage_type not in self.__config.STORE_MAP:
            raise Exception(f"Invalid storage type: {storage_type}")
        if self.__logged_in == False:
            raise Exception("Permission denied: user not logged in")

        if self.__checker is None:
            self.__checker = Checker(config=self.__config)

        check_collective_response: str = self.__checker.check_data(
            storage_type=StorageType(storage_type),
            file_location=file_location,
            file_name=file_name,
            data=data,
            check_list=check_list,
            ignore_checks=ignore_checks,
        )

        return check_collective_response

    def login(self, username: str, password: str) -> None:
        """Authenticate a user and establish a session.

        This method authenticates a user with the provided credentials and establishes
        a session if successful. Each user has specific read/write permissions for
        different libraries, which are loaded from the user's metadata.

        Args:
            username (str): The username for authentication.
            password (str): The password for authentication.

        Raises:
            AssertionError: If username is not a string.
            AssertionError: If password is not a string.
            Exception: If the username is not found in the user_info database.
            Exception: If the provided password is incorrect.

        Example:
            >>> tanki = Tanki()
            >>> try:
            ...     tanki.login("valid_user", "correct_password")
            ...     print("Login successful")
            ... except Exception as e:
            ...     print(f"Login failed: {e}")
            Login successful
        """
        assert isinstance(username, str), f"{username} is not of type str"
        assert isinstance(password, str), f"{password} is not of type str"

        username = username.lower()
        meta_data_library = self[self.__config.META_DATA_LIBRARY]
        df: pd.DataFrame = meta_data_library.read(symbol="user_info")

        user_info = df[df.username == username]
        if len(user_info) == 0:
            raise Exception(f"User {username} does not exist. Please get it created")

        hashpw = bcrypt.hashpw(password.encode("utf-8"), HASH_SALT).decode("utf-8")
        if hashpw not in set(user_info.password_hash):
            raise Exception(f"Wrong password for {username}")

        self.__user_data = user_info
        self.__logged_in = True
        self.__library_list[
            self.__config.META_DATA_LIBRARY
        ].user_data = self.__user_data

    def compile_data(
        self,
        universe_list: List[str],
        frequency: int,
        dtype: str,
        start_date: pd.Timestamp,
        end_date: pd.Timestamp,
    ) -> str:
        """Compile data for specified universes and their dependencies.

        This method processes and compiles data for the specified universes
        and any dependent universes. It stores the compiled data in a temporary
        library for further use. It handles data aggregation, transformation, and
        preparation according to the specified parameters.

        Args:
            universe_list (List[str]): List of universe names to compile.
            frequency (int): The frequency of data points (1 or 5 min).
            dtype (str): The data type for compilation (e.g., "trd", "ord", "column_bucket").
            start_date (pd.Timestamp): The start date for the data compilation period.
            end_date (pd.Timestamp): The end date for the data compilation period.

        Returns:
            str: A detailed response from the compiler indicating success or any issues encountered.

        Example:
            >>> tanki = Tanki()
            >>> tanki.login("username", "password")
            >>> result = tanki.compile_data(
            ...     universe_list=["nse/5_min/opt/trd", "nse/5_min/futidx/trd"],
            ...     frequency=1,
            ...     dtype="trd",
            ...     start_date=pd.Timestamp("2023-01-01"),
            ...     end_date=pd.Timestamp("2023-12-31")
            ... )
            >>> "Compilation Success!!" in result.lower()
            True
        """
        if self.__compiler is None:
            self.__compiler = Compiler(config=self.__config)

        compiler_response: str = self.__compiler.compile(
            universe_list=universe_list,
            frequency=frequency,
            dtype=dtype,
            start_date=start_date,
            end_date=end_date,
        )
        return compiler_response
