import pandas as pd
from typing import Callable, Dict, List, Optional, Union, cast
from main.data.auditor import Auditor
from main.data.checker import Checker
from main.data.utility import create_metadata_from_report
from main.decorators import authorization_check
from main.enums import StorageType
from main.config.config_base import ConfigBase


class Library:
    def __init__(self, config: ConfigBase, lib_name: str) -> None:
        """Initialize a Library instance.

        Args:
            config (ConfigBase): Configuration object.
            lib_name (str): Name of the library.
        """
        self.__config = config
        self.__auditor = Auditor(config=self.__config)
        self.__lib_name = lib_name
        self.__checker = Checker(config=self.__config)
        self.user_data: Optional[pd.DataFrame] = None

    def list_file_locations(self) -> List[str]:
        """list all file locations/libraries
        Note:
            1. We are exclusively utilizing the Library class through the metadata library for this operation, as the Library has access to the Auditor
            2. metadata library is initialized after a successful login

        Raises:
            Exception: In case any error occurred while listing file locations

        Returns:
            List[str]: list containing all file locations
        """

        if self.__lib_name != self.__config.META_DATA_LIBRARY:
            raise Exception("Permission denied: not a valid call")

        try:
            file_locations: List[str] = self.__auditor.list_file_locations(
                storage_type=StorageType.DB
            )
            return file_locations
        except Exception as exception:
            raise Exception(
                f"An error occurred while listing the file locations from store {StorageType.DB}: {repr(exception)}"
            )

    @cast(Callable[..., Callable[..., List[str]]], authorization_check)
    def list_symbols(self) -> List[str]:
        """list all symbols of lib_name

        Raises:
            Exception: In case any error occurred while listing file names

        Returns:
            List[str]: list containing all symbols
        """

        try:
            symbols: List[str] = self.__auditor.list_file_names(
                storage_type=StorageType.DB, file_location=self.__lib_name
            )
            return symbols
        except Exception as exception:
            raise Exception(
                f"An error occurred while listing the symbols at {self.__lib_name} from store {StorageType.DB}: {repr(exception)}"
            )

    @cast(Callable[..., Callable[..., pd.DataFrame]], authorization_check)
    def read(
        self,
        symbol: str,
        start_date: Optional[pd.Timestamp] = None,
        end_date: Optional[pd.Timestamp] = None,
    ) -> pd.DataFrame:
        """
        Reads the given universe data for time range from database

        Args:
            symbol (str): symbol name
            start_date (Optional[pd.Timestamp]): start_date. Defaults to None.
            end_date (Optional[pd.Timestamp]): end_date. Defaults to None.

        Raises:
            Exception: Read error when found

        Returns:
            pd.DataFrame: fetched data
        """

        try:
            df: pd.DataFrame = self.__auditor.read(
                storage_type=StorageType.DB,
                file_location=self.__lib_name,
                file_name=symbol,
                start_date=start_date,
                end_date=end_date,
            )
            return df
        except Exception as exception:
            if str(exception).startswith("DateRangeError"):
                raise Exception(exception)
            raise Exception(
                f"ReadError: Found error during reading {symbol} at {self.__lib_name} from store {StorageType.DB}: {repr(exception)}"
            )

    @cast(Callable[..., Callable[..., bool]], authorization_check)
    def append(self, symbol: str, data: pd.DataFrame, comment: str = "") -> bool:
        """
        1. The data to be appended will be checked
        2. Appends data to the specified symbol of the library named lib_name
        3. Records the corresponding operation in the library named operation_audit_log

        Args:
            symbol (str): symbol id in string format
            data (pd.DataFrame): data to be appended
            comment (str, optional): user comment for the operation, defaults to ""

        Raises:
            Exception: Append error when found

        Returns:
            bool: True if call was successful
        """

        checker_report = ""
        if (
            ("column_bucket" not in self.__lib_name)
            and ("slippages" not in self.__lib_name)
            and ("1440_min" not in self.__lib_name)
            and ("orderbook" not in self.__lib_name)
        ):
            checker_report = self.__checker.check_data_operations(
                storage_type=StorageType.DB,
                file_location=self.__lib_name,
                file_name=symbol,
                data=data,
            )

        try:
            metadata = self.__auditor.update_metadata(
                storage_type=StorageType.DB,
                file_location=self.__lib_name,
                file_name=symbol,
                data=data,
                operation="append",
                checker_report=checker_report,
            )

            version_number = self.__auditor.append(
                storage_type=StorageType.DB,
                file_location=self.__lib_name,
                file_name=symbol,
                data=data,
                metadata=metadata,
            )

            if (
                self.user_data is not None
                and self.user_data["username"].iloc[0] != "airflow"
            ):
                self.__auditor.update_operation_history(
                    storage_type=StorageType.DB,
                    file_location=self.__lib_name,
                    file_name=symbol,
                    version_number=version_number,
                    operation_type="append",
                    username=str(self.user_data["username"][0]),
                    comment=comment,
                )
        except Exception as exception:
            raise Exception(
                f"AppendError: Found error during appending {symbol} at {self.__lib_name} from store {StorageType.DB}: {repr(exception)}"
            )

        return True

    @cast(Callable[..., Callable[..., bool]], authorization_check)
    def update(self, symbol: str, data: pd.DataFrame, comment: str = "") -> bool:
        """
        1. The data to be updated will be checked
        2. Updates data to the specified symbol of the library named lib_name
        3. Records the corresponding operation in the library named operation_audit_log

        Args:
            symbol (str): symbol id in string format
            data (pd.DataFrame): data to be updated
            comment (str, optional): user comment for the operation, defaults to ""

        Raises:
            Exception: Update error when found

        Returns:
            bool: True if call was successful
        """

        checker_report = ""
        if (
            ("column_bucket" not in self.__lib_name)
            and ("slippages" not in self.__lib_name)
            and ("1440_min" not in self.__lib_name)
            and ("orderbook" not in self.__lib_name)
        ):
            checker_report = self.__checker.check_data_operations(
                storage_type=StorageType.DB,
                file_location=self.__lib_name,
                file_name=symbol,
                data=data,
            )

        try:
            metadata = self.__auditor.update_metadata(
                storage_type=StorageType.DB,
                file_location=self.__lib_name,
                file_name=symbol,
                data=data,
                operation="write",
                checker_report=checker_report,
            )

            version_number = self.__auditor.update(
                storage_type=StorageType.DB,
                file_location=self.__lib_name,
                file_name=symbol,
                data=data,
                metadata=metadata,
            )

            if (
                self.user_data is not None
                and self.user_data["username"].iloc[0] != "airflow"
            ):
                self.__auditor.update_operation_history(
                    storage_type=StorageType.DB,
                    file_location=self.__lib_name,
                    file_name=symbol,
                    version_number=version_number,
                    operation_type="update",
                    username=str(self.user_data["username"][0]),
                    comment=comment,
                )
        except Exception as exception:
            raise Exception(
                f"UpdateError: Found error during updating {symbol} at {self.__lib_name} from store {StorageType.DB}: {repr(exception)}"
            )

        return True

    @cast(Callable[..., Callable[..., bool]], authorization_check)
    def write(self, symbol: str, data: pd.DataFrame, comment: str = "") -> bool:
        """
        1. The data to be written will be checked
        2. Writes data to the specified symbol of the library named lib_name
        3. Records the corresponding operation in the library named operation_audit_log

        Args:
            symbol (str): symbol id in string format
            data (pd.DataFrame): data to be written
            comment (str, optional): user comment for the operation, defaults to ""

        Raises:
            Exception: write error when found

        Returns:
            bool: True if call was successful
        """

        checker_report = ""

        if comment == "corpact_applied":
            checker_report = comment
        elif (
            ("column_bucket" not in self.__lib_name)
            and ("slippages" not in self.__lib_name)
            and ("1440_min" not in self.__lib_name)
            and ("orderbook" not in self.__lib_name)
        ):
            checker_report = self.__checker.check_data_operations(
                storage_type=StorageType.DB,
                file_location=self.__lib_name,
                file_name=symbol,
                data=data,
            )

        try:
            metadata = self.__auditor.update_metadata(
                storage_type=StorageType.DB,
                file_location=self.__lib_name,
                file_name=symbol,
                data=data,
                operation="write",
                checker_report=checker_report,
            )
            version_number = self.__auditor.write(
                storage_type=StorageType.DB,
                file_location=self.__lib_name,
                file_name=symbol,
                data=data,
                metadata=metadata,
            )

            if (
                self.user_data is not None
                and self.user_data["username"].iloc[0] != "airflow"
            ):
                self.__auditor.update_operation_history(
                    storage_type=StorageType.DB,
                    file_location=self.__lib_name,
                    file_name=symbol,
                    version_number=version_number,
                    operation_type="write",
                    username=str(self.user_data["username"][0]),
                    comment=comment,
                )
        except Exception as exception:
            raise Exception(
                f"WriteError: Found error during writing {symbol} at {self.__lib_name} from store {StorageType.DB}: {repr(exception)}"
            )

        return True

    def read_metadata(
        self, symbol: str
    ) -> Optional[Dict[str, Union[str, Dict[str, float]]]]:
        """Read metadata for a symbol.

        Args:
            symbol (str): The symbol to read metadata for.

        Raises:
            Exception: If an error occurs while reading metadata.

        Returns:
            Optional[Dict[str, Union[str, Dict[str, float]]]]: The metadata dictionary if successful, None otherwise.
        """
        try:
            metadata: Optional[
                Dict[str, Union[str, Dict[str, float]]]
            ] = self.__auditor.read_metadata(
                storage_type=StorageType.DB,
                file_location=self.__lib_name,
                file_name=symbol,
            )
            return metadata
        except Exception as exception:
            raise Exception(
                f"ReadMetadataError: Found error during reading metadata for the {symbol} at {self.__lib_name} from store {StorageType.DB}: {repr(exception)}"
            )

    def write_metadata(self, symbol: str, data: pd.DataFrame) -> bool:
        """Write metadata for a symbol.

        Args:
            symbol (str): The symbol to write metadata for.
            data (pd.DataFrame): The data to generate metadata from.

        Raises:
            Exception: If an error occurs while writing metadata.

        Returns:
            bool: True if successful.
        """
        try:
            metadata: Dict[str, Union[str, Dict[str, float]]] = {
                "length": "0",
                "start_timestamp": "",
                "last_timestamp": "",
            }
            if (
                ("column_bucket" not in self.__lib_name)
                and ("slippages" not in self.__lib_name)
                and ("1440_min" not in self.__lib_name)
                and ("orderbook" not in self.__lib_name)
            ):
                checked_messages = self.__checker.check_data(
                    storage_type=StorageType.DB,
                    file_location=self.__lib_name,
                    file_name=symbol,
                    data=data,
                )

                universe = self.__lib_name.split("/")[2]
                dtype = self.__lib_name.split("/")[-1]
                universe_dtype = f"{universe}_{dtype}"

                if universe in ["after_market", "SAMPLER_DUMP", "slippages"]:
                    col_list = []
                else:
                    col_list = self.__config.UNIVERSE_DTYPE_TO_COLUMN_DICT.get(
                        universe_dtype, self.__config.COLUMNS_DICT[universe]
                    )
                metadata = create_metadata_from_report(
                    column_list=col_list,
                    checked_messages=checked_messages,
                )

            self.__auditor.write_metadata(
                storage_type=StorageType.DB,
                file_location=self.__lib_name,
                file_name=symbol,
                metadata=metadata,
            )
        except Exception as exception:
            raise Exception(
                f"WriteMetadataError: Found error during writing metadata for the {symbol} at {self.__lib_name} from store {StorageType.DB}: {repr(exception)}"
            )

        return True
