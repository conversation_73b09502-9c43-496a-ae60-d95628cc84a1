## For exchange specific info

from main.config.config_base import ConfigBase
from dataclasses import dataclass, field
from typing import List, Dict, <PERSON><PERSON>, Union
from operator import mul, truediv
from main.enums import ExchangeType, Check


@dataclass
class ConfigNSE(ConfigBase):
    EXCHANGE = "nse"
    EXCHANGE_ENUM = ExchangeType.NSE
    FILE_STORAGE = ""
    DB_STORAGE = ""
    FILE_DICT: Dict[str, Tuple[str, str]] = field(
        default_factory=lambda: {
            "ALL_DATES": ("commondata", "balte_uploads/ALL_DATES.npy"),
            "MAPPING_DICT": ("commondata", "balte_uploads/mapping_dict"),
            "OPTSTK_EXPIRY_DICT": ("commondata", "balte_uploads/optstk_expiry_dict"),
            "NIFTY_EXPIRY_DICT": ("commondata", "balte_uploads/nifty_expiry_dict"),
            "BANKNIFTY_EXPIRY_DICT": (
                "commondata",
                "balte_uploads/banknifty_expiry_dict",
            ),
            "FINNIFTY_EXPIRY_DICT": (
                "commondata",
                "balte_uploads/finnifty_expiry_dict",
            ),
            "NIFTYNXT50_EXPIRY_DICT": (
                "commondata",
                "balte_uploads/niftynxt50_expiry_dict",
            ),
            "MIDCPNIFTY_EXPIRY_DICT": (
                "commondata",
                "balte_uploads/midcpnifty_expiry_dict",
            ),
        }
    )

    MARKET_HOURS_DICT: Dict[str, Dict[str, str]] = field(
        default_factory=lambda: {
            "raw_circuit": {"open": "9:15", "close": "15:30"},
            "circuit": {"open": "9:15", "close": "15:30"},
            "cash": {"open": "9:15", "close": "15:30"},
            "raw_cash": {"open": "9:15", "close": "15:30"},
            "fno": {"open": "9:15", "close": "15:30"},
            "eq": {"open": "9:15", "close": "15:30"},
            "optstk": {"open": "9:15", "close": "15:30"},
            "raw_optstk": {"open": "9:15", "close": "15:30"},
            "raw_optstk_oi": {"open": "9:15", "close": "15:30"},
            "opt": {"open": "9:15", "close": "15:30"},
            "raw_opt": {"open": "9:15", "close": "15:30"},
            "raw_opt_oi": {"open": "9:15", "close": "15:30"},
            "optstk_unadjusted_spot": {"open": "9:15", "close": "15:30"},
            "raw_optstk_unadjusted_spot": {"open": "9:15", "close": "15:30"},
            "futidx": {"open": "9:15", "close": "15:30"},
            "raw_futidx": {"open": "9:15", "close": "15:30"},
            "raw_fut": {"open": "9:15", "close": "15:30"},
            "fut": {"open": "9:15", "close": "15:30"},
            "fut_raw": {"open": "9:15", "close": "15:30"},
            "raw_futidx_fut": {"open": "9:15", "close": "15:30"},
            "futidx_fut": {"open": "9:15", "close": "15:30"},
            "raw_cm_bhav": {"open": "9:15", "close": "15:30"},
            "raw_futidx_bhav": {"open": "9:15", "close": "15:30"},
            "raw_futstk_bhav": {"open": "9:15", "close": "15:30"},
            "raw_optstk_bhav": {"open": "9:15", "close": "15:30"},
            "raw_optidx_bhav": {"open": "9:15", "close": "15:30"},
            "cm_bhav": {"open": "9:15", "close": "15:30"},
            "futidx_bhav": {"open": "9:15", "close": "15:30"},
            "futstk_bhav": {"open": "9:15", "close": "15:30"},
            "optstk_bhav": {"open": "9:15", "close": "15:30"},
            "optidx_bhav": {"open": "9:15", "close": "15:30"},
            "raw_futstk_oi": {"open": "9:15", "close": "15:30"},
            "futstk_oi": {
                "open": "9:15",
                "close": "15:30",
            },  # conventionally balte uses futstk_oi instead of fut_oi
            "futidx_oi": {"open": "9:15", "close": "15:30"},
            "raw_futidx_fut_oi": {"open": "9:15", "close": "15:30"},
            "futidx_fut_oi": {"open": "9:15", "close": "15:30"},
            "raw_eq_ob": {"open": "9:15", "close": "15:30"},
            "eq_ob": {"open": "9:15", "close": "15:30"},
            "raw_fut_ob": {"open": "9:15", "close": "15:30"},
            "fut_ob": {"open": "9:15", "close": "15:30"},
            "raw_futstk_ob": {"open": "9:15", "close": "15:30"},
            "futstk_ob": {"open": "9:15", "close": "15:30"},
        }
    )

    PRE_DEPENDENCIES: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "optstk": ["raw_optstk", "raw_optstk_oi", "raw_optstk_unadjusted_spot"],
            "optstk_ord": [
                "raw_optstk_ord",
                "raw_optstk_oi",
                "raw_optstk_unadjusted_spot",
            ],
            "optstk_unadjusted_spot": ["raw_optstk_unadjusted_spot", "raw_optstk_oi"],
            "raw_optstk": [],
            "raw_optstk_ord": [],
            "raw_optstk_unadjusted_spot": [],
            "raw_optstk_oi": [],
            "opt": ["raw_opt", "raw_opt_oi", "raw_futidx"],
            "opt_ord": ["raw_opt_ord", "raw_opt_oi", "raw_futidx"],
            "futidx": ["raw_futidx", "raw_opt_oi"],
            "raw_opt": [],
            "raw_opt_ord": [],
            "raw_futidx": [],
            "raw_opt_oi": [],
            "cash": ["raw_cash"],
            "raw_cash": [],
            "circuit": ["raw_circuit"],
            "raw_circuit": [],
            "fno": [],
            "fut": ["raw_fut"],
            "raw_fut": [],
            "fut_raw": ["raw_fut"],
            "futidx_fut": ["raw_futidx_fut"],
            "raw_futidx_fut": [],
            "cm_bhav": ["raw_cm_bhav"],
            "raw_cm_bhav": [],
            "raw_futstk_bhav": [],
            "futstk_bhav": ["raw_futstk_bhav"],
            "raw_futidx_bhav": [],
            "futidx_bhav": ["raw_futidx_bhav"],
            "optstk_bhav": ["raw_optstk_bhav"],
            "raw_optstk_bhav": [],
            "optidx_bhav": ["raw_optidx_bhav"],
            "raw_optidx_bhav": [],
            "futstk_oi": ["raw_futstk_oi"],
            "raw_futstk_oi": [],
            "futidx_fut_oi": ["raw_futidx_fut_oi"],
            "raw_futidx_fut_oi": [],
            "raw_eq_ob": [],
            "eq_ob": ["raw_eq_ob"],
            "raw_fut_ob": [],
            "fut_ob": ["raw_fut_ob"],
            "raw_futstk_ob": [],
            "futstk_ob": ["raw_futstk_ob"],
        }
    )
    POST_DEPENDENCIES: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "optstk": [],
            "optstk_ord": [],
            "optstk_unadjusted_spot": [],
            "raw_optstk": ["optstk"],
            "raw_optstk_ord": ["optstk_ord"],
            "raw_optstk_unadjusted_spot": ["optstk", "optstk_unadjusted_spot"],
            "raw_optstk_oi": ["optstk", "optstk_unadjusted_spot"],
            "opt": [],
            "opt_ord": [],
            "futidx": [],
            "raw_opt": ["opt"],
            "raw_opt_ord": ["opt_ord"],
            "raw_futidx": ["opt", "opt_ord", "futidx"],
            "raw_opt_oi": ["opt", "opt_ord", "futidx"],
            "raw_cash": ["cash"],
            "cash": [],
            "raw_circuit": ["circuit"],
            "circuit": [],
            "fno": [],
            "raw_fut": ["fut", "fut_raw"],
            "fut": [],
            "fut_raw": [],
            "raw_futidx_fut": ["futidx_fut"],
            "futidx_fut": [],
            "raw_cm_bhav": ["cm_bhav"],
            "cm_bhav": [],
            "raw_futstk_bhav": ["futstk_bhav"],
            "futstk_bhav": [],
            "raw_futidx_bhav": ["futidx_bhav"],
            "futidx_bhav": [],
            "optstk_bhav": [],
            "raw_optstk_bhav": ["optstk_bhav"],
            "optidx_bhav": [],
            "raw_optidx_bhav": ["optidx_bhav"],
            "raw_futstk_oi": ["futstk_oi"],
            "futstk_oi": [],
            "raw_futidx_fut_oi": ["futidx_fut_oi"],
            "futidx_fut_oi": [],
            "raw_eq_ob": ["eq_ob"],
            "eq_ob": [],
            "raw_fut_ob": ["fut_ob"],
            "fut_ob": [],
            "raw_futstk_ob": ["fuststk_ob"],
            "futstk_ob": [],
        }
    )
    # TODO: either remove 0 named columns or handle the type anotations to handle Union[str,int]
    # adding type ignore for now as it will require a lot of changes across the repo
    COLUMNS_DICT: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "optstk": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "sym_id",
                "strike",
                "option_type",
                "expiry_date",
                "OI",
                "iv",
                "delta",
                "gamma",
                "theta",
                "vega",
            ],
            "optstk_unadjusted_spot": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "pcr",
            ],
            "raw_optstk": [
                "timestamp",
                "ID",
                "expiry",
                "option_type",
                "strike",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "raw_optstk_unadjusted_spot": [
                "timestamp",
                "ID",
                "expiry",
                "option_type",
                "strike",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "raw_optstk_oi": ["timestamp", "ID", "OI"],
            "opt": [
                "timestamp",
                "ID",
                "expiry",
                "option_type",
                "strike",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
                "Open_int",
                "iv",
                "delta",
                "gamma",
                "theta",
                "vega",
            ],
            "futidx": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
                "pcr",
            ],
            "raw_opt": [
                "timestamp",
                "ID",
                "expiry",
                "option_type",
                "strike",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
            ],
            "raw_futidx": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
            ],
            "raw_opt_oi": ["timestamp", "ID", "expiry", "option_type", "strike", "OI"],
            "raw_fut": [
                "timestamp",
                "ID",
                "expiry",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
            ],
            "fut": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
                "Next_Cons_Volume",
            ],
            "raw_futidx_fut": [
                "timestamp",
                "ID",
                "expiry",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
            ],
            "futidx_fut": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
                "Next_Cons_Volume",
            ],
            "raw_cash": [  ## copract applied or not
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
            ],
            "cash": [  ## copract applied or not
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
            ],
            "fno": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
            ],
            "eq": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
            ],
            "raw_futstk_oi": ["timestamp", "ID", "expiry", "OI"],
            "futstk_oi": ["timestamp", "ID", "near_month", "next_month", "far_month"],
            "futidx_oi": ["timestamp", "ID", "near_month", "next_month", "far_month"],
            "raw_futidx_fut_oi": ["timestamp", "ID", "expiry", "OI"],
            "raw_fo_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
            ],
            "raw_cm_bhav": [
                "date",
                "symbol",
                "series",
                "open",
                "high",
                "low",
                "close",
                "last",
                "prevclose",
                "tottrdqty",
                "tottrdval",
                "totaltrades",
                "isin",
            ],
            "cm_bhav": [
                "date",
                "symbol",
                "series",
                "open",
                "high",
                "low",
                "close",
                "last",
                "prevclose",
                "tottrdqty",
                "tottrdval",
                "totaltrades",
                "isin",
                "balte_id",
                "close_raw",
                "eod_price",
                "adj_factor",
            ],
            "futidx_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
                "balte_id",
                "expiry_rank",
                "close_raw",
                "adj_factor",
                "repo_rate",
                "eod_price",
            ],
            "raw_futstk_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
            ],
            "futstk_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
                "balte_id",
                "expiry_rank",
                "close_raw",
                "adj_factor",
                "repo_rate",
                "eod_price",
            ],
            "optidx_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
                "balte_id",
                "close_raw",
                "adj_factor",
                "expiry_rank",
                "eod_price",
            ],
            "optstk_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
                "balte_id",
                "close_raw",
                "adj_factor",
                "expiry_rank",
                "eod_price",
            ],
            "cd_fo_bhav": [
                "date",
                "contract_d",
                "previous_s",
                "open_price",
                "high_price",
                "low_price",
                "close_pric",
                "settlement",
                "net_change",
                "oi_no_con",
                "traded_qua",
                "trd_no_con",
                "traded_val",
                "segment",
                "symbol",
                "expiry",
                "balte_id",
                "expiry_rank",
                "eod_price",
            ],
            "cd_op_bhav": [
                "date",
                "contract_d",
                "previous_s",
                "open_price",
                "high_price",
                "low_price",
                "close_pric",
                "settlement",
                "net_change",
                "oi_no_con",
                "traded_qua",
                "trd_no_con",
                "undrlng_st",
                "notional_v",
                "premium_tr",
                "segment",
                "symbol",
                "expiry",
                "option_typ",
                "strike",
                "balte_id",
                "eod_price",
            ],
            "index_bhav": [
                "date",
                "symbol",
                "pb",
                "divyield",
                "turnover",
                "open",
                "pe",
                "volume",
                "low",
                "high",
                "close",
            ],
            "raw_futidx_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
            ],
            "raw_optstk_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
            ],
            "raw_optidx_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
            ],
            "raw_circuit": [
                "date",
                "symbol",
                "upper_circuit",
                "lower_circuit",
            ],
            "circuit": [
                "date",
                "ID",
                "upper_circuit",
                "lower_circuit",
            ],
            "corpact": [
                "exdate",
                "ID",
                "adj_factor",
            ],
            "demerger_merger": [
                "exdate",
                "Symbol",
                "ID",
            ],
            "isfno": [
                "date",
                "ID",
                "0",
            ],
            "isliquid": [
                "date",
                "ID",
                "0",
            ],
            "isnifty": [
                "date",
                "ID",
                "0",
            ],
            "symbol_change": [
                "date",
                "symbol",
                "current",
            ],
            "fut_raw": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
            ],
            "client_oi": [
                "date",
                "segment_type",
                "Client",
                "DII",
                "FII",
                "Pro",
                "Total",
            ],
            "dii": [
                "date",
                "fii_grosspurchase",
                "fii_grosssales",
                "fii_net",
                "dii_grosspurchase",
                "dii_grosssales",
                "dii_net",
            ],
            "fii": [
                "date",
                "segment_type",
                "buy_contract",
                "buy_value",
                "sell_contract",
                "sell_value",
                "oi_contract",
                "oi_value",
            ],
            "lotsize_index": [
                "date",
                "ID",
                "near_week",
                "near_month",
                "next_week",
                "next_month",
            ],
            "opt_margin": [
                "date",
                "ID",
                "contract_price",
                "R1",
                "R2",
                "R3",
                "R4",
                "R5",
                "R6",
                "R7",
                "R8",
                "R9",
                "R10",
                "R11",
                "R12",
                "R13",
                "R14",
                "R15",
                "R16",
                "delta",
            ],
            "bmdate_forward": [
                "date",
                "ID",
                "actual_date",
            ],
            "bmdate": [
                "date",
                "ID",
                "actual_date",
            ],
            "cash_delivery": [
                "date",
                "ID",
                0,  # type: ignore
            ],
            "cash_margin": [
                "date",
                "ID",
                0,  # type: ignore
            ],
            "cmvolt": [
                "date",
                "ID",
                0,  # type: ignore
            ],
            "corporate_action": [
                "exdate",
                "type",
                "symbol",
                "ID",
            ],
            "fno_sector": [
                "date",
                "ID",
                0,  # type: ignore
            ],
            "fut_margin": [
                "date",
                "ID",
                "span",
                "exposure",
            ],
            "isbanknifty": [
                "date",
                "ID",
                "0",
            ],
            "iseq": [
                "date",
                "ID",
                "0",
            ],
            "isht": [
                "date",
                "ID",
                "0",
            ],
            "isliquid_optstk": [
                "date",
                "ID",
                "0",
            ],
            "lotsize_near": [
                "date",
                "ID",
                "0",
            ],
            "lotsize_stk": [
                "date",
                "ID",
                "near_month",
                "next_month",
            ],
            "lotval_far": [
                "date",
                "ID",
                0,  # type: ignore
            ],
            "lotval_near": [
                "date",
                "ID",
                0,  # type: ignore
            ],
            "lotval_next": [
                "date",
                "ID",
                0,  # type: ignore
            ],
            "low_liquid_fno": [
                "date",
                "ID",
                "0",
            ],
            "mwpl": [
                "date",
                "ID",
                "0",
            ],
            "sector_wts": [
                "date",
                "sector",
                "ID",
                "Close_wgts",
                "vol_wgts",
            ],
            "repo_rate": [
                "date",
                "repo_rate",
            ],
            "raw_eq_ob": [
                "timestamp",
                "ID",
                "side",
                "level",
                "price",
                "quantity",
                "queue_depth",
            ],
            "eq_ob": [
                "timestamp",
                "ID",
                "side",
                "level",
                "price",
                "quantity",
                "queue_depth",
            ],
            "raw_futstk_ob": [
                "timestamp",
                "ID",
                "expiry",
                "side",
                "level",
                "price",
                "quantity",
                "queue_depth",
            ],
            "futstk_ob": [
                "timestamp",
                "ID",
                "side",
                "level",
                "price",
                "quantity",
                "queue_depth",
            ],
            "raw_fut_ob": [
                "timestamp",
                "ID",
                "expiry",
                "side",
                "level",
                "price",
                "quantity",
                "queue_depth",
            ],
            "fut_ob": [
                "timestamp",
                "ID",
                "side",
                "level",
                "price",
                "quantity",
                "queue_depth",
            ],
        }
    )

    UNIVERSE_TO_DATA_TYPE_DICT_UPDATE_DICT: Dict[
        str, Dict[Union[str, int], str]
    ] = field(
        default_factory=lambda: {
            "raw_optstk_unadjusted_spot": {"ID": "object"},
            "raw_optstk": {"ID": "object"},
            "raw_optstk_oi": {"ID": "object"},
            "raw_opt": {"ID": "object"},
            "raw_opt_oi": {"ID": "object"},
            "raw_fut": {"ID": "object"},
            "raw_cash": {"ID": "object"},
            "raw_futstk_oi": {"ID": "object"},
            "raw_futidx_oi": {"ID": "object"},
            "fno_sector": {0: "object"},
            "futstk_oi": {
                "near_month": "float64",
                "next_month": "float64",
                "far_month": "float64",
            },
            "futidx_oi": {
                "near_month": "float64",
                "next_month": "float64",
                "far_month": "float64",
            },
            "isfno": {"0": "bool"},
            "isbanknifty": {"0": "bool"},
            "iseq": {"0": "bool"},
            "isht": {"0": "bool"},
            "isliquid": {"0": "bool"},
            "isliquid_optstk": {"0": "bool"},
            "isnifty": {"0": "bool"},
            "low_liquid_fno": {"0": "bool"},
            "mwpl": {"0": "bool"},
        }
    )

    UNIVERSE_DTYPE_TO_COLUMN_DICT: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_optstk_ord": [
                "timestamp",
                "ID",
                "expiry",
                "option_type",
                "strike",
                "Open",
                "High",
                "Low",
                "Close",
            ],
            "raw_opt_ord": [
                "timestamp",
                "ID",
                "expiry",
                "option_type",
                "strike",
                "Open",
                "High",
                "Low",
                "Close",
            ],
            "opt_ord": [
                "timestamp",
                "ID",
                "expiry",
                "option_type",
                "strike",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "Open_int",
                "iv",
                "delta",
                "gamma",
                "theta",
                "vega",
            ],
        }
    )

    UNIVERSE_TO_AFTER_MARKET_SYMBOL_LIST: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "cash": ["corpact"],
            "circuit": ["corpact"],
            "fno": ["isfno"],
            "eq": ["isliquid"],
            "fut": [
                "corpact",
                "isfno",
                "repo_rate",
            ],  # we need to add here support of corpact_till_expiry
            "fut_raw": ["isfno"],
            "optstk_unadjusted_spot": ["isfno"],
            "cm_bhav": ["corpact"],
            "futstk_bhav": [
                "corpact",
                "repo_rate",
            ],
            "futidx_bhav": [
                "repo_rate",
            ],
            "futstk_oi": ["corpact", "lotsize_stk"],
        }
    )

    UNIVERSE_TO_MINIO_FILES_LIST: Dict[str, Dict[str, str]] = field(
        default_factory=lambda: {
            "fut": {"expiry_dict": "OPTSTK_EXPIRY_DICT"},
            "futidx_fut": {
                "nifty_expiry_dict": "NIFTY_EXPIRY_DICT",
                "banknifty_expiry_dict": "BANKNIFTY_EXPIRY_DICT",
                "finnifty_expiry_dict": "FINNIFTY_EXPIRY_DICT",
                "niftynxt50_expiry_dict": "NIFTYNXT50_EXPIRY_DICT",
                "midcpnifty_expiry_dict": "MIDCPNIFTY_EXPIRY_DICT",
            },
            "fut_raw": {"expiry_dict": "OPTSTK_EXPIRY_DICT"},
            "futstk_bhav": {"expiry_dict": "OPTSTK_EXPIRY_DICT"},
            "futidx_bhav": {
                "nifty_expiry_dict": "NIFTY_EXPIRY_DICT",
                "banknifty_expiry_dict": "BANKNIFTY_EXPIRY_DICT",
                "finnifty_expiry_dict": "FINNIFTY_EXPIRY_DICT",
                "niftynxt50_expiry_dict": "NIFTYNXT50_EXPIRY_DICT",
                "midcpnifty_expiry_dict": "MIDCPNIFTY_EXPIRY_DICT",
            },
            "optstk_bhav": {"expiry_dict": "OPTSTK_EXPIRY_DICT"},
            "optidx_bhav": {
                "nifty_expiry_dict": "NIFTY_EXPIRY_DICT",
                "banknifty_expiry_dict": "BANKNIFTY_EXPIRY_DICT",
                "finnifty_expiry_dict": "FINNIFTY_EXPIRY_DICT",
                "niftynxt50_expiry_dict": "NIFTYNXT50_EXPIRY_DICT",
                "midcpnifty_expiry_dict": "MIDCPNIFTY_EXPIRY_DICT",
            },
            "futstk_oi": {"expiry_dict": "OPTSTK_EXPIRY_DICT"},
            "futidx_fut_oi": {
                "nifty_expiry_dict": "NIFTY_EXPIRY_DICT",
                "banknifty_expiry_dict": "BANKNIFTY_EXPIRY_DICT",
                "finnifty_expiry_dict": "FINNIFTY_EXPIRY_DICT",
                "niftynxt50_expiry_dict": "NIFTYNXT50_EXPIRY_DICT",
                "midcpnifty_expiry_dict": "MIDCPNIFTY_EXPIRY_DICT",
            },
        }
    )

    UNIVERSE_TO_RENAME_COLUMN_DICT: Dict[str, Dict[Union[int, str], str]] = field(
        default_factory=lambda: {
            "corpact": {"date": "exdate", 0: "adj_factor"},
            "circuit": {0: "upper_circuit", 1: "lower_circuit"},
            "raw_cm_bhav": {"timestamp": "date"},
            "raw_futidx_bhav": {"timestamp": "date"},
            "raw_optstk_bhav": {"timestamp": "date"},
            "raw_optidx_bhav": {"timestamp": "date"},
            "raw_fo_bhav": {"timestamp": "date"},
            "raw_futstk_bhav": {"timestamp": "date"},
            "isfno": {0: "0"},
            "isliquid": {0: "0"},
            "iseq": {0: "0"},
            "isht": {0: "0"},
            "isnifty": {0: "0"},
            "mwpl": {0: "0"},
            "isliquid_optstk": {0: "0"},
            "isbanknifty": {0: "0"},
            "low_liquid_fno": {0: "0"},
        }
    )

    MODIFY_COLUMNS_OPERATIONS: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "cash": ["apply_corpact"],
            "circuit": ["apply_corpact"],
            "fut": [
                "apply_corpact",
                "apply_basis_adjustment",
            ],  # we need to add here support of apply_dividend_removal
            "cm_bhav": ["apply_corpact"],
            "futstk_bhav": ["apply_corpact"],
            "futstk_oi": [
                "restructure_oi",
                "apply_corpact",
                "round_to_nearest_lotsize",
            ],
            "futidx_fut_oi": ["restructure_oi"],
        }
    )

    FILTER_DATA_OPERATIONS: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "fno": ["filter_id_fno"],
            "fut": [
                "filter_id_fno",
                "filter_near_contract",
            ],
            "fut_raw": [
                "filter_id_fno",
                "filter_near_contract",
            ],
            "futidx_fut": ["filter_near_contract"],
            "optstk_unadjusted_spot": ["filter_id_fno"],
            "raw_cm_bhav": [
                "filter_series",
            ],  # Added the support for bhav as well...
        }
    )

    UNIVERSE_TO_IGNORE_CHECK_LIST: Dict[str, List[Check]] = field(
        default_factory=lambda: {
            "cash": [
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_ALL_DATES,
            ],
            "raw_cash": [
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_ALL_DATES,
            ],
            "optstk": [
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
            ],
            "raw_optstk": [
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
            ],
            "optstk_unadjusted_spot": [
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_ALL_DATES,
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
            ],
            "raw_optstk_unadjusted_spot": [
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_ALL_DATES,
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
            ],
            "opt": [
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
                Check.CHECK_ALL_TIMESTAMPS,
            ],
            "raw_opt": [
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
                Check.CHECK_ALL_TIMESTAMPS,
            ],
            "fut": [
                Check.CHECK_ALL_DATES,
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
            ],
            "raw_fut": [
                Check.CHECK_ALL_DATES,
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
            ],
            "futidx_fut": [
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_ALL_TIMESTAMPS,
            ],
            "raw_futidx_fut": [
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_ALL_TIMESTAMPS,
            ],
            "futidx": [
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
                Check.CHECK_ALL_TIMESTAMPS,
            ],
            "raw_futidx": [
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
            ],
            "fut_raw": [
                Check.CHECK_ALL_DATES,
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
            ],
            "futstk_oi": [
                Check.CHECK_ALL_DATES,
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_ALL_TIMESTAMPS,
            ],
            "futidx_oi": [
                Check.CHECK_ALL_DATES,
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_ALL_TIMESTAMPS,
            ],
        }
    )

    OPTION_LIST: List[str] = field(
        default_factory=lambda: [
            "optstk",
            "raw_optstk",
            "opt",
            "raw_opt",
            "raw_opt_oi",
            "raw_optstk_oi",
        ]
    )

    FUTURE_LIST: List[str] = field(
        default_factory=lambda: [
            "raw_fut",
            "fut",
            "raw_futidx_fut",
            "raw_fut_ob",
            "raw_futstk_ob",
            "futidx_fut",
            "fut_raw",
            "futstk_oi",
            "futidx_oi",
            "fut_ob",
            "futstk_ob",
        ]
    )

    SPOT_LIST: List[str] = field(
        default_factory=lambda: [
            "raw_cash",
            "optstk_unadjusted_spot",
            "raw_optstk_unadjusted_spot",
            "raw_futidx",
            "raw_eq_ob",
            "futidx",
            "cash",
            "raw_cash_1",
            "raw_futstk_oi",
            "raw_futidx_fut_oi",
            "raw_circuit",
            "circuit",
            "eq_ob",
        ]
    )

    OPTION_TO_UNDERLYING_MAP: Dict[str, str] = field(
        default_factory=lambda: {
            "optstk": "optstk_unadjusted_spot",
            "raw_optstk": "raw_optstk_unadjusted_spot",
            "opt": "futidx",
            "raw_opt": "raw_futidx",
        }
    )

    FUTURE_TO_UNDERLYING_MAP: Dict[str, str] = field(
        default_factory=lambda: {
            "raw_fut": "raw_optstk_unadjusted_spot",
            "fut": "optstk_unadjusted_spot",
            "raw_futidx_fut": "raw_futidx",
            "futidx_fut": "futidx",
            "futstk_oi": "raw_futstk_oi",
            "futidx_oi": "raw_futidx_fut_oi",
        }
    )

    UNDERLYING_TO_OPTION_MAP: Dict[str, str] = field(
        default_factory=lambda: {
            "optstk_unadjusted_spot": "optstk",
            "raw_optstk_unadjusted_spot": "optstk_unadjusted_spot",
            "futidx": "opt",
            "raw_futidx": "raw_opt",
        }
    )

    UNDERLYING_TO_FUTURE_MAP: Dict[str, str] = field(
        default_factory=lambda: {
            "raw_optstk_unadjusted_spot": "raw_fut",
            "optstk_unadjusted_spot": "fut",
            "raw_futidx": "raw_futidx_fut",
            "futidx": "futidx_fut",
            "raw_futstk_oi": "futstk_oi",
        }
    )

    LOCAL_FILE_LOCATION = "nse/compilation"

    INTEREST_RATE = 0.1
    EXPIRY_TIME = 55800  ## 15:30 in seconds

    SERIES_FILTER_MAP: Dict[str, List[str]] = field(
        default_factory=lambda: {"raw_cm_bhav": ["EQ"]}
    )

    AFTER_MARKET_DICT_MINIO: Dict[str, str] = field(
        default_factory=lambda: {
            "cm_bhav": "nse_daily_downloads/cm_bhav.csv",
            "futidx_bhav": "nse_daily_downloads/fo_bhav.csv",
            "futstk_bhav": "nse_daily_downloads/fo_bhav.csv",
            "optidx_bhav": "nse_daily_downloads/fo_bhav.csv",
            "optstk_bhav": "nse_daily_downloads/fo_bhav.csv",
            "fo_bhav": "nse_daily_downloads/fo_bhav.csv"
            # "cd_fo_bhav": "nse_daily_downloads/cd_fo_bhav.csv",
            # "cd_op_bhav": "nse_daily_downloads/cd_op_bhav.csv",
            # "index_bhav": "nse_daily_downloads/index_bhav.csv",
        }
    )

    AFTER_MARKET_DICT_GRPC: List[str] = field(
        default_factory=lambda: [
            "bmdate",
            "cash_delivery",
            "cash_margin",
            "client_oi",
            "cmvolt",
            "dii",
            "fii",
            "fno_sector",
            "fut_margin",
            "iseq",
            "isfno",
            "isht",
            "isliquid",
            "isnifty",
            "lotval_far",
            "lotval_near",
            "lotval_next",
            "mwpl",
            "opt_margin",
            "sector_wts",
            "low_liquid_fno",
            "corpact",
            "isliquid_optstk",
            "lotsize_stk",
            "lotsize_index",
            # "futstk_oi",
            # "futidx_oi",
            "circuit",
            "isbanknifty",
            "low_liquid_fno",
        ]
    )

    CORPACT_UNIVERSES: List[str] = field(
        default_factory=lambda: [
            "cash",
            "fut_raw",
            "fut",
            "eq",
            "fno",
            "futstk_oi",
        ]
    )

    CORPACT_LIBRARIES: List[str] = field(
        default_factory=lambda: [
            "nse/1_min/cash/trd",
            "nse/5_min/cash/trd",
            "nse/1_min/eq/column_bucket",
            "nse/5_min/eq/column_bucket",
            "nse/1_min/fno/column_bucket",
            "nse/5_min/fno/column_bucket",
            "nse/1_min/fut/trd",
            "nse/5_min/fut/trd",
            "nse/1_min/fut/column_bucket",
            "nse/5_min/fut/column_bucket",
            "nse/1_min/fut_raw/trd",
            "nse/5_min/fut_raw/trd",
            "nse/1_min/fut_raw/column_bucket",
            "nse/5_min/fut_raw/column_bucket",
            "nse/1_min/futstk_oi/trd",
            "nse/1_min/futstk_oi/column_bucket",
        ]
    )

    AFTER_MARKET_CORPACTS_UNIVERSES: List[str] = field(
        default_factory=lambda: [
            "futstk_bhav",
            "cm_bhav",
            "circuit",
        ]
    )

    SEGMENT_APPEND_LIST: List[Tuple[str, int, str]] = field(
        default_factory=lambda: [
            ("CASH", 1, "trd"),
            ("CASH", 1, "ord"),
            ("CASH", 5, "trd"),
            ("CASH", 5, "ord"),
            # ("CASH_OI", 1, "trd"),
            # ("CASH_OI", 5, "trd"),
            # ("NF", 1, "trd"),
            # ("NF", 1, "ord"),
            # ("NF", 5, "trd"),
            # ("NF", 5, "ord"),
            # ("NF_OI", 1, "trd"),
            # ("NF_OI", 5, "trd"),
        ]
    )

    UNIVERSE_TO_LOTSIZE_DICT: Dict[str, str] = field(
        default_factory=lambda: {"futstk_oi": "lotsize_stk"}
    )
