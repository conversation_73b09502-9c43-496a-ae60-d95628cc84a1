## For exchange specific info
import pytz
from main.config.config_base import ConfigBase
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Union
import datetime

from main.enums import ExchangeType, Check


@dataclass
class Session:
    STARTING_TIMESTAMP: datetime.time
    ENDING_TIMESTAMP: datetime.time


@dataclass
class ConfigKRX(ConfigBase):
    EXCHANGE = "krx"
    EXCHANGE_ENUM = ExchangeType.KRX
    FILE_STORAGE = ""
    DB_STORAGE = ""
    FILE_DICT: Dict[str, Tuple[str, str]] = field(
        default_factory=lambda: {
            "ALL_DATES": ("international", "all_dates/ALL_DATES_KRX.npy"),
            "MAPPING_DICT": ("commondata", "balte_uploads/mapping_dict"),
            "KOSPI_EXPIRY_DICT": ("international", "expiry_dict/kospi_expiry_dict"),
            "KOSPI_FUT_EXPIRY_DICT": (
                "international",
                "expiry_dict/kospi_fut_expiry_dict",
            ),
        }
    )

    MARKET_HOURS_DICT: Dict[str, Dict[str, str]] = field(
        default_factory=lambda: {
            "raw_optidx": {"open": "9:00", "close": "15:30"},
            "raw_futidx": {"open": "9:00", "close": "15:30"},
            "raw_futidx_fut": {"open": "8:45", "close": "15:35"},
            "optidx": {"open": "9:00", "close": "15:30"},
            "futidx": {"open": "9:00", "close": "15:30"},
            "futidx_fut": {"open": "8:45", "close": "15:35"},
        }
    )

    PRE_DEPENDENCIES: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "optidx": ["raw_optidx", "raw_futidx"],
            "futidx": ["raw_futidx"],
            "futidx_fut": ["raw_futidx_fut"],
            "raw_futidx": [],
            "raw_optidx": [],
            "raw_futidx_fut": [],
        }
    )

    POST_DEPENDENCIES: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "optidx": [],
            "futidx": [],
            "raw_futidx": ["optidx", "futidx"],
            "raw_optidx": ["optidx"],
            "raw_futidx_fut": ["futidx_fut"],
            "futidx_fut": [],
        }
    )

    COLUMNS_DICT: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_optidx": [
                "timestamp",
                "ID",
                "expiry",
                "option_type",
                "strike",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "raw_futidx": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "raw_futidx_fut": [
                "timestamp",
                "ID",
                "expiry",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "Next_Close",
                "Next_Cons_Volume",
            ],
            "optidx": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "iv",
                "delta",
                "gamma",
                "theta",
                "vega",
            ],
            "futidx": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "futidx_fut": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
        }
    )
    UNIVERSE_TO_DATA_TYPE_DICT_UPDATE_DICT: Dict[
        str, Dict[Union[str, int], str]
    ] = field(
        default_factory=lambda: {
            "raw_futidx": {"ID": "object", "expiry": "uint64"},
            "raw_futidx_fut": {"ID": "object", "expiry": "uint64"},
            "raw_optidx": {"ID": "object", "expiry": "uint64"},
        }
    )
    UNIVERSE_TO_MINIO_FILES_LIST: Dict[str, Dict[str, str]] = field(
        default_factory=lambda: {
            "futidx_fut": {"expiry_dict": "KOSPI_FUT_EXPIRY_DICT"},
            "raw_futidx_fut": {"expiry_dict": "KOSPI_FUT_EXPIRY_DICT"},
            "raw_optidx": {"expiry_dict": "KOSPI_EXPIRY_DICT"},
            "optidx": {"expiry_dict": "KOSPI_EXPIRY_DICT"},
        }
    )
    UNIVERSE_TO_AFTER_MARKET_SYMBOL_LIST: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "futidx_fut": [
                "repo_rate",
            ],
        }
    )

    MODIFY_COLUMNS_OPERATIONS: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "futidx_fut": ["apply_basis_adjustment"],
        }
    )
    FILTER_DATA_OPERATIONS: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_futidx": ["apply_symbol_change", "apply_demerger_merger"],
            "futidx_fut": ["filter_near_contract"],
            "raw_optidx": ["apply_symbol_change", "apply_demerger_merger"],
            "raw_futidx_fut": ["apply_symbol_change", "apply_demerger_merger"],
        }
    )

    OPTION_LIST: List[str] = field(
        default_factory=lambda: [
            "raw_optidx",
            "optidx",
        ]
    )

    FUTURE_LIST: List[str] = field(
        default_factory=lambda: [
            "raw_futidx_fut",
            "futidx_fut",
        ]
    )

    SPOT_LIST: List[str] = field(
        default_factory=lambda: [
            "raw_futidx",
            "futidx",
        ]
    )

    OPTION_TO_UNDERLYING_MAP: Dict[str, str] = field(
        default_factory=lambda: {"optidx": "futidx"}
    )

    UNDERLYING_TO_OPTION_MAP: Dict[str, str] = field(
        default_factory=lambda: {
            "futidx": "optidx",
        }
    )

    UNIVERSE_TO_BALTE_UNIVERSE_MAPPING: Dict[str, str] = field(
        default_factory=lambda: {
            "optidx": "optidx_krx",
            "futidx": "futidx_krx",
            "futidx_fut": "futidx_fut_krx",
            # "raw_futidx": "raw_futidx_krx",
            # "raw_futidx_fut": "raw_futidx_fut_krx",
            # "raw_optidx": "raw_optidx_krx",
        }
    )

    UNIVERSE_TO_IGNORE_CHECK_LIST: Dict[str, List[Check]] = field(
        default_factory=lambda: {
            "futidx": [
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
                Check.CHECK_NAN_ENTRIES,
            ],
            "optidx": [
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
                Check.CHECK_NAN_ENTRIES,
            ],
            "futidx_fut": [
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
                Check.CHECK_NAN_ENTRIES,
            ],
        }
    )

    LOCAL_FILE_LOCATION = "krx/compilation"

    INTEREST_RATE = 0.05
    EXPIRY_TIME = 55200  ## 15:20 in seconds
    TIMEZONE = pytz.timezone("Asia/Seoul")

    # ALLOWED_OHLC_ACROSS_ROW_AVERAGE_MULTIPLIER = 30
    # ALLOWED_OHLC_INTRADAY_AVERAGE_MULTIPLIER = 30
    # ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER = 30
    # DATA_JUMPS_THRESHOLD = 0.1
