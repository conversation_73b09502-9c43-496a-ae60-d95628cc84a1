"""
Storage class for accessing files required in the compilation process.

This implementation uses Minio as the backend storage system. It provides
functionality to read files from Minio buckets, with special handling for
different file types like numpy arrays and pickled dictionaries.

Note:
    Many methods in this class are not implemented yet and will raise
    NotImplementedError when called.

Examples:
    ```python
    # Create a FileStore instance
    file_store = FileStore()

    # Read data from a file
    data = file_store.read(
        file_location="my-bucket",
        file_name="data_ALL_DATES.npy"
    )
    ```
"""

import pickle
from main.storage.config import MINIO_ACCESS_KEY, MINIO_END_POINT, MINIO_SECRET_KEY
from main.storage.storage_base import StoreBase
from typing import Any, List, Optional, Dict, Union
import pandas as pd
from minio import Minio
from io import BytesIO
import numpy as np


class FileStore(StoreBase):
    """
    Implementation of StoreBase for file storage using Minio.

    This class provides methods to interact with files stored in Minio buckets.
    Currently, only the read method is fully implemented, while other methods
    will raise NotImplementedError.
    """

    def __init__(self) -> None:
        """
        Initialize the FileStore with Minio connection.

        Connects to the Minio server using credentials from the config file.
        """
        super().__init__()
        self.__store = Minio(
            MINIO_END_POINT, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, secure=False
        )

    def list_file_locations(self) -> List[str]:
        """
        List all available buckets in Minio.

        This method is not implemented yet.

        Returns:
            List[str]: A list of all bucket names

        Raises:
            NotImplementedError: This method is not yet implemented
        """
        raise NotImplementedError()

    def list_file_names(self, file_location: str) -> List[str]:
        """
        List all objects within a specific Minio bucket.

        This method is not implemented yet.

        Args:
            file_location (str): The bucket name to list objects from

        Returns:
            List[str]: A list of all object names in the specified bucket

        Raises:
            NotImplementedError: This method is not yet implemented
        """
        raise NotImplementedError()

    def read(
        self,
        file_location: str,
        file_name: str,
        start_date: Optional[pd.Timestamp] = None,
        end_date: Optional[pd.Timestamp] = None,
    ) -> Any:
        """
        Read data from Minio storage at the specified bucket and object.

        This method supports special handling for different file types:
        - Files with "ALL_DATES" in the name are treated as numpy arrays
        - Files with "dict" in the name are treated as pickled DataFrames

        Args:
            file_location (str): The bucket name to read from
            file_name (str): The object name to read
            start_date (Optional[pd.Timestamp], optional): Start date for filtering data (not used). Defaults to None.
            end_date (Optional[pd.Timestamp], optional): End date for filtering data (not used). Defaults to None.

        Returns:
            Any: The data read from storage

        Raises:
            Exception: If there's an error retrieving the object from Minio

        Examples:
            ```python
            # Read a numpy array file
            dates_df = file_store.read("my-bucket", "ALL_DATES.npy")

            # Read a pickled dictionary file
            dict_df = file_store.read("my-bucket", "data_dict.pkl")
            ```
        """
        try:
            data = self.__store.get_object(file_location, file_name).data
        except Exception:
            raise Exception(f"Error in getting {file_name} from {file_location}")

        if "ALL_DATES" in file_name:
            data = np.load(BytesIO(data), allow_pickle=True)
            return data
        elif "dict" in file_name:
            return pickle.loads(data)

        return data

    def append(
        self,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> int:
        """
        Append data to an existing object in Minio.

        This method is not implemented yet.

        Args:
            file_location (str): The bucket name where the object exists
            file_name (str): The object name to append to
            data (pd.DataFrame): The data to append
            metadata (Dict[str, Union[str, Dict[str, float]]]): Metadata associated with the data

        Returns:
            int: Version number or identifier for the operation

        Raises:
            NotImplementedError: This method is not yet implemented
        """
        raise NotImplementedError()

    def update(
        self,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> int:
        """
        Update an existing object in Minio.

        This method is not implemented yet.

        Args:
            file_location (str): The bucket name where the object exists
            file_name (str): The object name to update
            data (pd.DataFrame): The new data to update with
            metadata (Dict[str, Union[str, Dict[str, float]]]): Metadata associated with the data

        Returns:
            int: Version number or identifier for the operation

        Raises:
            NotImplementedError: This method is not yet implemented
        """
        raise NotImplementedError()

    def write(
        self,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> int:
        """
        Write data to Minio (creates or overwrites an object).

        This method is not implemented yet.

        Args:
            file_location (str): The bucket name to write to
            file_name (str): The object name to write
            data (pd.DataFrame): The data to write
            metadata (Dict[str, Union[str, Dict[str, float]]]): Metadata associated with the data

        Returns:
            int: Version number or identifier for the operation

        Raises:
            NotImplementedError: This method is not yet implemented
        """
        raise NotImplementedError()

    def read_metadata(
        self, file_location: str, file_name: str
    ) -> Dict[str, Union[str, Dict[str, float]]]:
        """
        Read metadata for a specific object in Minio.

        This method is not implemented yet.

        Args:
            file_location (str): The bucket name where the object exists
            file_name (str): The object name to read metadata from

        Returns:
            Dict[str, Union[str, Dict[str, float]]]: The metadata associated with the object

        Raises:
            NotImplementedError: This method is not yet implemented
        """
        raise NotImplementedError()

    def write_metadata(
        self,
        file_location: str,
        file_name: str,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> bool:
        """
        Write metadata for a specific object in Minio.

        This method is not implemented yet.

        Args:
            file_location (str): The bucket name where the object exists
            file_name (str): The object name to write metadata for
            metadata (Dict[str, Union[str, Dict[str, float]]]): The metadata to write

        Returns:
            bool: True if the operation was successful, False otherwise

        Raises:
            NotImplementedError: This method is not yet implemented
        """
        raise NotImplementedError()
