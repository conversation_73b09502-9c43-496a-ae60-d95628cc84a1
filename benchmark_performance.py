#!/usr/bin/env python3
"""
Benchmark script to compare performance between original Python implementation
and Rust-optimized version.
"""

import time
import pandas as pd
import numpy as np
import sys
import os
from typing import Dict, Tuple

# Add paths
sys.path.append('/home/<USER>/repos/data_auditing/rust_data_processor')

# Import both implementations
try:
    from python_wrapper import (
        process_chunk_data as rust_process_chunk,
        resample_futures_trade as rust_resample_fut_trd,
        resample_futures_order as rust_resample_fut_ord,
        resample_options_trade as rust_resample_opt_trd,
        RUST_AVAILABLE
    )
except ImportError:
    RUST_AVAILABLE = False
    print("Rust implementation not available")

def generate_test_data(n_rows: int = 100000) -> pd.DataFrame:
    """Generate synthetic test data similar to the real financial data."""
    np.random.seed(42)
    
    # Generate timestamps (1 second intervals)
    start_time = pd.Timestamp('2024-01-01 09:15:00')
    timestamps = pd.date_range(start_time, periods=n_rows, freq='1S')
    
    # Generate symbols and expiries
    symbols = np.random.choice(['NIFTY', 'BANKNIFTY', 'RELIANCE', 'TCS'], n_rows)
    expiries = np.random.choice(['2024-01-25', '2024-02-29', '2024-03-28'], n_rows)
    
    # Generate strike prices (NaN for futures, values for options)
    is_future = np.random.choice([True, False], n_rows, p=[0.3, 0.7])
    strike_prices = np.where(is_future, np.nan, 
                            np.random.choice([18000, 18500, 19000, 19500, 20000], n_rows))
    
    # Generate option types
    option_types = np.where(is_future, np.nan,
                           np.random.choice(['CE', 'PE'], n_rows))
    
    # Generate prices and volumes
    close_prices = np.where(np.random.random(n_rows) > 0.1,  # 90% have close prices
                           np.random.uniform(100, 20000, n_rows), 0)
    ord_prices = np.where(np.random.random(n_rows) > 0.1,   # 90% have order prices
                         np.random.uniform(100, 20000, n_rows), 0)
    volumes = np.random.uniform(1, 1000, n_rows)
    
    df = pd.DataFrame({
        'timestamp': timestamps,
        'symbol': symbols,
        'expiry': expiries,
        'strike_price': strike_prices,
        'option_type': option_types,
        'close': close_prices,
        'ord_price': ord_prices,
        'volume': volumes
    })
    
    df.set_index('timestamp', inplace=True)
    return df

def benchmark_chunk_processing(df: pd.DataFrame) -> Dict[str, float]:
    """Benchmark chunk data processing."""
    results = {}
    
    # Python implementation
    start_time = time.time()
    df_fut = df[df.strike_price.isna()].drop(columns=["strike_price", "option_type"])
    df_opt = df[~df.strike_price.isna()]
    
    python_result = {
        'fut_trd': df_fut[df_fut.close != 0].drop(columns=["ord_price"]),
        'fut_ord': df_fut[df_fut.ord_price != 0].drop(columns=["close", "volume"]),
        'opt_trd': df_opt[df_opt.close != 0].drop(columns=["ord_price"]),
        'opt_ord': df_opt[df_opt.ord_price != 0].drop(columns=["close", "volume"])
    }
    python_time = time.time() - start_time
    results['python_chunk_processing'] = python_time
    
    # Rust implementation
    if RUST_AVAILABLE:
        start_time = time.time()
        rust_result = rust_process_chunk(df)
        rust_time = time.time() - start_time
        results['rust_chunk_processing'] = rust_time
        results['chunk_speedup'] = python_time / rust_time if rust_time > 0 else float('inf')
        
        # Verify results are similar
        for key in python_result:
            if not python_result[key].empty and not rust_result[key].empty:
                assert len(python_result[key]) == len(rust_result[key]), f"Length mismatch for {key}"
    
    return results

def benchmark_futures_trade_resampling(df_fut_trd: pd.DataFrame) -> Dict[str, float]:
    """Benchmark futures trade resampling."""
    if df_fut_trd.empty:
        return {}
    
    results = {}
    
    # Python implementation
    start_time = time.time()
    python_result = (
        df_fut_trd.groupby(["symbol", "expiry"])
        .resample("1min", label="right", closed="right")
        .agg({"close": ["first", "max", "min", "last"], "volume": "sum"})
        .dropna()
    )
    python_time = time.time() - start_time
    results['python_fut_trd_resample'] = python_time
    
    # Rust implementation
    if RUST_AVAILABLE:
        start_time = time.time()
        rust_result = rust_resample_fut_trd(df_fut_trd, interval_minutes=1)
        rust_time = time.time() - start_time
        results['rust_fut_trd_resample'] = rust_time
        results['fut_trd_speedup'] = python_time / rust_time if rust_time > 0 else float('inf')
    
    return results

def benchmark_futures_order_resampling(df_fut_ord: pd.DataFrame) -> Dict[str, float]:
    """Benchmark futures order resampling."""
    if df_fut_ord.empty:
        return {}
    
    results = {}
    
    # Python implementation
    start_time = time.time()
    python_result = (
        df_fut_ord.groupby(["symbol", "expiry"])
        .resample("1min", label="right", closed="right")
        .agg({"ord_price": ["first", "max", "min", "last"]})
        .dropna()
    )
    python_time = time.time() - start_time
    results['python_fut_ord_resample'] = python_time
    
    # Rust implementation
    if RUST_AVAILABLE:
        start_time = time.time()
        rust_result = rust_resample_fut_ord(df_fut_ord, interval_minutes=1)
        rust_time = time.time() - start_time
        results['rust_fut_ord_resample'] = rust_time
        results['fut_ord_speedup'] = python_time / rust_time if rust_time > 0 else float('inf')
    
    return results

def benchmark_options_trade_resampling(df_opt_trd: pd.DataFrame) -> Dict[str, float]:
    """Benchmark options trade resampling."""
    if df_opt_trd.empty:
        return {}
    
    results = {}
    
    # Python implementation
    start_time = time.time()
    python_result = (
        df_opt_trd.groupby(["symbol", "expiry", "strike_price", "option_type"])
        .resample("1min", label="right", closed="right")
        .agg({"close": ["first", "max", "min", "last"]})
        .dropna()
    )
    python_time = time.time() - start_time
    results['python_opt_trd_resample'] = python_time
    
    # Rust implementation
    if RUST_AVAILABLE:
        start_time = time.time()
        rust_result = rust_resample_opt_trd(df_opt_trd, interval_minutes=1)
        rust_time = time.time() - start_time
        results['rust_opt_trd_resample'] = rust_time
        results['opt_trd_speedup'] = python_time / rust_time if rust_time > 0 else float('inf')
    
    return results

def run_comprehensive_benchmark(data_sizes: list = [10000, 50000, 100000, 500000]):
    """Run comprehensive benchmark across different data sizes."""
    print("=" * 80)
    print("RUST DATA PROCESSOR PERFORMANCE BENCHMARK")
    print("=" * 80)
    print(f"Rust available: {RUST_AVAILABLE}")
    print()
    
    all_results = {}
    
    for size in data_sizes:
        print(f"Testing with {size:,} rows...")
        print("-" * 40)
        
        # Generate test data
        df = generate_test_data(size)
        
        # Benchmark chunk processing
        chunk_results = benchmark_chunk_processing(df)
        
        # Get processed data for further benchmarking
        if RUST_AVAILABLE:
            processed = rust_process_chunk(df)
        else:
            df_fut = df[df.strike_price.isna()].drop(columns=["strike_price", "option_type"])
            df_opt = df[~df.strike_price.isna()]
            processed = {
                'fut_trd': df_fut[df_fut.close != 0].drop(columns=["ord_price"]),
                'fut_ord': df_fut[df_fut.ord_price != 0].drop(columns=["close", "volume"]),
                'opt_trd': df_opt[df_opt.close != 0].drop(columns=["ord_price"]),
                'opt_ord': df_opt[df_opt.ord_price != 0].drop(columns=["close", "volume"])
            }
        
        # Benchmark individual operations
        fut_trd_results = benchmark_futures_trade_resampling(processed['fut_trd'])
        fut_ord_results = benchmark_futures_order_resampling(processed['fut_ord'])
        opt_trd_results = benchmark_options_trade_resampling(processed['opt_trd'])
        
        # Combine results
        size_results = {**chunk_results, **fut_trd_results, **fut_ord_results, **opt_trd_results}
        all_results[size] = size_results
        
        # Print results for this size
        print(f"Chunk processing: {chunk_results.get('python_chunk_processing', 0):.4f}s (Python)")
        if RUST_AVAILABLE:
            print(f"                  {chunk_results.get('rust_chunk_processing', 0):.4f}s (Rust)")
            print(f"                  {chunk_results.get('chunk_speedup', 0):.2f}x speedup")
        
        print(f"Futures trade:    {fut_trd_results.get('python_fut_trd_resample', 0):.4f}s (Python)")
        if RUST_AVAILABLE:
            print(f"                  {fut_trd_results.get('rust_fut_trd_resample', 0):.4f}s (Rust)")
            print(f"                  {fut_trd_results.get('fut_trd_speedup', 0):.2f}x speedup")
        
        print(f"Options trade:    {opt_trd_results.get('python_opt_trd_resample', 0):.4f}s (Python)")
        if RUST_AVAILABLE:
            print(f"                  {opt_trd_results.get('rust_opt_trd_resample', 0):.4f}s (Rust)")
            print(f"                  {opt_trd_results.get('opt_trd_speedup', 0):.2f}x speedup")
        
        print()
    
    # Summary
    print("=" * 80)
    print("SUMMARY")
    print("=" * 80)
    
    if RUST_AVAILABLE:
        avg_chunk_speedup = np.mean([results.get('chunk_speedup', 1) for results in all_results.values()])
        avg_fut_trd_speedup = np.mean([results.get('fut_trd_speedup', 1) for results in all_results.values() if 'fut_trd_speedup' in results])
        avg_opt_trd_speedup = np.mean([results.get('opt_trd_speedup', 1) for results in all_results.values() if 'opt_trd_speedup' in results])
        
        print(f"Average speedups:")
        print(f"  Chunk processing: {avg_chunk_speedup:.2f}x")
        print(f"  Futures trade:    {avg_fut_trd_speedup:.2f}x")
        print(f"  Options trade:    {avg_opt_trd_speedup:.2f}x")
        print()
        print("Rust optimization provides significant performance improvements!")
    else:
        print("Rust optimization not available. Consider building the extension for better performance.")
    
    return all_results

if __name__ == "__main__":
    # Run the benchmark
    results = run_comprehensive_benchmark()
    
    print("\nBenchmark completed!")
    if RUST_AVAILABLE:
        print("To use the optimized version in your code:")
        print("  from sample_snap_tick_data_optimized import process_snap_file_optimized")
        print("  process_snap_file_optimized('SYMBOL_NAME')")
    else:
        print("To enable Rust optimization:")
        print("  cd rust_data_processor")
        print("  python build.py")
